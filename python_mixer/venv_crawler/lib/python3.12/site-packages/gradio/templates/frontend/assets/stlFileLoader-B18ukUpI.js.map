{"version": 3, "file": "stlFileLoader-B18ukUpI.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+loaders@8.2.0_@babylonjs+core@8.2.0_babylonjs-gltf2interface@7.25.2/node_modules/@babylonjs/loaders/STL/stlFileLoader.js"], "sourcesContent": ["import { Tools } from \"@babylonjs/core/Misc/tools.js\";\nimport { VertexBuffer } from \"@babylonjs/core/Buffers/buffer.js\";\nimport { Mesh } from \"@babylonjs/core/Meshes/mesh.js\";\nimport { RegisterSceneLoaderPlugin } from \"@babylonjs/core/Loading/sceneLoader.js\";\nimport { AssetContainer } from \"@babylonjs/core/assetContainer.js\";\nimport { STLFileLoaderMetadata } from \"./stlFileLoader.metadata.js\";\nimport \"@babylonjs/core/Materials/standardMaterial.js\";\n/**\n * STL file type loader.\n * This is a babylon scene loader plugin.\n */\nexport class STLFileLoader {\n    constructor() {\n        /** @internal */\n        this.solidPattern = /solid (\\S*)([\\S\\s]*?)endsolid[ ]*(\\S*)/g;\n        /** @internal */\n        this.facetsPattern = /facet([\\s\\S]*?)endfacet/g;\n        /** @internal */\n        this.normalPattern = /normal[\\s]+([-+]?[0-9]+\\.?[0-9]*([eE][-+]?[0-9]+)?)+[\\s]+([-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?)+[\\s]+([-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?)+/g;\n        /** @internal */\n        this.vertexPattern = /vertex[\\s]+([-+]?[0-9]+\\.?[0-9]*([eE][-+]?[0-9]+)?)+[\\s]+([-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?)+[\\s]+([-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?)+/g;\n        /**\n         * Defines the name of the plugin.\n         */\n        this.name = STLFileLoaderMetadata.name;\n        /**\n         * Defines the extensions the stl loader is able to load.\n         * force data to come in as an ArrayBuffer\n         * we'll convert to string if it looks like it's an ASCII .stl\n         */\n        this.extensions = STLFileLoaderMetadata.extensions;\n    }\n    /**\n     * Import meshes into a scene.\n     * @param meshesNames An array of mesh names, a single mesh name, or empty string for all meshes that filter what meshes are imported\n     * @param scene The scene to import into\n     * @param data The data to import\n     * @param rootUrl The root url for scene and resources\n     * @param meshes The meshes array to import into\n     * @returns True if successful or false otherwise\n     */\n    importMesh(meshesNames, scene, data, rootUrl, meshes) {\n        let matches;\n        if (typeof data !== \"string\") {\n            if (this._isBinary(data)) {\n                // binary .stl\n                const babylonMesh = new Mesh(\"stlmesh\", scene);\n                this._parseBinary(babylonMesh, data);\n                if (meshes) {\n                    meshes.push(babylonMesh);\n                }\n                return true;\n            }\n            // ASCII .stl\n            // convert to string\n            data = new TextDecoder().decode(new Uint8Array(data));\n        }\n        //if arrived here, data is a string, containing the STLA data.\n        while ((matches = this.solidPattern.exec(data))) {\n            let meshName = matches[1];\n            const meshNameFromEnd = matches[3];\n            if (meshNameFromEnd && meshName != meshNameFromEnd) {\n                Tools.Error(\"Error in STL, solid name != endsolid name\");\n                return false;\n            }\n            // check meshesNames\n            if (meshesNames && meshName) {\n                if (meshesNames instanceof Array) {\n                    if (!meshesNames.indexOf(meshName)) {\n                        continue;\n                    }\n                }\n                else {\n                    if (meshName !== meshesNames) {\n                        continue;\n                    }\n                }\n            }\n            // stl mesh name can be empty as well\n            meshName = meshName || \"stlmesh\";\n            const babylonMesh = new Mesh(meshName, scene);\n            this._parseASCII(babylonMesh, matches[2]);\n            if (meshes) {\n                meshes.push(babylonMesh);\n            }\n        }\n        return true;\n    }\n    /**\n     * Load into a scene.\n     * @param scene The scene to load into\n     * @param data The data to import\n     * @param rootUrl The root url for scene and resources\n     * @returns true if successful or false otherwise\n     */\n    load(scene, data, rootUrl) {\n        const result = this.importMesh(null, scene, data, rootUrl, null);\n        return result;\n    }\n    /**\n     * Load into an asset container.\n     * @param scene The scene to load into\n     * @param data The data to import\n     * @param rootUrl The root url for scene and resources\n     * @returns The loaded asset container\n     */\n    loadAssetContainer(scene, data, rootUrl) {\n        const container = new AssetContainer(scene);\n        scene._blockEntityCollection = true;\n        this.importMesh(null, scene, data, rootUrl, container.meshes);\n        scene._blockEntityCollection = false;\n        return container;\n    }\n    _isBinary(data) {\n        // check if file size is correct for binary stl\n        const reader = new DataView(data);\n        // A Binary STL header is 80 bytes, if the data size is not great than\n        // that then it's not a binary STL.\n        if (reader.byteLength <= 80) {\n            return false;\n        }\n        const faceSize = (32 / 8) * 3 + (32 / 8) * 3 * 3 + 16 / 8;\n        const nFaces = reader.getUint32(80, true);\n        if (80 + 32 / 8 + nFaces * faceSize === reader.byteLength) {\n            return true;\n        }\n        // US-ASCII begin with 's', 'o', 'l', 'i', 'd'\n        const ascii = [115, 111, 108, 105, 100];\n        for (let off = 0; off < 5; off++) {\n            if (reader.getUint8(off) !== ascii[off]) {\n                return true;\n            }\n        }\n        return false;\n    }\n    _parseBinary(mesh, data) {\n        const reader = new DataView(data);\n        const faces = reader.getUint32(80, true);\n        const dataOffset = 84;\n        const faceLength = 12 * 4 + 2;\n        let offset = 0;\n        const positions = new Float32Array(faces * 3 * 3);\n        const normals = new Float32Array(faces * 3 * 3);\n        const indices = new Uint32Array(faces * 3);\n        let indicesCount = 0;\n        for (let face = 0; face < faces; face++) {\n            const start = dataOffset + face * faceLength;\n            const normalX = reader.getFloat32(start, true);\n            const normalY = reader.getFloat32(start + 4, true);\n            const normalZ = reader.getFloat32(start + 8, true);\n            for (let i = 1; i <= 3; i++) {\n                const vertexstart = start + i * 12;\n                // ordering is intentional to match ascii import\n                positions[offset] = reader.getFloat32(vertexstart, true);\n                normals[offset] = normalX;\n                if (!STLFileLoader.DO_NOT_ALTER_FILE_COORDINATES) {\n                    positions[offset + 2] = reader.getFloat32(vertexstart + 4, true);\n                    positions[offset + 1] = reader.getFloat32(vertexstart + 8, true);\n                    normals[offset + 2] = normalY;\n                    normals[offset + 1] = normalZ;\n                }\n                else {\n                    positions[offset + 1] = reader.getFloat32(vertexstart + 4, true);\n                    positions[offset + 2] = reader.getFloat32(vertexstart + 8, true);\n                    normals[offset + 1] = normalY;\n                    normals[offset + 2] = normalZ;\n                }\n                offset += 3;\n            }\n            if (STLFileLoader.DO_NOT_ALTER_FILE_COORDINATES) {\n                indices[indicesCount] = indicesCount;\n                indices[indicesCount + 1] = indicesCount + 2;\n                indices[indicesCount + 2] = indicesCount + 1;\n                indicesCount += 3;\n            }\n            else {\n                indices[indicesCount] = indicesCount++;\n                indices[indicesCount] = indicesCount++;\n                indices[indicesCount] = indicesCount++;\n            }\n        }\n        mesh.setVerticesData(VertexBuffer.PositionKind, positions);\n        mesh.setVerticesData(VertexBuffer.NormalKind, normals);\n        mesh.setIndices(indices);\n        mesh.computeWorldMatrix(true);\n    }\n    _parseASCII(mesh, solidData) {\n        const positions = [];\n        const normals = [];\n        const indices = [];\n        let indicesCount = 0;\n        //load facets, ignoring loop as the standard doesn't define it can contain more than vertices\n        let matches;\n        while ((matches = this.facetsPattern.exec(solidData))) {\n            const facet = matches[1];\n            //one normal per face\n            const normalMatches = this.normalPattern.exec(facet);\n            this.normalPattern.lastIndex = 0;\n            if (!normalMatches) {\n                continue;\n            }\n            const normal = [Number(normalMatches[1]), Number(normalMatches[5]), Number(normalMatches[3])];\n            let vertexMatch;\n            while ((vertexMatch = this.vertexPattern.exec(facet))) {\n                if (!STLFileLoader.DO_NOT_ALTER_FILE_COORDINATES) {\n                    positions.push(Number(vertexMatch[1]), Number(vertexMatch[5]), Number(vertexMatch[3]));\n                    normals.push(normal[0], normal[1], normal[2]);\n                }\n                else {\n                    positions.push(Number(vertexMatch[1]), Number(vertexMatch[3]), Number(vertexMatch[5]));\n                    // Flipping the second and third component because inverted\n                    // when normal was declared.\n                    normals.push(normal[0], normal[2], normal[1]);\n                }\n            }\n            if (STLFileLoader.DO_NOT_ALTER_FILE_COORDINATES) {\n                indices.push(indicesCount, indicesCount + 2, indicesCount + 1);\n                indicesCount += 3;\n            }\n            else {\n                indices.push(indicesCount++, indicesCount++, indicesCount++);\n            }\n            this.vertexPattern.lastIndex = 0;\n        }\n        this.facetsPattern.lastIndex = 0;\n        mesh.setVerticesData(VertexBuffer.PositionKind, positions);\n        mesh.setVerticesData(VertexBuffer.NormalKind, normals);\n        mesh.setIndices(indices);\n        mesh.computeWorldMatrix(true);\n    }\n}\n/**\n * Defines if Y and Z axes are swapped or not when loading an STL file.\n * The default is false to maintain backward compatibility. When set to\n * true, coordinates from the STL file are used without change.\n */\nSTLFileLoader.DO_NOT_ALTER_FILE_COORDINATES = false;\nRegisterSceneLoaderPlugin(new STLFileLoader());\n//# sourceMappingURL=stlFileLoader.js.map"], "names": ["STLFileLoader", "STLFileLoaderMetadata", "meshesNames", "scene", "data", "rootUrl", "meshes", "matches", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "meshName", "meshNameFromEnd", "Tools", "container", "<PERSON>setC<PERSON><PERSON>", "reader", "faceSize", "nFaces", "ascii", "off", "mesh", "faces", "dataOffset", "face<PERSON><PERSON><PERSON>", "offset", "positions", "normals", "indices", "indicesCount", "face", "start", "normalX", "normalY", "normalZ", "i", "vertexstart", "VertexBuffer", "solidData", "facet", "normalMatches", "normal", "vertexMatch", "RegisterSceneLoaderPlugin"], "mappings": "uNAWO,MAAMA,CAAc,CACvB,aAAc,CAEV,KAAK,aAAe,0CAEpB,KAAK,cAAgB,2BAErB,KAAK,cAAgB,oJAErB,KAAK,cAAgB,oJAIrB,KAAK,KAAOC,EAAsB,KAMlC,KAAK,WAAaA,EAAsB,UAC3C,CAUD,WAAWC,EAAaC,EAAOC,EAAMC,EAASC,EAAQ,CAClD,IAAIC,EACJ,GAAI,OAAOH,GAAS,SAAU,CAC1B,GAAI,KAAK,UAAUA,CAAI,EAAG,CAEtB,MAAMI,EAAc,IAAIC,EAAK,UAAWN,CAAK,EAC7C,YAAK,aAAaK,EAAaJ,CAAI,EAC/BE,GACAA,EAAO,KAAKE,CAAW,EAEpB,EACV,CAGDJ,EAAO,IAAI,cAAc,OAAO,IAAI,WAAWA,CAAI,CAAC,CACvD,CAED,KAAQG,EAAU,KAAK,aAAa,KAAKH,CAAI,GAAI,CAC7C,IAAIM,EAAWH,EAAQ,CAAC,EACxB,MAAMI,EAAkBJ,EAAQ,CAAC,EACjC,GAAII,GAAmBD,GAAYC,EAC/B,OAAAC,EAAM,MAAM,2CAA2C,EAChD,GAGX,GAAIV,GAAeQ,GACf,GAAIR,aAAuB,OACvB,GAAI,CAACA,EAAY,QAAQQ,CAAQ,EAC7B,iBAIAA,IAAaR,EACb,SAKZQ,EAAWA,GAAY,UACvB,MAAMF,EAAc,IAAIC,EAAKC,EAAUP,CAAK,EAC5C,KAAK,YAAYK,EAAaD,EAAQ,CAAC,CAAC,EACpCD,GACAA,EAAO,KAAKE,CAAW,CAE9B,CACD,MAAO,EACV,CAQD,KAAKL,EAAOC,EAAMC,EAAS,CAEvB,OADe,KAAK,WAAW,KAAMF,EAAOC,EAAMC,EAAS,IAAI,CAElE,CAQD,mBAAmBF,EAAOC,EAAMC,EAAS,CACrC,MAAMQ,EAAY,IAAIC,EAAeX,CAAK,EAC1C,OAAAA,EAAM,uBAAyB,GAC/B,KAAK,WAAW,KAAMA,EAAOC,EAAMC,EAASQ,EAAU,MAAM,EAC5DV,EAAM,uBAAyB,GACxBU,CACV,CACD,UAAUT,EAAM,CAEZ,MAAMW,EAAS,IAAI,SAASX,CAAI,EAGhC,GAAIW,EAAO,YAAc,GACrB,MAAO,GAEX,MAAMC,EAAY,GAAK,EAAK,EAAK,GAAK,EAAK,EAAI,EAAI,GAAK,EAClDC,EAASF,EAAO,UAAU,GAAI,EAAI,EACxC,GAAI,GAAK,GAAK,EAAIE,EAASD,IAAaD,EAAO,WAC3C,MAAO,GAGX,MAAMG,EAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,GAAG,EACtC,QAASC,EAAM,EAAGA,EAAM,EAAGA,IACvB,GAAIJ,EAAO,SAASI,CAAG,IAAMD,EAAMC,CAAG,EAClC,MAAO,GAGf,MAAO,EACV,CACD,aAAaC,EAAMhB,EAAM,CACrB,MAAMW,EAAS,IAAI,SAASX,CAAI,EAC1BiB,EAAQN,EAAO,UAAU,GAAI,EAAI,EACjCO,EAAa,GACbC,EAAa,GAAK,EAAI,EAC5B,IAAIC,EAAS,EACb,MAAMC,EAAY,IAAI,aAAaJ,EAAQ,EAAI,CAAC,EAC1CK,EAAU,IAAI,aAAaL,EAAQ,EAAI,CAAC,EACxCM,EAAU,IAAI,YAAYN,EAAQ,CAAC,EACzC,IAAIO,EAAe,EACnB,QAASC,EAAO,EAAGA,EAAOR,EAAOQ,IAAQ,CACrC,MAAMC,EAAQR,EAAaO,EAAON,EAC5BQ,EAAUhB,EAAO,WAAWe,EAAO,EAAI,EACvCE,EAAUjB,EAAO,WAAWe,EAAQ,EAAG,EAAI,EAC3CG,EAAUlB,EAAO,WAAWe,EAAQ,EAAG,EAAI,EACjD,QAASI,EAAI,EAAGA,GAAK,EAAGA,IAAK,CACzB,MAAMC,EAAcL,EAAQI,EAAI,GAEhCT,EAAUD,CAAM,EAAIT,EAAO,WAAWoB,EAAa,EAAI,EACvDT,EAAQF,CAAM,EAAIO,EACb/B,EAAc,+BAOfyB,EAAUD,EAAS,CAAC,EAAIT,EAAO,WAAWoB,EAAc,EAAG,EAAI,EAC/DV,EAAUD,EAAS,CAAC,EAAIT,EAAO,WAAWoB,EAAc,EAAG,EAAI,EAC/DT,EAAQF,EAAS,CAAC,EAAIQ,EACtBN,EAAQF,EAAS,CAAC,EAAIS,IATtBR,EAAUD,EAAS,CAAC,EAAIT,EAAO,WAAWoB,EAAc,EAAG,EAAI,EAC/DV,EAAUD,EAAS,CAAC,EAAIT,EAAO,WAAWoB,EAAc,EAAG,EAAI,EAC/DT,EAAQF,EAAS,CAAC,EAAIQ,EACtBN,EAAQF,EAAS,CAAC,EAAIS,GAQ1BT,GAAU,CACb,CACGxB,EAAc,+BACd2B,EAAQC,CAAY,EAAIA,EACxBD,EAAQC,EAAe,CAAC,EAAIA,EAAe,EAC3CD,EAAQC,EAAe,CAAC,EAAIA,EAAe,EAC3CA,GAAgB,IAGhBD,EAAQC,CAAY,EAAIA,IACxBD,EAAQC,CAAY,EAAIA,IACxBD,EAAQC,CAAY,EAAIA,IAE/B,CACDR,EAAK,gBAAgBgB,EAAa,aAAcX,CAAS,EACzDL,EAAK,gBAAgBgB,EAAa,WAAYV,CAAO,EACrDN,EAAK,WAAWO,CAAO,EACvBP,EAAK,mBAAmB,EAAI,CAC/B,CACD,YAAYA,EAAMiB,EAAW,CACzB,MAAMZ,EAAY,CAAA,EACZC,EAAU,CAAA,EACVC,EAAU,CAAA,EAChB,IAAIC,EAAe,EAEfrB,EACJ,KAAQA,EAAU,KAAK,cAAc,KAAK8B,CAAS,GAAI,CACnD,MAAMC,EAAQ/B,EAAQ,CAAC,EAEjBgC,EAAgB,KAAK,cAAc,KAAKD,CAAK,EAEnD,GADA,KAAK,cAAc,UAAY,EAC3B,CAACC,EACD,SAEJ,MAAMC,EAAS,CAAC,OAAOD,EAAc,CAAC,CAAC,EAAG,OAAOA,EAAc,CAAC,CAAC,EAAG,OAAOA,EAAc,CAAC,CAAC,CAAC,EAC5F,IAAIE,EACJ,KAAQA,EAAc,KAAK,cAAc,KAAKH,CAAK,GAC1CtC,EAAc,+BAKfyB,EAAU,KAAK,OAAOgB,EAAY,CAAC,CAAC,EAAG,OAAOA,EAAY,CAAC,CAAC,EAAG,OAAOA,EAAY,CAAC,CAAC,CAAC,EAGrFf,EAAQ,KAAKc,EAAO,CAAC,EAAGA,EAAO,CAAC,EAAGA,EAAO,CAAC,CAAC,IAP5Cf,EAAU,KAAK,OAAOgB,EAAY,CAAC,CAAC,EAAG,OAAOA,EAAY,CAAC,CAAC,EAAG,OAAOA,EAAY,CAAC,CAAC,CAAC,EACrFf,EAAQ,KAAKc,EAAO,CAAC,EAAGA,EAAO,CAAC,EAAGA,EAAO,CAAC,CAAC,GAShDxC,EAAc,+BACd2B,EAAQ,KAAKC,EAAcA,EAAe,EAAGA,EAAe,CAAC,EAC7DA,GAAgB,GAGhBD,EAAQ,KAAKC,IAAgBA,IAAgBA,GAAc,EAE/D,KAAK,cAAc,UAAY,CAClC,CACD,KAAK,cAAc,UAAY,EAC/BR,EAAK,gBAAgBgB,EAAa,aAAcX,CAAS,EACzDL,EAAK,gBAAgBgB,EAAa,WAAYV,CAAO,EACrDN,EAAK,WAAWO,CAAO,EACvBP,EAAK,mBAAmB,EAAI,CAC/B,CACL,CAMApB,EAAc,8BAAgC,GAC9C0C,EAA0B,IAAI1C,CAAe", "x_google_ignoreList": [0]}