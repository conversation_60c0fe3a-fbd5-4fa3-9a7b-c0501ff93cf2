import{h as A}from"./index-Cb4A4-Xi.js";import"./index-Ccc2t4AG.js";import"./svelte/svelte.js";const U=1,d=2,N=3,L=9,O=10,Y=11,k=48,z=4,P=0,B=1,C=2,M=3;function G(o){let n=0;return{id_length:o[n++],colormap_type:o[n++],image_type:o[n++],colormap_index:o[n++]|o[n++]<<8,colormap_length:o[n++]|o[n++]<<8,colormap_size:o[n++],origin:[o[n++]|o[n++]<<8,o[n++]|o[n++]<<8],width:o[n++]|o[n++]<<8,height:o[n++]|o[n++]<<8,pixel_size:o[n++],flags:o[n++]}}function R(o,n){if(n.length<19){<PERSON><PERSON>rror("Unable to load TGA file - Not enough data to contain header");return}let a=18;const r=G(n);if(r.id_length+a>n.length){A.Error("Unable to load TGA file - Not enough data");return}a+=r.id_length;let _=!1,f=!1,m=!1;switch(r.image_type){case L:_=!0;case U:f=!0;break;case O:_=!0;case d:break;case Y:_=!0;case N:m=!0;break}let p;const u=r.pixel_size>>3,h=r.width*r.height*u;let s;if(f&&(s=n.subarray(a,a+=r.colormap_length*(r.colormap_size>>3))),_){p=new Uint8Array(h);let I,y,w,b=0;const T=new Uint8Array(u);for(;a<h&&b<h;)if(I=n[a++],y=(I&127)+1,I&128){for(w=0;w<u;++w)T[w]=n[a++];for(w=0;w<y;++w)p.set(T,b+w*u);b+=u*y}else{for(y*=u,w=0;w<y;++w)p[b+w]=n[a++];b+=y}}else p=n.subarray(a,a+=f?r.width*r.height:h);let g,c,i,t,e,l;switch((r.flags&k)>>z){default:case C:g=0,i=1,l=r.width,c=0,t=1,e=r.height;break;case P:g=0,i=1,l=r.width,c=r.height-1,t=-1,e=-1;break;case M:g=r.width-1,i=-1,l=-1,c=0,t=1,e=r.height;break;case B:g=r.width-1,i=-1,l=-1,c=r.height-1,t=-1,e=-1;break}const x="_getImageData"+(m?"Grey":"")+r.pixel_size+"bits",D=j[x](r,s,p,c,t,e,g,i,l);o.getEngine()._uploadDataToTextureDirectly(o,D)}function H(o,n,a,r,_,f,m,p,u){const h=a,s=n,g=o.width,c=o.height;let i,t=0,e,l;const x=new Uint8Array(g*c*4);for(l=r;l!==f;l+=_)for(e=m;e!==u;e+=p,t++)i=h[t],x[(e+g*l)*4+3]=255,x[(e+g*l)*4+2]=s[i*3+0],x[(e+g*l)*4+1]=s[i*3+1],x[(e+g*l)*4+0]=s[i*3+2];return x}function S(o,n,a,r,_,f,m,p,u){const h=a,s=o.width,g=o.height;let c,i=0,t,e;const l=new Uint8Array(s*g*4);for(e=r;e!==f;e+=_)for(t=m;t!==u;t+=p,i+=2){c=h[i+0]+(h[i+1]<<8);const x=((c&31744)>>10)*255/31|0,D=((c&992)>>5)*255/31|0,E=(c&31)*255/31|0;l[(t+s*e)*4+0]=x,l[(t+s*e)*4+1]=D,l[(t+s*e)*4+2]=E,l[(t+s*e)*4+3]=c&32768?0:255}return l}function X(o,n,a,r,_,f,m,p,u){const h=a,s=o.width,g=o.height;let c=0,i,t;const e=new Uint8Array(s*g*4);for(t=r;t!==f;t+=_)for(i=m;i!==u;i+=p,c+=3)e[(i+s*t)*4+3]=255,e[(i+s*t)*4+2]=h[c+0],e[(i+s*t)*4+1]=h[c+1],e[(i+s*t)*4+0]=h[c+2];return e}function v(o,n,a,r,_,f,m,p,u){const h=a,s=o.width,g=o.height;let c=0,i,t;const e=new Uint8Array(s*g*4);for(t=r;t!==f;t+=_)for(i=m;i!==u;i+=p,c+=4)e[(i+s*t)*4+2]=h[c+0],e[(i+s*t)*4+1]=h[c+1],e[(i+s*t)*4+0]=h[c+2],e[(i+s*t)*4+3]=h[c+3];return e}function F(o,n,a,r,_,f,m,p,u){const h=a,s=o.width,g=o.height;let c,i=0,t,e;const l=new Uint8Array(s*g*4);for(e=r;e!==f;e+=_)for(t=m;t!==u;t+=p,i++)c=h[i],l[(t+s*e)*4+0]=c,l[(t+s*e)*4+1]=c,l[(t+s*e)*4+2]=c,l[(t+s*e)*4+3]=255;return l}function K(o,n,a,r,_,f,m,p,u){const h=a,s=o.width,g=o.height;let c=0,i,t;const e=new Uint8Array(s*g*4);for(t=r;t!==f;t+=_)for(i=m;i!==u;i+=p,c+=2)e[(i+s*t)*4+0]=h[c+0],e[(i+s*t)*4+1]=h[c+0],e[(i+s*t)*4+2]=h[c+0],e[(i+s*t)*4+3]=h[c+1];return e}const j={GetTGAHeader:G,UploadContent:R,_getImageData8bits:H,_getImageData16bits:S,_getImageData24bits:X,_getImageData32bits:v,_getImageDataGrey8bits:F,_getImageDataGrey16bits:K};class V{constructor(){this.supportCascades=!1}loadCubeData(){throw".env not supported in Cube."}loadData(n,a,r){const _=new Uint8Array(n.buffer,n.byteOffset,n.byteLength),f=G(_);r(f.width,f.height,a.generateMipMaps,!1,()=>{R(a,_)})}}export{V as _TGATextureLoader};
//# sourceMappingURL=tgaTextureLoader-BFcHp3Ba.js.map
