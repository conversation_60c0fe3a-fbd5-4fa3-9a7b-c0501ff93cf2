{"version": 3, "file": "rgbdEncode.fragment-B5tgC5P8.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/rgbdEncode.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nconst name = \"rgbdEncodePixelShader\";\nconst shader = `varying vec2 vUV;uniform sampler2D textureSampler;\n#include<helperFunctions>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) \n{gl_FragColor=toRGBD(texture2D(textureSampler,vUV).rgb);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const rgbdEncodePixelShader = { name, shader };\n//# sourceMappingURL=rgbdEncode.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "rgbdEncodePixelShader"], "mappings": "qIAGA,MAAMA,EAAO,wBACPC,EAAS;AAAA;AAAA;AAAA;AAAA,2DAMVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAwB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}