{"version": 3, "file": "sankeyDiagram-3MH5UGAL-B4rSfPq8.js", "sources": ["../../../../node_modules/.pnpm/d3-scale-chromatic@3.1.0/node_modules/d3-scale-chromatic/src/colors.js", "../../../../node_modules/.pnpm/d3-scale-chromatic@3.1.0/node_modules/d3-scale-chromatic/src/categorical/Tableau10.js", "../../../../node_modules/.pnpm/d3-array@2.12.1/node_modules/d3-array/src/max.js", "../../../../node_modules/.pnpm/d3-array@2.12.1/node_modules/d3-array/src/min.js", "../../../../node_modules/.pnpm/d3-array@2.12.1/node_modules/d3-array/src/sum.js", "../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/align.js", "../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/constant.js", "../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/sankey.js", "../../../../node_modules/.pnpm/d3-path@1.0.9/node_modules/d3-path/src/path.js", "../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/constant.js", "../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/point.js", "../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/array.js", "../../../../node_modules/.pnpm/d3-shape@1.3.7/node_modules/d3-shape/src/link/index.js", "../../../../node_modules/.pnpm/d3-sankey@0.12.3/node_modules/d3-sankey/src/sankeyLinkHorizontal.js", "../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/sankeyDiagram-3MH5UGAL.mjs"], "sourcesContent": ["export default function(specifier) {\n  var n = specifier.length / 6 | 0, colors = new Array(n), i = 0;\n  while (i < n) colors[i] = \"#\" + specifier.slice(i * 6, ++i * 6);\n  return colors;\n}\n", "import colors from \"../colors.js\";\n\nexport default colors(\"4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab\");\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import {min} from \"d3-array\";\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length ? node.depth\n      : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1\n      : 0;\n}\n", "export default function constant(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {max, min, sum} from \"d3-array\";\nimport {justify} from \"./align.js\";\nimport constant from \"./constant.js\";\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error(\"missing: \" + id);\n  return node;\n}\n\nfunction computeLinkBreadths({nodes}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport default function Sankey() {\n  let x0 = 0, y0 = 0, x1 = 1, y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8, py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey() {\n    const graph = {nodes: nodes.apply(null, arguments), links: links.apply(null, arguments)};\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function(graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function(_) {\n    return arguments.length ? (id = typeof _ === \"function\" ? _ : constant(_), sankey) : id;\n  };\n\n  sankey.nodeAlign = function(_) {\n    return arguments.length ? (align = typeof _ === \"function\" ? _ : constant(_), sankey) : align;\n  };\n\n  sankey.nodeSort = function(_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n\n  sankey.nodeWidth = function(_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n\n  sankey.nodePadding = function(_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n\n  sankey.nodes = function(_) {\n    return arguments.length ? (nodes = typeof _ === \"function\" ? _ : constant(_), sankey) : nodes;\n  };\n\n  sankey.links = function(_) {\n    return arguments.length ? (links = typeof _ === \"function\" ? _ : constant(_), sankey) : links;\n  };\n\n  sankey.linkSort = function(_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n\n  sankey.size = function(_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function(_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n\n  sankey.iterations = function(_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n\n  function computeNodeLinks({nodes, links}) {\n    for (const [i, node] of nodes.entries()) {\n      node.index = i;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    }\n    const nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d]));\n    for (const [i, link] of links.entries()) {\n      link.index = i;\n      let {source, target} = link;\n      if (typeof source !== \"object\") source = link.source = find(nodeById, source);\n      if (typeof target !== \"object\") target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    }\n    if (linkSort != null) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({nodes}) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.depth = x;\n        for (const {target} of node.sourceLinks) {\n          next.add(target);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeHeights({nodes}) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set;\n    let x = 0;\n    while (current.size) {\n      for (const node of current) {\n        node.height = x;\n        for (const {source} of node.targetLinks) {\n          next.add(source);\n        }\n      }\n      if (++x > n) throw new Error(\"circular link\");\n      current = next;\n      next = new Set;\n    }\n  }\n\n  function computeNodeLayers({nodes}) {\n    const x = max(nodes, d => d.depth) + 1;\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {source, value} of target.targetLinks) {\n          let v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {target, value} of source.sourceLinks) {\n          let v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        let dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({sourceLinks, targetLinks}) {\n    if (linkSort === undefined) {\n      for (const {source: {sourceLinks}} of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {target: {targetLinks}} of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {sourceLinks, targetLinks} of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {source: node, width} of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {target: node, width} of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n", "var pi = Math.PI,\n    tau = 2 * pi,\n    epsilon = 1e-6,\n    tauEpsilon = tau - epsilon;\n\nfunction Path() {\n  this._x0 = this._y0 = // start of current subpath\n  this._x1 = this._y1 = null; // end of current subpath\n  this._ = \"\";\n}\n\nfunction path() {\n  return new Path;\n}\n\nPath.prototype = path.prototype = {\n  constructor: Path,\n  moveTo: function(x, y) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y);\n  },\n  closePath: function() {\n    if (this._x1 !== null) {\n      this._x1 = this._x0, this._y1 = this._y0;\n      this._ += \"Z\";\n    }\n  },\n  lineTo: function(x, y) {\n    this._ += \"L\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  quadraticCurveTo: function(x1, y1, x, y) {\n    this._ += \"Q\" + (+x1) + \",\" + (+y1) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  bezierCurveTo: function(x1, y1, x2, y2, x, y) {\n    this._ += \"C\" + (+x1) + \",\" + (+y1) + \",\" + (+x2) + \",\" + (+y2) + \",\" + (this._x1 = +x) + \",\" + (this._y1 = +y);\n  },\n  arcTo: function(x1, y1, x2, y2, r) {\n    x1 = +x1, y1 = +y1, x2 = +x2, y2 = +y2, r = +r;\n    var x0 = this._x1,\n        y0 = this._y1,\n        x21 = x2 - x1,\n        y21 = y2 - y1,\n        x01 = x0 - x1,\n        y01 = y0 - y1,\n        l01_2 = x01 * x01 + y01 * y01;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x1,y1).\n    if (this._x1 === null) {\n      this._ += \"M\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Or, is (x1,y1) coincident with (x0,y0)? Do nothing.\n    else if (!(l01_2 > epsilon));\n\n    // Or, are (x0,y0), (x1,y1) and (x2,y2) collinear?\n    // Equivalently, is (x1,y1) coincident with (x2,y2)?\n    // Or, is the radius zero? Line to (x1,y1).\n    else if (!(Math.abs(y01 * x21 - y21 * x01) > epsilon) || !r) {\n      this._ += \"L\" + (this._x1 = x1) + \",\" + (this._y1 = y1);\n    }\n\n    // Otherwise, draw an arc!\n    else {\n      var x20 = x2 - x0,\n          y20 = y2 - y0,\n          l21_2 = x21 * x21 + y21 * y21,\n          l20_2 = x20 * x20 + y20 * y20,\n          l21 = Math.sqrt(l21_2),\n          l01 = Math.sqrt(l01_2),\n          l = r * Math.tan((pi - Math.acos((l21_2 + l01_2 - l20_2) / (2 * l21 * l01))) / 2),\n          t01 = l / l01,\n          t21 = l / l21;\n\n      // If the start tangent is not coincident with (x0,y0), line to.\n      if (Math.abs(t01 - 1) > epsilon) {\n        this._ += \"L\" + (x1 + t01 * x01) + \",\" + (y1 + t01 * y01);\n      }\n\n      this._ += \"A\" + r + \",\" + r + \",0,0,\" + (+(y01 * x20 > x01 * y20)) + \",\" + (this._x1 = x1 + t21 * x21) + \",\" + (this._y1 = y1 + t21 * y21);\n    }\n  },\n  arc: function(x, y, r, a0, a1, ccw) {\n    x = +x, y = +y, r = +r, ccw = !!ccw;\n    var dx = r * Math.cos(a0),\n        dy = r * Math.sin(a0),\n        x0 = x + dx,\n        y0 = y + dy,\n        cw = 1 ^ ccw,\n        da = ccw ? a0 - a1 : a1 - a0;\n\n    // Is the radius negative? Error.\n    if (r < 0) throw new Error(\"negative radius: \" + r);\n\n    // Is this path empty? Move to (x0,y0).\n    if (this._x1 === null) {\n      this._ += \"M\" + x0 + \",\" + y0;\n    }\n\n    // Or, is (x0,y0) not coincident with the previous point? Line to (x0,y0).\n    else if (Math.abs(this._x1 - x0) > epsilon || Math.abs(this._y1 - y0) > epsilon) {\n      this._ += \"L\" + x0 + \",\" + y0;\n    }\n\n    // Is this arc empty? We’re done.\n    if (!r) return;\n\n    // Does the angle go the wrong way? Flip the direction.\n    if (da < 0) da = da % tau + tau;\n\n    // Is this a complete circle? Draw two arcs to complete the circle.\n    if (da > tauEpsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (x - dx) + \",\" + (y - dy) + \"A\" + r + \",\" + r + \",0,1,\" + cw + \",\" + (this._x1 = x0) + \",\" + (this._y1 = y0);\n    }\n\n    // Is this arc non-empty? Draw an arc!\n    else if (da > epsilon) {\n      this._ += \"A\" + r + \",\" + r + \",0,\" + (+(da >= pi)) + \",\" + cw + \",\" + (this._x1 = x + r * Math.cos(a1)) + \",\" + (this._y1 = y + r * Math.sin(a1));\n    }\n  },\n  rect: function(x, y, w, h) {\n    this._ += \"M\" + (this._x0 = this._x1 = +x) + \",\" + (this._y0 = this._y1 = +y) + \"h\" + (+w) + \"v\" + (+h) + \"h\" + (-w) + \"Z\";\n  },\n  toString: function() {\n    return this._;\n  }\n};\n\nexport default path;\n", "export default function(x) {\n  return function constant() {\n    return x;\n  };\n}\n", "export function x(p) {\n  return p[0];\n}\n\nexport function y(p) {\n  return p[1];\n}\n", "export var slice = Array.prototype.slice;\n", "import {path} from \"d3-path\";\nimport {slice} from \"../array.js\";\nimport constant from \"../constant.js\";\nimport {x as pointX, y as pointY} from \"../point.js\";\nimport pointRadial from \"../pointRadial.js\";\n\nfunction linkSource(d) {\n  return d.source;\n}\n\nfunction linkTarget(d) {\n  return d.target;\n}\n\nfunction link(curve) {\n  var source = linkSource,\n      target = linkTarget,\n      x = pointX,\n      y = pointY,\n      context = null;\n\n  function link() {\n    var buffer, argv = slice.call(arguments), s = source.apply(this, argv), t = target.apply(this, argv);\n    if (!context) context = buffer = path();\n    curve(context, +x.apply(this, (argv[0] = s, argv)), +y.apply(this, argv), +x.apply(this, (argv[0] = t, argv)), +y.apply(this, argv));\n    if (buffer) return context = null, buffer + \"\" || null;\n  }\n\n  link.source = function(_) {\n    return arguments.length ? (source = _, link) : source;\n  };\n\n  link.target = function(_) {\n    return arguments.length ? (target = _, link) : target;\n  };\n\n  link.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), link) : x;\n  };\n\n  link.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), link) : y;\n  };\n\n  link.context = function(_) {\n    return arguments.length ? ((context = _ == null ? null : _), link) : context;\n  };\n\n  return link;\n}\n\nfunction curveHorizontal(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0 = (x0 + x1) / 2, y0, x0, y1, x1, y1);\n}\n\nfunction curveVertical(context, x0, y0, x1, y1) {\n  context.moveTo(x0, y0);\n  context.bezierCurveTo(x0, y0 = (y0 + y1) / 2, x1, y0, x1, y1);\n}\n\nfunction curveRadial(context, x0, y0, x1, y1) {\n  var p0 = pointRadial(x0, y0),\n      p1 = pointRadial(x0, y0 = (y0 + y1) / 2),\n      p2 = pointRadial(x1, y0),\n      p3 = pointRadial(x1, y1);\n  context.moveTo(p0[0], p0[1]);\n  context.bezierCurveTo(p1[0], p1[1], p2[0], p2[1], p3[0], p3[1]);\n}\n\nexport function linkHorizontal() {\n  return link(curveHorizontal);\n}\n\nexport function linkVertical() {\n  return link(curveVertical);\n}\n\nexport function linkRadial() {\n  var l = link(curveRadial);\n  l.angle = l.x, delete l.x;\n  l.radius = l.y, delete l.y;\n  return l;\n}\n", "import {linkHorizontal} from \"d3-shape\";\n\nfunction horizontalSource(d) {\n  return [d.source.x1, d.y0];\n}\n\nfunction horizontalTarget(d) {\n  return [d.target.x0, d.y1];\n}\n\nexport default function() {\n  return linkHorizontal()\n      .source(horizontalSource)\n      .target(horizontalTarget);\n}\n", "import {\n  __name,\n  clear,\n  common_default,\n  defaultConfig2 as defaultConfig,\n  getAccDescription,\n  getAccTitle,\n  getConfig2 as getConfig,\n  getDiagramTitle,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle,\n  setupGraphViewbox\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/sankey/parser/sankey.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 9], $V1 = [1, 10], $V2 = [1, 5, 10, 12];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"SANKEY\": 4, \"NEWLINE\": 5, \"csv\": 6, \"opt_eof\": 7, \"record\": 8, \"csv_tail\": 9, \"EOF\": 10, \"field[source]\": 11, \"COMMA\": 12, \"field[target]\": 13, \"field[value]\": 14, \"field\": 15, \"escaped\": 16, \"non_escaped\": 17, \"DQUOTE\": 18, \"ESCAPED_TEXT\": 19, \"NON_ESCAPED_TEXT\": 20, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 4: \"SANKEY\", 5: \"NEWLINE\", 10: \"EOF\", 11: \"field[source]\", 12: \"COMMA\", 13: \"field[target]\", 14: \"field[value]\", 18: \"DQUOTE\", 19: \"ESCAPED_TEXT\", 20: \"NON_ESCAPED_TEXT\" },\n    productions_: [0, [3, 4], [6, 2], [9, 2], [9, 0], [7, 1], [7, 0], [8, 5], [15, 1], [15, 1], [16, 3], [17, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 7:\n          const source = yy.findOrCreateNode($$[$0 - 4].trim().replaceAll('\"\"', '\"'));\n          const target = yy.findOrCreateNode($$[$0 - 2].trim().replaceAll('\"\"', '\"'));\n          const value = parseFloat($$[$0].trim());\n          yy.addLink(source, target, value);\n          break;\n        case 8:\n        case 9:\n        case 11:\n          this.$ = $$[$0];\n          break;\n        case 10:\n          this.$ = $$[$0 - 1];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [{ 3: 1, 4: [1, 2] }, { 1: [3] }, { 5: [1, 3] }, { 6: 4, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 1: [2, 6], 7: 11, 10: [1, 12] }, o($V1, [2, 4], { 9: 13, 5: [1, 14] }), { 12: [1, 15] }, o($V2, [2, 8]), o($V2, [2, 9]), { 19: [1, 16] }, o($V2, [2, 11]), { 1: [2, 1] }, { 1: [2, 5] }, o($V1, [2, 2]), { 6: 17, 8: 5, 15: 6, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 15: 18, 16: 7, 17: 8, 18: $V0, 20: $V1 }, { 18: [1, 19] }, o($V1, [2, 3]), { 12: [1, 20] }, o($V2, [2, 10]), { 15: 21, 16: 7, 17: 8, 18: $V0, 20: $V1 }, o([1, 5, 10], [2, 7])],\n    defaultActions: { 11: [2, 1], 12: [2, 5] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            this.pushState(\"csv\");\n            return 4;\n            break;\n          case 1:\n            return 10;\n            break;\n          case 2:\n            return 5;\n            break;\n          case 3:\n            return 12;\n            break;\n          case 4:\n            this.pushState(\"escaped_text\");\n            return 18;\n            break;\n          case 5:\n            return 20;\n            break;\n          case 6:\n            this.popState(\"escaped_text\");\n            return 18;\n            break;\n          case 7:\n            return 19;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:sankey-beta\\b)/i, /^(?:$)/i, /^(?:((\\u000D\\u000A)|(\\u000A)))/i, /^(?:(\\u002C))/i, /^(?:(\\u0022))/i, /^(?:([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])*)/i, /^(?:(\\u0022)(?!(\\u0022)))/i, /^(?:(([\\u0020-\\u0021\\u0023-\\u002B\\u002D-\\u007E])|(\\u002C)|(\\u000D)|(\\u000A)|(\\u0022)(\\u0022))*)/i],\n      conditions: { \"csv\": { \"rules\": [1, 2, 3, 4, 5, 6, 7], \"inclusive\": false }, \"escaped_text\": { \"rules\": [6, 7], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 2, 3, 4, 5, 6, 7], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar sankey_default = parser;\n\n// src/diagrams/sankey/sankeyDB.ts\nvar links = [];\nvar nodes = [];\nvar nodesMap = /* @__PURE__ */ new Map();\nvar clear2 = /* @__PURE__ */ __name(() => {\n  links = [];\n  nodes = [];\n  nodesMap = /* @__PURE__ */ new Map();\n  clear();\n}, \"clear\");\nvar SankeyLink = class {\n  constructor(source, target, value = 0) {\n    this.source = source;\n    this.target = target;\n    this.value = value;\n  }\n  static {\n    __name(this, \"SankeyLink\");\n  }\n};\nvar addLink = /* @__PURE__ */ __name((source, target, value) => {\n  links.push(new SankeyLink(source, target, value));\n}, \"addLink\");\nvar SankeyNode = class {\n  constructor(ID) {\n    this.ID = ID;\n  }\n  static {\n    __name(this, \"SankeyNode\");\n  }\n};\nvar findOrCreateNode = /* @__PURE__ */ __name((ID) => {\n  ID = common_default.sanitizeText(ID, getConfig());\n  let node = nodesMap.get(ID);\n  if (node === void 0) {\n    node = new SankeyNode(ID);\n    nodesMap.set(ID, node);\n    nodes.push(node);\n  }\n  return node;\n}, \"findOrCreateNode\");\nvar getNodes = /* @__PURE__ */ __name(() => nodes, \"getNodes\");\nvar getLinks = /* @__PURE__ */ __name(() => links, \"getLinks\");\nvar getGraph = /* @__PURE__ */ __name(() => ({\n  nodes: nodes.map((node) => ({ id: node.ID })),\n  links: links.map((link) => ({\n    source: link.source.ID,\n    target: link.target.ID,\n    value: link.value\n  }))\n}), \"getGraph\");\nvar sankeyDB_default = {\n  nodesMap,\n  getConfig: /* @__PURE__ */ __name(() => getConfig().sankey, \"getConfig\"),\n  getNodes,\n  getLinks,\n  getGraph,\n  addLink,\n  findOrCreateNode,\n  getAccTitle,\n  setAccTitle,\n  getAccDescription,\n  setAccDescription,\n  getDiagramTitle,\n  setDiagramTitle,\n  clear: clear2\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nimport {\n  select as d3select,\n  scaleOrdinal as d3scaleOrdinal,\n  schemeTableau10 as d3schemeTableau10\n} from \"d3\";\nimport {\n  sankey as d3Sankey,\n  sankeyLinkHorizontal as d3SankeyLinkHorizontal,\n  sankeyLeft as d3SankeyLeft,\n  sankeyRight as d3SankeyRight,\n  sankeyCenter as d3SankeyCenter,\n  sankeyJustify as d3SankeyJustify\n} from \"d3-sankey\";\n\n// src/rendering-util/uid.ts\nvar Uid = class _Uid {\n  static {\n    __name(this, \"Uid\");\n  }\n  static {\n    this.count = 0;\n  }\n  static next(name) {\n    return new _Uid(name + ++_Uid.count);\n  }\n  constructor(id) {\n    this.id = id;\n    this.href = `#${id}`;\n  }\n  toString() {\n    return \"url(\" + this.href + \")\";\n  }\n};\n\n// src/diagrams/sankey/sankeyRenderer.ts\nvar alignmentsMap = {\n  left: d3SankeyLeft,\n  right: d3SankeyRight,\n  center: d3SankeyCenter,\n  justify: d3SankeyJustify\n};\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  const { securityLevel, sankey: conf } = getConfig();\n  const defaultSankeyConfig = defaultConfig.sankey;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = d3select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? d3select(sandboxElement.nodes()[0].contentDocument.body) : d3select(\"body\");\n  const svg = securityLevel === \"sandbox\" ? root.select(`[id=\"${id}\"]`) : d3select(`[id=\"${id}\"]`);\n  const width = conf?.width ?? defaultSankeyConfig.width;\n  const height = conf?.height ?? defaultSankeyConfig.width;\n  const useMaxWidth = conf?.useMaxWidth ?? defaultSankeyConfig.useMaxWidth;\n  const nodeAlignment = conf?.nodeAlignment ?? defaultSankeyConfig.nodeAlignment;\n  const prefix = conf?.prefix ?? defaultSankeyConfig.prefix;\n  const suffix = conf?.suffix ?? defaultSankeyConfig.suffix;\n  const showValues = conf?.showValues ?? defaultSankeyConfig.showValues;\n  const graph = diagObj.db.getGraph();\n  const nodeAlign = alignmentsMap[nodeAlignment];\n  const nodeWidth = 10;\n  const sankey = d3Sankey().nodeId((d) => d.id).nodeWidth(nodeWidth).nodePadding(10 + (showValues ? 15 : 0)).nodeAlign(nodeAlign).extent([\n    [0, 0],\n    [width, height]\n  ]);\n  sankey(graph);\n  const colorScheme = d3scaleOrdinal(d3schemeTableau10);\n  svg.append(\"g\").attr(\"class\", \"nodes\").selectAll(\".node\").data(graph.nodes).join(\"g\").attr(\"class\", \"node\").attr(\"id\", (d) => (d.uid = Uid.next(\"node-\")).id).attr(\"transform\", function(d) {\n    return \"translate(\" + d.x0 + \",\" + d.y0 + \")\";\n  }).attr(\"x\", (d) => d.x0).attr(\"y\", (d) => d.y0).append(\"rect\").attr(\"height\", (d) => {\n    return d.y1 - d.y0;\n  }).attr(\"width\", (d) => d.x1 - d.x0).attr(\"fill\", (d) => colorScheme(d.id));\n  const getText = /* @__PURE__ */ __name(({ id: id2, value }) => {\n    if (!showValues) {\n      return id2;\n    }\n    return `${id2}\n${prefix}${Math.round(value * 100) / 100}${suffix}`;\n  }, \"getText\");\n  svg.append(\"g\").attr(\"class\", \"node-labels\").attr(\"font-size\", 14).selectAll(\"text\").data(graph.nodes).join(\"text\").attr(\"x\", (d) => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6).attr(\"y\", (d) => (d.y1 + d.y0) / 2).attr(\"dy\", `${showValues ? \"0\" : \"0.35\"}em`).attr(\"text-anchor\", (d) => d.x0 < width / 2 ? \"start\" : \"end\").text(getText);\n  const link = svg.append(\"g\").attr(\"class\", \"links\").attr(\"fill\", \"none\").attr(\"stroke-opacity\", 0.5).selectAll(\".link\").data(graph.links).join(\"g\").attr(\"class\", \"link\").style(\"mix-blend-mode\", \"multiply\");\n  const linkColor = conf?.linkColor ?? \"gradient\";\n  if (linkColor === \"gradient\") {\n    const gradient = link.append(\"linearGradient\").attr(\"id\", (d) => (d.uid = Uid.next(\"linearGradient-\")).id).attr(\"gradientUnits\", \"userSpaceOnUse\").attr(\"x1\", (d) => d.source.x1).attr(\"x2\", (d) => d.target.x0);\n    gradient.append(\"stop\").attr(\"offset\", \"0%\").attr(\"stop-color\", (d) => colorScheme(d.source.id));\n    gradient.append(\"stop\").attr(\"offset\", \"100%\").attr(\"stop-color\", (d) => colorScheme(d.target.id));\n  }\n  let coloring;\n  switch (linkColor) {\n    case \"gradient\":\n      coloring = /* @__PURE__ */ __name((d) => d.uid, \"coloring\");\n      break;\n    case \"source\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.source.id), \"coloring\");\n      break;\n    case \"target\":\n      coloring = /* @__PURE__ */ __name((d) => colorScheme(d.target.id), \"coloring\");\n      break;\n    default:\n      coloring = linkColor;\n  }\n  link.append(\"path\").attr(\"d\", d3SankeyLinkHorizontal()).attr(\"stroke\", coloring).attr(\"stroke-width\", (d) => Math.max(1, d.width));\n  setupGraphViewbox(void 0, svg, 0, useMaxWidth);\n}, \"draw\");\nvar sankeyRenderer_default = {\n  draw\n};\n\n// src/diagrams/sankey/sankeyUtils.ts\nvar prepareTextForParsing = /* @__PURE__ */ __name((text) => {\n  const textToParse = text.replaceAll(/^[^\\S\\n\\r]+|[^\\S\\n\\r]+$/g, \"\").replaceAll(/([\\n\\r])+/g, \"\\n\").trim();\n  return textToParse;\n}, \"prepareTextForParsing\");\n\n// src/diagrams/sankey/styles.js\nvar getStyles = /* @__PURE__ */ __name((options) => `.label {\n      font-family: ${options.fontFamily};\n    }`, \"getStyles\");\nvar styles_default = getStyles;\n\n// src/diagrams/sankey/sankeyDiagram.ts\nvar originalParse = sankey_default.parse.bind(sankey_default);\nsankey_default.parse = (text) => originalParse(prepareTextForParsing(text));\nvar diagram = {\n  styles: styles_default,\n  parser: sankey_default,\n  db: sankeyDB_default,\n  renderer: sankeyRenderer_default\n};\nexport {\n  diagram\n};\n"], "names": ["colors", "specifier", "i", "d3schemeTableau10", "max", "values", "valueof", "value", "index", "min", "sum", "targetDepth", "d", "left", "node", "right", "justify", "center", "constant", "x", "ascendingSourceBreadth", "a", "b", "ascendingBreadth", "ascending<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultId", "defaultNodes", "graph", "defaultLinks", "find", "nodeById", "id", "computeLinkBreadths", "nodes", "y0", "y1", "link", "<PERSON><PERSON>", "x0", "x1", "dx", "dy", "py", "align", "sort", "linkSort", "links", "iterations", "sankey", "computeNodeLinks", "computeNodeValues", "computeNodeDepths", "computeNodeHeights", "computeNodeBreadths", "_", "source", "target", "sourceLinks", "targetLinks", "n", "current", "next", "computeNodeLayers", "kx", "columns", "column", "initializeNodeBreadths", "ky", "c", "y", "reorderLinks", "alpha", "beta", "relaxRightToLeft", "relaxLeftToRight", "w", "v", "targetTop", "reorderNodeLinks", "resolveCollisions", "sourceTop", "subject", "resolveCollisionsBottomToTop", "resolveCollisionsTopToBottom", "width", "pi", "tau", "epsilon", "tauEpsilon", "Path", "path", "x2", "y2", "r", "x21", "y21", "x01", "y01", "l01_2", "x20", "y20", "l21_2", "l20_2", "l21", "l01", "l", "t01", "t21", "a0", "a1", "ccw", "cw", "da", "h", "p", "slice", "linkSource", "linkTarget", "curve", "pointX", "pointY", "context", "buffer", "argv", "s", "t", "curveHorizontal", "linkHorizontal", "horizontalSource", "horizontalTarget", "d3SankeyLinkHorizontal", "parser", "o", "__name", "o2", "$V0", "$V1", "$V2", "parser2", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "TERROR", "EOF", "args", "lexer2", "sharedState", "k", "yyloc", "ranges", "popStack", "lex", "token", "symbol", "state", "action", "yyval", "len", "newState", "expected", "errStr", "lexer", "ch", "lines", "oldLines", "past", "pre", "match", "indexed_rule", "backup", "tempMatch", "rules", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "<PERSON><PERSON><PERSON>", "sankey_default", "nodesMap", "clear2", "clear", "SankeyLink", "addLink", "SankeyNode", "ID", "findOrCreateNode", "common_default", "getConfig", "getNodes", "getLinks", "getGraph", "sankeyDB_default", "getAccTitle", "setAccTitle", "getAccDescription", "setAccDescription", "getDiagramTitle", "setDiagramTitle", "<PERSON><PERSON>", "_Uid", "name", "alignmentsMap", "d3SankeyLeft", "d3SankeyRight", "d3SankeyCenter", "d3SankeyJustify", "draw", "text", "_version", "diagObj", "securityLevel", "conf", "defaultSankeyConfig", "defaultConfig", "sandboxElement", "d3select", "root", "svg", "height", "useMaxWidth", "nodeAlignment", "prefix", "suffix", "showValues", "nodeAlign", "d3Sankey", "colorScheme", "d3scaleOrdinal", "getText", "id2", "linkColor", "gradient", "coloring", "setupGraphViewbox", "sankeyRenderer_default", "prepareTextForParsing", "getStyles", "options", "styles_default", "originalParse", "diagram"], "mappings": "uWAAe,SAAQA,GAACC,EAAW,CAEjC,QADI,EAAIA,EAAU,OAAS,EAAI,EAAGD,EAAS,IAAI,MAAM,CAAC,EAAGE,EAAI,EACtDA,EAAI,GAAGF,EAAOE,CAAC,EAAI,IAAMD,EAAU,MAAMC,EAAI,EAAG,EAAEA,EAAI,CAAC,EAC9D,OAAOF,CACT,CCFA,MAAeG,GAAAH,GAAO,8DAA8D,ECFrE,SAASI,GAAIC,EAAQC,EAAS,CAC3C,IAAIF,EACJ,GAAIE,IAAY,OACd,UAAWC,KAASF,EACdE,GAAS,OACLH,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzCD,EAAMG,GAAUH,IAAQ,QAAaG,GAASA,KACpDH,EAAMG,EAGX,CACD,OAAOH,CACT,CCnBe,SAASK,GAAIJ,EAAQC,EAAS,CAC3C,IAAIG,EACJ,GAAIH,IAAY,OACd,UAAWC,KAASF,EACdE,GAAS,OACLE,EAAMF,GAAUE,IAAQ,QAAaF,GAASA,KACpDE,EAAMF,OAGL,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACXE,EAAQD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,IAAM,OACzCI,EAAMF,GAAUE,IAAQ,QAAaF,GAASA,KACpDE,EAAMF,EAGX,CACD,OAAOE,CACT,CCnBe,SAASC,EAAIL,EAAQC,EAAS,CAC3C,IAAII,EAAM,EACV,GAAIJ,IAAY,OACd,QAASC,KAASF,GACZE,EAAQ,CAACA,KACXG,GAAOH,OAGN,CACL,IAAIC,EAAQ,GACZ,QAASD,KAASF,GACZE,EAAQ,CAACD,EAAQC,EAAO,EAAEC,EAAOH,CAAM,KACzCK,GAAOH,EAGZ,CACD,OAAOG,CACT,CCfA,SAASC,GAAYC,EAAG,CACtB,OAAOA,EAAE,OAAO,KAClB,CAEO,SAASC,GAAKC,EAAM,CACzB,OAAOA,EAAK,KACd,CAEO,SAASC,GAAMD,EAAM,EAAG,CAC7B,OAAO,EAAI,EAAIA,EAAK,MACtB,CAEO,SAASE,GAAQF,EAAM,EAAG,CAC/B,OAAOA,EAAK,YAAY,OAASA,EAAK,MAAQ,EAAI,CACpD,CAEO,SAASG,GAAOH,EAAM,CAC3B,OAAOA,EAAK,YAAY,OAASA,EAAK,MAChCA,EAAK,YAAY,OAASL,GAAIK,EAAK,YAAaH,EAAW,EAAI,EAC/D,CACR,CCtBe,SAASO,EAASC,EAAG,CAClC,OAAO,UAAW,CAChB,OAAOA,CACX,CACA,CCAA,SAASC,GAAuBC,EAAGC,EAAG,CACpC,OAAOC,EAAiBF,EAAE,OAAQC,EAAE,MAAM,GAAKD,EAAE,MAAQC,EAAE,KAC7D,CAEA,SAASE,GAAuBH,EAAGC,EAAG,CACpC,OAAOC,EAAiBF,EAAE,OAAQC,EAAE,MAAM,GAAKD,EAAE,MAAQC,EAAE,KAC7D,CAEA,SAASC,EAAiBF,EAAGC,EAAG,CAC9B,OAAOD,EAAE,GAAKC,EAAE,EAClB,CAEA,SAASf,GAAMK,EAAG,CAChB,OAAOA,EAAE,KACX,CAEA,SAASa,GAAUb,EAAG,CACpB,OAAOA,EAAE,KACX,CAEA,SAASc,GAAaC,EAAO,CAC3B,OAAOA,EAAM,KACf,CAEA,SAASC,GAAaD,EAAO,CAC3B,OAAOA,EAAM,KACf,CAEA,SAASE,GAAKC,EAAUC,EAAI,CAC1B,MAAMjB,EAAOgB,EAAS,IAAIC,CAAE,EAC5B,GAAI,CAACjB,EAAM,MAAM,IAAI,MAAM,YAAciB,CAAE,EAC3C,OAAOjB,CACT,CAEA,SAASkB,GAAoB,CAAC,MAAAC,CAAK,EAAG,CACpC,UAAWnB,KAAQmB,EAAO,CACxB,IAAIC,EAAKpB,EAAK,GACVqB,EAAKD,EACT,UAAWE,KAAQtB,EAAK,YACtBsB,EAAK,GAAKF,EAAKE,EAAK,MAAQ,EAC5BF,GAAME,EAAK,MAEb,UAAWA,KAAQtB,EAAK,YACtBsB,EAAK,GAAKD,EAAKC,EAAK,MAAQ,EAC5BD,GAAMC,EAAK,KAEd,CACH,CAEe,SAASC,IAAS,CAC/B,IAAIC,EAAK,EAAGJ,EAAK,EAAGK,EAAK,EAAGJ,EAAK,EAC7BK,EAAK,GACLC,EAAK,EAAGC,EACRX,EAAKN,GACLkB,EAAQ3B,GACR4B,EACAC,EACAZ,EAAQP,GACRoB,EAAQlB,GACRmB,EAAa,EAEjB,SAASC,GAAS,CAChB,MAAMrB,EAAQ,CAAC,MAAOM,EAAM,MAAM,KAAM,SAAS,EAAG,MAAOa,EAAM,MAAM,KAAM,SAAS,CAAC,EACvF,OAAAG,EAAiBtB,CAAK,EACtBuB,EAAkBvB,CAAK,EACvBwB,EAAkBxB,CAAK,EACvByB,EAAmBzB,CAAK,EACxB0B,EAAoB1B,CAAK,EACzBK,GAAoBL,CAAK,EAClBA,CACR,CAEDqB,EAAO,OAAS,SAASrB,EAAO,CAC9B,OAAAK,GAAoBL,CAAK,EAClBA,CACX,EAEEqB,EAAO,OAAS,SAASM,EAAG,CAC1B,OAAO,UAAU,QAAUvB,EAAK,OAAOuB,GAAM,WAAaA,EAAIpC,EAASoC,CAAC,EAAGN,GAAUjB,CACzF,EAEEiB,EAAO,UAAY,SAASM,EAAG,CAC7B,OAAO,UAAU,QAAUX,EAAQ,OAAOW,GAAM,WAAaA,EAAIpC,EAASoC,CAAC,EAAGN,GAAUL,CAC5F,EAEEK,EAAO,SAAW,SAASM,EAAG,CAC5B,OAAO,UAAU,QAAUV,EAAOU,EAAGN,GAAUJ,CACnD,EAEEI,EAAO,UAAY,SAASM,EAAG,CAC7B,OAAO,UAAU,QAAUd,EAAK,CAACc,EAAGN,GAAUR,CAClD,EAEEQ,EAAO,YAAc,SAASM,EAAG,CAC/B,OAAO,UAAU,QAAUb,EAAKC,EAAK,CAACY,EAAGN,GAAUP,CACvD,EAEEO,EAAO,MAAQ,SAASM,EAAG,CACzB,OAAO,UAAU,QAAUrB,EAAQ,OAAOqB,GAAM,WAAaA,EAAIpC,EAASoC,CAAC,EAAGN,GAAUf,CAC5F,EAEEe,EAAO,MAAQ,SAASM,EAAG,CACzB,OAAO,UAAU,QAAUR,EAAQ,OAAOQ,GAAM,WAAaA,EAAIpC,EAASoC,CAAC,EAAGN,GAAUF,CAC5F,EAEEE,EAAO,SAAW,SAASM,EAAG,CAC5B,OAAO,UAAU,QAAUT,EAAWS,EAAGN,GAAUH,CACvD,EAEEG,EAAO,KAAO,SAASM,EAAG,CACxB,OAAO,UAAU,QAAUhB,EAAKJ,EAAK,EAAGK,EAAK,CAACe,EAAE,CAAC,EAAGnB,EAAK,CAACmB,EAAE,CAAC,EAAGN,GAAU,CAACT,EAAKD,EAAIH,EAAKD,CAAE,CAC/F,EAEEc,EAAO,OAAS,SAASM,EAAG,CAC1B,OAAO,UAAU,QAAUhB,EAAK,CAACgB,EAAE,CAAC,EAAE,CAAC,EAAGf,EAAK,CAACe,EAAE,CAAC,EAAE,CAAC,EAAGpB,EAAK,CAACoB,EAAE,CAAC,EAAE,CAAC,EAAGnB,EAAK,CAACmB,EAAE,CAAC,EAAE,CAAC,EAAGN,GAAU,CAAC,CAACV,EAAIJ,CAAE,EAAG,CAACK,EAAIJ,CAAE,CAAC,CACxH,EAEEa,EAAO,WAAa,SAASM,EAAG,CAC9B,OAAO,UAAU,QAAUP,EAAa,CAACO,EAAGN,GAAUD,CAC1D,EAEE,SAASE,EAAiB,CAAC,MAAAhB,EAAO,MAAAa,CAAK,EAAG,CACxC,SAAW,CAAC5C,EAAGY,CAAI,IAAKmB,EAAM,QAAO,EACnCnB,EAAK,MAAQZ,EACbY,EAAK,YAAc,GACnBA,EAAK,YAAc,GAErB,MAAMgB,EAAW,IAAI,IAAIG,EAAM,IAAI,CAACrB,EAAGV,IAAM,CAAC6B,EAAGnB,EAAGV,EAAG+B,CAAK,EAAGrB,CAAC,CAAC,CAAC,EAClE,SAAW,CAACV,EAAGkC,CAAI,IAAKU,EAAM,QAAO,EAAI,CACvCV,EAAK,MAAQlC,EACb,GAAI,CAAC,OAAAqD,EAAQ,OAAAC,CAAM,EAAIpB,EACnB,OAAOmB,GAAW,WAAUA,EAASnB,EAAK,OAASP,GAAKC,EAAUyB,CAAM,GACxE,OAAOC,GAAW,WAAUA,EAASpB,EAAK,OAASP,GAAKC,EAAU0B,CAAM,GAC5ED,EAAO,YAAY,KAAKnB,CAAI,EAC5BoB,EAAO,YAAY,KAAKpB,CAAI,CAC7B,CACD,GAAIS,GAAY,KACd,SAAW,CAAC,YAAAY,EAAa,YAAAC,CAAW,IAAKzB,EACvCwB,EAAY,KAAKZ,CAAQ,EACzBa,EAAY,KAAKb,CAAQ,CAG9B,CAED,SAASK,EAAkB,CAAC,MAAAjB,CAAK,EAAG,CAClC,UAAWnB,KAAQmB,EACjBnB,EAAK,MAAQA,EAAK,aAAe,OAC3B,KAAK,IAAIJ,EAAII,EAAK,YAAaP,EAAK,EAAGG,EAAII,EAAK,YAAaP,EAAK,CAAC,EACnEO,EAAK,UAEd,CAED,SAASqC,EAAkB,CAAC,MAAAlB,CAAK,EAAG,CAClC,MAAM0B,EAAI1B,EAAM,OAChB,IAAI2B,EAAU,IAAI,IAAI3B,CAAK,EACvB4B,EAAO,IAAI,IACX1C,EAAI,EACR,KAAOyC,EAAQ,MAAM,CACnB,UAAW9C,KAAQ8C,EAAS,CAC1B9C,EAAK,MAAQK,EACb,SAAW,CAAC,OAAAqC,CAAM,IAAK1C,EAAK,YAC1B+C,EAAK,IAAIL,CAAM,CAElB,CACD,GAAI,EAAErC,EAAIwC,EAAG,MAAM,IAAI,MAAM,eAAe,EAC5CC,EAAUC,EACVA,EAAO,IAAI,GACZ,CACF,CAED,SAAST,EAAmB,CAAC,MAAAnB,CAAK,EAAG,CACnC,MAAM0B,EAAI1B,EAAM,OAChB,IAAI2B,EAAU,IAAI,IAAI3B,CAAK,EACvB4B,EAAO,IAAI,IACX1C,EAAI,EACR,KAAOyC,EAAQ,MAAM,CACnB,UAAW9C,KAAQ8C,EAAS,CAC1B9C,EAAK,OAASK,EACd,SAAW,CAAC,OAAAoC,CAAM,IAAKzC,EAAK,YAC1B+C,EAAK,IAAIN,CAAM,CAElB,CACD,GAAI,EAAEpC,EAAIwC,EAAG,MAAM,IAAI,MAAM,eAAe,EAC5CC,EAAUC,EACVA,EAAO,IAAI,GACZ,CACF,CAED,SAASC,EAAkB,CAAC,MAAA7B,CAAK,EAAG,CAClC,MAAMd,EAAIf,GAAI6B,EAAOrB,GAAKA,EAAE,KAAK,EAAI,EAC/BmD,GAAMxB,EAAKD,EAAKE,IAAOrB,EAAI,GAC3B6C,EAAU,IAAI,MAAM7C,CAAC,EAC3B,UAAWL,KAAQmB,EAAO,CACxB,MAAM/B,EAAI,KAAK,IAAI,EAAG,KAAK,IAAIiB,EAAI,EAAG,KAAK,MAAMwB,EAAM,KAAK,KAAM7B,EAAMK,CAAC,CAAC,CAAC,CAAC,EAC5EL,EAAK,MAAQZ,EACbY,EAAK,GAAKwB,EAAKpC,EAAI6D,EACnBjD,EAAK,GAAKA,EAAK,GAAK0B,EAChBwB,EAAQ9D,CAAC,EAAG8D,EAAQ9D,CAAC,EAAE,KAAKY,CAAI,EAC/BkD,EAAQ9D,CAAC,EAAI,CAACY,CAAI,CACxB,CACD,GAAI8B,EAAM,UAAWqB,KAAUD,EAC7BC,EAAO,KAAKrB,CAAI,EAElB,OAAOoB,CACR,CAED,SAASE,EAAuBF,EAAS,CACvC,MAAMG,EAAK1D,GAAIuD,EAASI,IAAMjC,EAAKD,GAAMkC,EAAE,OAAS,GAAK1B,GAAMhC,EAAI0D,EAAG7D,EAAK,CAAC,EAC5E,UAAW0B,KAAS+B,EAAS,CAC3B,IAAIK,EAAInC,EACR,UAAWpB,KAAQmB,EAAO,CACxBnB,EAAK,GAAKuD,EACVvD,EAAK,GAAKuD,EAAIvD,EAAK,MAAQqD,EAC3BE,EAAIvD,EAAK,GAAK4B,EACd,UAAWN,KAAQtB,EAAK,YACtBsB,EAAK,MAAQA,EAAK,MAAQ+B,CAE7B,CACDE,GAAKlC,EAAKkC,EAAI3B,IAAOT,EAAM,OAAS,GACpC,QAAS/B,EAAI,EAAGA,EAAI+B,EAAM,OAAQ,EAAE/B,EAAG,CACrC,MAAMY,EAAOmB,EAAM/B,CAAC,EACpBY,EAAK,IAAMuD,GAAKnE,EAAI,GACpBY,EAAK,IAAMuD,GAAKnE,EAAI,EACrB,CACDoE,EAAarC,CAAK,CACnB,CACF,CAED,SAASoB,EAAoB1B,EAAO,CAClC,MAAMqC,EAAUF,EAAkBnC,CAAK,EACvCe,EAAK,KAAK,IAAID,GAAKN,EAAKD,IAAO9B,GAAI4D,EAASI,GAAKA,EAAE,MAAM,EAAI,EAAE,EAC/DF,EAAuBF,CAAO,EAC9B,QAAS9D,EAAI,EAAGA,EAAI6C,EAAY,EAAE7C,EAAG,CACnC,MAAMqE,EAAQ,KAAK,IAAI,IAAMrE,CAAC,EACxBsE,EAAO,KAAK,IAAI,EAAID,GAAQrE,EAAI,GAAK6C,CAAU,EACrD0B,EAAiBT,EAASO,EAAOC,CAAI,EACrCE,EAAiBV,EAASO,EAAOC,CAAI,CACtC,CACF,CAGD,SAASE,EAAiBV,EAASO,EAAOC,EAAM,CAC9C,QAAStE,EAAI,EAAGyD,EAAIK,EAAQ,OAAQ9D,EAAIyD,EAAG,EAAEzD,EAAG,CAC9C,MAAM+D,EAASD,EAAQ9D,CAAC,EACxB,UAAWsD,KAAUS,EAAQ,CAC3B,IAAII,EAAI,EACJM,EAAI,EACR,SAAW,CAAC,OAAApB,EAAQ,MAAAhD,CAAK,IAAKiD,EAAO,YAAa,CAChD,IAAIoB,EAAIrE,GAASiD,EAAO,MAAQD,EAAO,OACvCc,GAAKQ,EAAUtB,EAAQC,CAAM,EAAIoB,EACjCD,GAAKC,CACN,CACD,GAAI,EAAED,EAAI,GAAI,SACd,IAAIlC,GAAM4B,EAAIM,EAAInB,EAAO,IAAMe,EAC/Bf,EAAO,IAAMf,EACbe,EAAO,IAAMf,EACbqC,EAAiBtB,CAAM,CACxB,CACGZ,IAAS,QAAWqB,EAAO,KAAK1C,CAAgB,EACpDwD,EAAkBd,EAAQO,CAAI,CAC/B,CACF,CAGD,SAASC,EAAiBT,EAASO,EAAOC,EAAM,CAC9C,QAASb,EAAIK,EAAQ,OAAQ9D,EAAIyD,EAAI,EAAGzD,GAAK,EAAG,EAAEA,EAAG,CACnD,MAAM+D,EAASD,EAAQ9D,CAAC,EACxB,UAAWqD,KAAUU,EAAQ,CAC3B,IAAII,EAAI,EACJM,EAAI,EACR,SAAW,CAAC,OAAAnB,EAAQ,MAAAjD,CAAK,IAAKgD,EAAO,YAAa,CAChD,IAAIqB,EAAIrE,GAASiD,EAAO,MAAQD,EAAO,OACvCc,GAAKW,EAAUzB,EAAQC,CAAM,EAAIoB,EACjCD,GAAKC,CACN,CACD,GAAI,EAAED,EAAI,GAAI,SACd,IAAIlC,GAAM4B,EAAIM,EAAIpB,EAAO,IAAMgB,EAC/BhB,EAAO,IAAMd,EACbc,EAAO,IAAMd,EACbqC,EAAiBvB,CAAM,CACxB,CACGX,IAAS,QAAWqB,EAAO,KAAK1C,CAAgB,EACpDwD,EAAkBd,EAAQO,CAAI,CAC/B,CACF,CAED,SAASO,EAAkB9C,EAAOsC,EAAO,CACvC,MAAMrE,EAAI+B,EAAM,QAAU,EACpBgD,EAAUhD,EAAM/B,CAAC,EACvBgF,EAA6BjD,EAAOgD,EAAQ,GAAKvC,EAAIxC,EAAI,EAAGqE,CAAK,EACjEY,EAA6BlD,EAAOgD,EAAQ,GAAKvC,EAAIxC,EAAI,EAAGqE,CAAK,EACjEW,EAA6BjD,EAAOE,EAAIF,EAAM,OAAS,EAAGsC,CAAK,EAC/DY,EAA6BlD,EAAOC,EAAI,EAAGqC,CAAK,CACjD,CAGD,SAASY,EAA6BlD,EAAOoC,EAAGnE,EAAGqE,EAAO,CACxD,KAAOrE,EAAI+B,EAAM,OAAQ,EAAE/B,EAAG,CAC5B,MAAMY,EAAOmB,EAAM/B,CAAC,EACduC,GAAM4B,EAAIvD,EAAK,IAAMyD,EACvB9B,EAAK,OAAM3B,EAAK,IAAM2B,EAAI3B,EAAK,IAAM2B,GACzC4B,EAAIvD,EAAK,GAAK4B,CACf,CACF,CAGD,SAASwC,EAA6BjD,EAAOoC,EAAGnE,EAAGqE,EAAO,CACxD,KAAOrE,GAAK,EAAG,EAAEA,EAAG,CAClB,MAAMY,EAAOmB,EAAM/B,CAAC,EACduC,GAAM3B,EAAK,GAAKuD,GAAKE,EACvB9B,EAAK,OAAM3B,EAAK,IAAM2B,EAAI3B,EAAK,IAAM2B,GACzC4B,EAAIvD,EAAK,GAAK4B,CACf,CACF,CAED,SAASoC,EAAiB,CAAC,YAAArB,EAAa,YAAAC,CAAW,EAAG,CACpD,GAAIb,IAAa,OAAW,CAC1B,SAAW,CAAC,OAAQ,CAAC,YAAAY,CAAW,CAAC,IAAKC,EACpCD,EAAY,KAAKjC,EAAsB,EAEzC,SAAW,CAAC,OAAQ,CAAC,YAAAkC,CAAW,CAAC,IAAKD,EACpCC,EAAY,KAAKtC,EAAsB,CAE1C,CACF,CAED,SAASkD,EAAarC,EAAO,CAC3B,GAAIY,IAAa,OACf,SAAW,CAAC,YAAAY,EAAa,YAAAC,CAAW,IAAKzB,EACvCwB,EAAY,KAAKjC,EAAsB,EACvCkC,EAAY,KAAKtC,EAAsB,CAG5C,CAGD,SAASyD,EAAUtB,EAAQC,EAAQ,CACjC,IAAIa,EAAId,EAAO,IAAMA,EAAO,YAAY,OAAS,GAAKb,EAAK,EAC3D,SAAW,CAAC,OAAQ5B,EAAM,MAAAsE,CAAK,IAAK7B,EAAO,YAAa,CACtD,GAAIzC,IAAS0C,EAAQ,MACrBa,GAAKe,EAAQ1C,CACd,CACD,SAAW,CAAC,OAAQ5B,EAAM,MAAAsE,CAAK,IAAK5B,EAAO,YAAa,CACtD,GAAI1C,IAASyC,EAAQ,MACrBc,GAAKe,CACN,CACD,OAAOf,CACR,CAGD,SAASW,EAAUzB,EAAQC,EAAQ,CACjC,IAAIa,EAAIb,EAAO,IAAMA,EAAO,YAAY,OAAS,GAAKd,EAAK,EAC3D,SAAW,CAAC,OAAQ5B,EAAM,MAAAsE,CAAK,IAAK5B,EAAO,YAAa,CACtD,GAAI1C,IAASyC,EAAQ,MACrBc,GAAKe,EAAQ1C,CACd,CACD,SAAW,CAAC,OAAQ5B,EAAM,MAAAsE,CAAK,IAAK7B,EAAO,YAAa,CACtD,GAAIzC,IAAS0C,EAAQ,MACrBa,GAAKe,CACN,CACD,OAAOf,CACR,CAED,OAAOrB,CACT,CChXA,IAAIqC,GAAK,KAAK,GACVC,GAAM,EAAID,GACVE,EAAU,KACVC,GAAaF,GAAMC,EAEvB,SAASE,IAAO,CACd,KAAK,IAAM,KAAK,IAChB,KAAK,IAAM,KAAK,IAAM,KACtB,KAAK,EAAI,EACX,CAEA,SAASC,IAAO,CACd,OAAO,IAAID,EACb,CAEAA,GAAK,UAAYC,GAAK,UAAY,CAChC,YAAaD,GACb,OAAQ,SAAStE,EAAGkD,EAAG,CACrB,KAAK,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAAClD,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAACkD,EAC5E,EACD,UAAW,UAAW,CAChB,KAAK,MAAQ,OACf,KAAK,IAAM,KAAK,IAAK,KAAK,IAAM,KAAK,IACrC,KAAK,GAAK,IAEb,EACD,OAAQ,SAASlD,EAAGkD,EAAG,CACrB,KAAK,GAAK,KAAO,KAAK,IAAM,CAAClD,GAAK,KAAO,KAAK,IAAM,CAACkD,EACtD,EACD,iBAAkB,SAAS9B,EAAIJ,EAAIhB,EAAGkD,EAAG,CACvC,KAAK,GAAK,KAAO,CAAC9B,EAAM,KAAO,CAACJ,EAAM,KAAO,KAAK,IAAM,CAAChB,GAAK,KAAO,KAAK,IAAM,CAACkD,EAClF,EACD,cAAe,SAAS9B,EAAIJ,EAAIwD,EAAIC,EAAIzE,EAAGkD,EAAG,CAC5C,KAAK,GAAK,KAAO,CAAC9B,EAAM,KAAO,CAACJ,EAAM,KAAO,CAACwD,EAAM,KAAO,CAACC,EAAM,KAAO,KAAK,IAAM,CAACzE,GAAK,KAAO,KAAK,IAAM,CAACkD,EAC9G,EACD,MAAO,SAAS9B,EAAIJ,EAAIwD,EAAIC,EAAIC,EAAG,CACjCtD,EAAK,CAACA,EAAIJ,EAAK,CAACA,EAAIwD,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIC,EAAI,CAACA,EAC7C,IAAIvD,EAAK,KAAK,IACVJ,EAAK,KAAK,IACV4D,EAAMH,EAAKpD,EACXwD,EAAMH,EAAKzD,EACX6D,EAAM1D,EAAKC,EACX0D,EAAM/D,EAAKC,EACX+D,EAAQF,EAAMA,EAAMC,EAAMA,EAG9B,GAAIJ,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAsBA,CAAC,EAGlD,GAAI,KAAK,MAAQ,KACf,KAAK,GAAK,KAAO,KAAK,IAAMtD,GAAM,KAAO,KAAK,IAAMJ,WAI3C+D,EAAQX,EAKd,GAAI,EAAE,KAAK,IAAIU,EAAMH,EAAMC,EAAMC,CAAG,EAAIT,IAAY,CAACM,EACxD,KAAK,GAAK,KAAO,KAAK,IAAMtD,GAAM,KAAO,KAAK,IAAMJ,OAIjD,CACH,IAAIgE,EAAMR,EAAKrD,EACX8D,EAAMR,EAAK1D,EACXmE,EAAQP,EAAMA,EAAMC,EAAMA,EAC1BO,EAAQH,EAAMA,EAAMC,EAAMA,EAC1BG,EAAM,KAAK,KAAKF,CAAK,EACrBG,EAAM,KAAK,KAAKN,CAAK,EACrBO,EAAIZ,EAAI,KAAK,KAAKR,GAAK,KAAK,MAAMgB,EAAQH,EAAQI,IAAU,EAAIC,EAAMC,EAAI,GAAK,CAAC,EAChFE,EAAMD,EAAID,EACVG,EAAMF,EAAIF,EAGV,KAAK,IAAIG,EAAM,CAAC,EAAInB,IACtB,KAAK,GAAK,KAAOhD,EAAKmE,EAAMV,GAAO,KAAO7D,EAAKuE,EAAMT,IAGvD,KAAK,GAAK,IAAMJ,EAAI,IAAMA,EAAI,SAAW,EAAEI,EAAME,EAAMH,EAAMI,GAAQ,KAAO,KAAK,IAAM7D,EAAKoE,EAAMb,GAAO,KAAO,KAAK,IAAM3D,EAAKwE,EAAMZ,EACvI,CACF,EACD,IAAK,SAAS5E,EAAGkD,EAAGwB,EAAGe,EAAIC,EAAIC,EAAK,CAClC3F,EAAI,CAACA,EAAGkD,EAAI,CAACA,EAAGwB,EAAI,CAACA,EAAGiB,EAAM,CAAC,CAACA,EAChC,IAAItE,EAAKqD,EAAI,KAAK,IAAIe,CAAE,EACpBnE,EAAKoD,EAAI,KAAK,IAAIe,CAAE,EACpBtE,EAAKnB,EAAIqB,EACTN,EAAKmC,EAAI5B,EACTsE,EAAK,EAAID,EACTE,EAAKF,EAAMF,EAAKC,EAAKA,EAAKD,EAG9B,GAAIf,EAAI,EAAG,MAAM,IAAI,MAAM,oBAAsBA,CAAC,EAG9C,KAAK,MAAQ,KACf,KAAK,GAAK,IAAMvD,EAAK,IAAMJ,GAIpB,KAAK,IAAI,KAAK,IAAMI,CAAE,EAAIiD,GAAW,KAAK,IAAI,KAAK,IAAMrD,CAAE,EAAIqD,KACtE,KAAK,GAAK,IAAMjD,EAAK,IAAMJ,GAIxB2D,IAGDmB,EAAK,IAAGA,EAAKA,EAAK1B,GAAMA,IAGxB0B,EAAKxB,GACP,KAAK,GAAK,IAAMK,EAAI,IAAMA,EAAI,QAAUkB,EAAK,KAAO5F,EAAIqB,GAAM,KAAO6B,EAAI5B,GAAM,IAAMoD,EAAI,IAAMA,EAAI,QAAUkB,EAAK,KAAO,KAAK,IAAMzE,GAAM,KAAO,KAAK,IAAMJ,GAIrJ8E,EAAKzB,IACZ,KAAK,GAAK,IAAMM,EAAI,IAAMA,EAAI,OAAS,EAAEmB,GAAM3B,IAAO,IAAM0B,EAAK,KAAO,KAAK,IAAM5F,EAAI0E,EAAI,KAAK,IAAIgB,CAAE,GAAK,KAAO,KAAK,IAAMxC,EAAIwB,EAAI,KAAK,IAAIgB,CAAE,IAEnJ,EACD,KAAM,SAAS1F,EAAGkD,EAAGM,EAAGsC,EAAG,CACzB,KAAK,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAAC9F,GAAK,KAAO,KAAK,IAAM,KAAK,IAAM,CAACkD,GAAK,KAAO,CAACM,EAAK,KAAO,CAACsC,EAAK,IAAO,CAACtC,EAAK,GACxH,EACD,SAAU,UAAW,CACnB,OAAO,KAAK,CACb,CACH,EC/He,SAAQzD,GAACC,EAAG,CACzB,OAAO,UAAoB,CACzB,OAAOA,CACX,CACA,CCJO,SAASA,GAAE+F,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CAEO,SAAS7C,GAAE6C,EAAG,CACnB,OAAOA,EAAE,CAAC,CACZ,CCNO,IAAIC,GAAQ,MAAM,UAAU,MCMnC,SAASC,GAAWxG,EAAG,CACrB,OAAOA,EAAE,MACX,CAEA,SAASyG,GAAWzG,EAAG,CACrB,OAAOA,EAAE,MACX,CAEA,SAASwB,GAAKkF,EAAO,CACnB,IAAI/D,EAAS6D,GACT5D,EAAS6D,GACTlG,EAAIoG,GACJlD,EAAImD,GACJC,EAAU,KAEd,SAASrF,GAAO,CACd,IAAIsF,EAAQC,EAAOR,GAAM,KAAK,SAAS,EAAGS,EAAIrE,EAAO,MAAM,KAAMoE,CAAI,EAAGE,EAAIrE,EAAO,MAAM,KAAMmE,CAAI,EAGnG,GAFKF,IAASA,EAAUC,EAAShC,GAAI,GACrC4B,EAAMG,EAAS,CAACtG,EAAE,MAAM,MAAOwG,EAAK,CAAC,EAAIC,EAAGD,EAAM,EAAE,CAACtD,EAAE,MAAM,KAAMsD,CAAI,EAAG,CAACxG,EAAE,MAAM,MAAOwG,EAAK,CAAC,EAAIE,EAAGF,EAAM,EAAE,CAACtD,EAAE,MAAM,KAAMsD,CAAI,CAAC,EAC/HD,EAAQ,OAAOD,EAAU,KAAMC,EAAS,IAAM,IACnD,CAED,OAAAtF,EAAK,OAAS,SAASkB,EAAG,CACxB,OAAO,UAAU,QAAUC,EAASD,EAAGlB,GAAQmB,CACnD,EAEEnB,EAAK,OAAS,SAASkB,EAAG,CACxB,OAAO,UAAU,QAAUE,EAASF,EAAGlB,GAAQoB,CACnD,EAEEpB,EAAK,EAAI,SAASkB,EAAG,CACnB,OAAO,UAAU,QAAUnC,EAAI,OAAOmC,GAAM,WAAaA,EAAIpC,GAAS,CAACoC,CAAC,EAAGlB,GAAQjB,CACvF,EAEEiB,EAAK,EAAI,SAASkB,EAAG,CACnB,OAAO,UAAU,QAAUe,EAAI,OAAOf,GAAM,WAAaA,EAAIpC,GAAS,CAACoC,CAAC,EAAGlB,GAAQiC,CACvF,EAEEjC,EAAK,QAAU,SAASkB,EAAG,CACzB,OAAO,UAAU,QAAWmE,EAAUnE,GAAY,KAAWlB,GAAQqF,CACzE,EAESrF,CACT,CAEA,SAAS0F,GAAgBL,EAASnF,EAAIJ,EAAIK,EAAIJ,EAAI,CAChDsF,EAAQ,OAAOnF,EAAIJ,CAAE,EACrBuF,EAAQ,cAAcnF,GAAMA,EAAKC,GAAM,EAAGL,EAAII,EAAIH,EAAII,EAAIJ,CAAE,CAC9D,CAgBO,SAAS4F,IAAiB,CAC/B,OAAO3F,GAAK0F,EAAe,CAC7B,CCtEA,SAASE,GAAiBpH,EAAG,CAC3B,MAAO,CAACA,EAAE,OAAO,GAAIA,EAAE,EAAE,CAC3B,CAEA,SAASqH,GAAiBrH,EAAG,CAC3B,MAAO,CAACA,EAAE,OAAO,GAAIA,EAAE,EAAE,CAC3B,CAEe,SAAAsH,IAAW,CACxB,OAAOH,GAAgB,EAClB,OAAOC,EAAgB,EACvB,OAAOC,EAAgB,CAC9B,CCEA,IAAIE,GAAS,UAAW,CACtB,IAAIC,EAAoBC,EAAO,SAAS,EAAGzD,EAAG0D,EAAI,EAAG,CACnD,IAAKA,EAAKA,GAAM,CAAE,EAAE,EAAI,EAAE,OAAQ,IAAKA,EAAG,EAAE,CAAC,CAAC,EAAI1D,EAAG,CACrD,OAAO0D,CACX,EAAK,GAAG,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAG,GAAI,EAAE,EACrDC,EAAU,CACZ,MAAuBL,EAAO,UAAiB,CAC9C,EAAE,OAAO,EACV,GAAI,CAAE,EACN,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,OAAU,EAAG,QAAW,EAAG,IAAO,EAAG,QAAW,EAAG,OAAU,EAAG,SAAY,EAAG,IAAO,GAAI,gBAAiB,GAAI,MAAS,GAAI,gBAAiB,GAAI,eAAgB,GAAI,MAAS,GAAI,QAAW,GAAI,YAAe,GAAI,OAAU,GAAI,aAAgB,GAAI,iBAAoB,GAAI,QAAW,EAAG,KAAQ,CAAG,EAC3U,WAAY,CAAE,EAAG,QAAS,EAAG,SAAU,EAAG,UAAW,GAAI,MAAO,GAAI,gBAAiB,GAAI,QAAS,GAAI,gBAAiB,GAAI,eAAgB,GAAI,SAAU,GAAI,eAAgB,GAAI,kBAAoB,EACrM,aAAc,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,CAAC,EAC5G,cAA+BA,EAAO,SAAmBM,EAAQC,EAAQC,EAAUC,EAAIC,EAASC,EAAIC,EAAI,CACtG,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAO,CACb,IAAK,GACH,MAAMxF,EAASuF,EAAG,iBAAiBE,EAAGE,EAAK,CAAC,EAAE,KAAM,EAAC,WAAW,KAAM,GAAG,CAAC,EACpE1F,EAASsF,EAAG,iBAAiBE,EAAGE,EAAK,CAAC,EAAE,KAAM,EAAC,WAAW,KAAM,GAAG,CAAC,EACpE3I,EAAQ,WAAWyI,EAAGE,CAAE,EAAE,KAAI,CAAE,EACtCJ,EAAG,QAAQvF,EAAQC,EAAQjD,CAAK,EAChC,MACF,IAAK,GACL,IAAK,GACL,IAAK,IACH,KAAK,EAAIyI,EAAGE,CAAE,EACd,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAClB,KACH,CACF,EAAE,WAAW,EACd,MAAO,CAAC,CAAE,EAAG,EAAG,EAAG,CAAC,EAAG,CAAC,GAAK,CAAE,EAAG,CAAC,CAAC,CAAG,EAAE,CAAE,EAAG,CAAC,EAAG,CAAC,GAAK,CAAE,EAAG,EAAG,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAIX,EAAK,GAAIC,CAAK,EAAE,CAAE,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,GAAI,GAAI,CAAC,EAAG,EAAE,CAAC,EAAIJ,EAAEI,EAAK,CAAC,EAAG,CAAC,EAAG,CAAE,EAAG,GAAI,EAAG,CAAC,EAAG,EAAE,CAAG,CAAA,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAG,EAAEJ,EAAEK,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGL,EAAEK,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAC,EAAIL,EAAEK,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAC,EAAI,CAAE,EAAG,CAAC,EAAG,CAAC,CAAC,EAAIL,EAAEI,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG,CAAE,EAAG,GAAI,EAAG,EAAG,GAAI,EAAG,GAAI,EAAG,GAAI,EAAG,GAAID,EAAK,GAAIC,CAAK,EAAE,CAAE,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAID,EAAK,GAAIC,CAAG,EAAI,CAAE,GAAI,CAAC,EAAG,EAAE,CAAC,EAAIJ,EAAEI,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAG,EAAEJ,EAAEK,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,GAAI,GAAI,EAAG,GAAI,EAAG,GAAIF,EAAK,GAAIC,CAAG,EAAIJ,EAAE,CAAC,EAAG,EAAG,EAAE,EAAG,CAAC,EAAG,CAAC,CAAC,CAAC,EACliB,eAAgB,CAAE,GAAI,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,CAAC,CAAG,EAC1C,WAA4BC,EAAO,SAAoBc,EAAKC,EAAM,CAChE,GAAIA,EAAK,YACP,KAAK,MAAMD,CAAG,MACT,CACL,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACP,CACF,EAAE,YAAY,EACf,MAAuBhB,EAAO,SAAeiB,EAAO,CAC/C,IAACC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAE,EAAEC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAE,EAAEC,EAAQ,KAAK,MAAOjB,EAAS,GAAIE,EAAW,EAAGD,EAAS,EAAmBiB,EAAS,EAAGC,EAAM,EAClKC,EAAOJ,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCK,EAAS,OAAO,OAAO,KAAK,KAAK,EACjCC,EAAc,CAAE,GAAI,CAAA,GACxB,QAASC,KAAK,KAAK,GACb,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,CAAC,IACjDD,EAAY,GAAGC,CAAC,EAAI,KAAK,GAAGA,CAAC,GAGjCF,EAAO,SAASV,EAAOW,EAAY,EAAE,EACrCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAO,OAAU,MAC1BA,EAAO,OAAS,IAElB,IAAIG,EAAQH,EAAO,OACnBL,EAAO,KAAKQ,CAAK,EACjB,IAAIC,EAASJ,EAAO,SAAWA,EAAO,QAAQ,OAC1C,OAAOC,EAAY,GAAG,YAAe,WACvC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAEhD,SAASI,EAAS1G,EAAG,CACnB6F,EAAM,OAASA,EAAM,OAAS,EAAI7F,EAClC+F,EAAO,OAASA,EAAO,OAAS/F,EAChCgG,EAAO,OAASA,EAAO,OAAShG,CACjC,CACD0E,EAAOgC,EAAU,UAAU,EAC3B,SAASC,GAAM,CACb,IAAIC,EACJ,OAAAA,EAAQd,EAAO,IAAG,GAAMO,EAAO,IAAK,GAAIF,EACpC,OAAOS,GAAU,WACfA,aAAiB,QACnBd,EAASc,EACTA,EAAQd,EAAO,OAEjBc,EAAQhB,EAAK,SAASgB,CAAK,GAAKA,GAE3BA,CACR,CACDlC,EAAOiC,EAAK,KAAK,EAEjB,QADIE,EAAwBC,EAAOC,EAAW7E,EAAG8E,EAAQ,CAAE,EAAEzD,EAAG0D,EAAKC,EAAUC,IAClE,CAUX,GATAL,EAAQjB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAeiB,CAAK,EAC3BC,EAAS,KAAK,eAAeD,CAAK,IAE9BD,IAAW,MAAQ,OAAOA,EAAU,OACtCA,EAASF,EAAG,GAEdI,EAASd,EAAMa,CAAK,GAAKb,EAAMa,CAAK,EAAED,CAAM,GAE1C,OAAOE,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CACjE,IAAIK,EAAS,GACbD,EAAW,CAAA,EACX,IAAK5D,KAAK0C,EAAMa,CAAK,EACf,KAAK,WAAWvD,CAAC,GAAKA,EAAI2C,GAC5BiB,EAAS,KAAK,IAAM,KAAK,WAAW5D,CAAC,EAAI,GAAG,EAG5C8C,EAAO,aACTe,EAAS,wBAA0BlC,EAAW,GAAK;AAAA,EAAQmB,EAAO,aAAc,EAAG;AAAA,YAAiBc,EAAS,KAAK,IAAI,EAAI,WAAa,KAAK,WAAWN,CAAM,GAAKA,GAAU,IAE5KO,EAAS,wBAA0BlC,EAAW,GAAK,iBAAmB2B,GAAUV,EAAM,eAAiB,KAAO,KAAK,WAAWU,CAAM,GAAKA,GAAU,KAErJ,KAAK,WAAWO,EAAQ,CACtB,KAAMf,EAAO,MACb,MAAO,KAAK,WAAWQ,CAAM,GAAKA,EAClC,KAAMR,EAAO,SACb,IAAKG,EACL,SAAAW,CACZ,CAAW,CACF,CACD,GAAIJ,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAChD,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcD,CAAM,EAEpG,OAAQE,EAAO,CAAC,EAAC,CACf,IAAK,GACHlB,EAAM,KAAKgB,CAAM,EACjBd,EAAO,KAAKM,EAAO,MAAM,EACzBL,EAAO,KAAKK,EAAO,MAAM,EACzBR,EAAM,KAAKkB,EAAO,CAAC,CAAC,EACpBF,EAAS,KAEP5B,EAASoB,EAAO,OAChBrB,EAASqB,EAAO,OAChBnB,EAAWmB,EAAO,SAClBG,EAAQH,EAAO,OAQjB,MACF,IAAK,GAwBH,GAvBAY,EAAM,KAAK,aAAaF,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCC,EAAM,EAAIjB,EAAOA,EAAO,OAASkB,CAAG,EACpCD,EAAM,GAAK,CACT,WAAYhB,EAAOA,EAAO,QAAUiB,GAAO,EAAE,EAAE,WAC/C,UAAWjB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUiB,GAAO,EAAE,EAAE,aACjD,YAAajB,EAAOA,EAAO,OAAS,CAAC,EAAE,WACrD,EACgBS,IACFO,EAAM,GAAG,MAAQ,CACfhB,EAAOA,EAAO,QAAUiB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1CjB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACjD,GAEY9D,EAAI,KAAK,cAAc,MAAM8E,EAAO,CAClChC,EACAC,EACAC,EACAoB,EAAY,GACZS,EAAO,CAAC,EACRhB,EACAC,CACd,EAAc,OAAOI,CAAI,CAAC,EACV,OAAOlE,EAAM,IACf,OAAOA,EAEL+E,IACFpB,EAAQA,EAAM,MAAM,EAAG,GAAKoB,EAAM,CAAC,EACnClB,EAASA,EAAO,MAAM,EAAG,GAAKkB,CAAG,EACjCjB,EAASA,EAAO,MAAM,EAAG,GAAKiB,CAAG,GAEnCpB,EAAM,KAAK,KAAK,aAAakB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1ChB,EAAO,KAAKiB,EAAM,CAAC,EACnBhB,EAAO,KAAKgB,EAAM,EAAE,EACpBE,EAAWjB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAKqB,CAAQ,EACnB,MACF,IAAK,GACH,MAAO,EACV,CACF,CACD,MAAO,EACR,EAAE,OAAO,CACd,EACMG,EAAwB,UAAW,CACrC,IAAIhB,EAAS,CACX,IAAK,EACL,WAA4B3B,EAAO,SAAoBc,EAAKC,EAAM,CAChE,GAAI,KAAK,GAAG,OACV,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAEtB,EAAE,YAAY,EAEf,SAA0Bd,EAAO,SAASiB,EAAOR,EAAI,CACnD,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAA,EAC3B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACZ,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACvB,EACY,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,EAAG,CAAC,GAE3B,KAAK,OAAS,EACP,IACR,EAAE,UAAU,EAEb,MAAuBjB,EAAO,UAAW,CACvC,IAAI4C,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACF,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEV,KAAK,QAAQ,QACf,KAAK,OAAO,MAAM,CAAC,IAErB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACR,EAAE,OAAO,EAEV,MAAuB5C,EAAO,SAAS4C,EAAI,CACzC,IAAIL,EAAMK,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EACpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASL,CAAG,EAC5D,KAAK,QAAUA,EACf,IAAIO,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EACzDD,EAAM,OAAS,IACjB,KAAK,UAAYA,EAAM,OAAS,GAElC,IAAIrF,EAAI,KAAK,OAAO,MACpB,YAAK,OAAS,CACZ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaqF,GAASA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAAKA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAAS,KAAK,OAAO,aAAeN,CAClM,EACY,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC/E,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAAS+E,CAAG,GAErD,KAAK,OAAS,KAAK,OAAO,OACnB,IACR,EAAE,OAAO,EAEV,KAAsBvC,EAAO,UAAW,CACtC,YAAK,MAAQ,GACN,IACR,EAAE,MAAM,EAET,OAAwBA,EAAO,UAAW,CACxC,GAAI,KAAK,QAAQ,gBACf,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,eAAgB,CAChO,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACvB,CAAW,EAEH,OAAO,IACR,EAAE,QAAQ,EAEX,KAAsBA,EAAO,SAAS1E,EAAG,CACvC,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAC/B,EAAE,MAAM,EAET,UAA2B0E,EAAO,UAAW,CAC3C,IAAI+C,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAQ,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC5E,EAAE,WAAW,EAEd,cAA+B/C,EAAO,UAAW,CAC/C,IAAIxE,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KAChBA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAKA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAG,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAChF,EAAE,eAAe,EAElB,aAA8BwE,EAAO,UAAW,CAC9C,IAAIgD,EAAM,KAAK,YACXjH,EAAI,IAAI,MAAMiH,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAe,EAAG;AAAA,EAAOjH,EAAI,GAChD,EAAE,cAAc,EAEjB,WAA4BiE,EAAO,SAASiD,EAAOC,EAAc,CAC/D,IAAIhB,EAAOW,EAAOM,EAmDlB,GAlDI,KAAK,QAAQ,kBACfA,EAAS,CACP,SAAU,KAAK,SACf,OAAQ,CACN,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC1B,EACD,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACvB,EACc,KAAK,QAAQ,SACfA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAGnDN,EAAQI,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCJ,IACF,KAAK,UAAYA,EAAM,QAEzB,KAAK,OAAS,CACZ,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EAAQA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAAS,KAAK,OAAO,YAAcI,EAAM,CAAC,EAAE,MACvJ,EACQ,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAE9D,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBf,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMgB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SACpB,KAAK,KAAO,IAEVhB,EACF,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1B,QAASL,KAAKsB,EACZ,KAAKtB,CAAC,EAAIsB,EAAOtB,CAAC,EAEpB,MAAO,EACR,CACD,MAAO,EACR,EAAE,YAAY,EAEf,KAAsB7B,EAAO,UAAW,CACtC,GAAI,KAAK,KACP,OAAO,KAAK,IAET,KAAK,SACR,KAAK,KAAO,IAEd,IAAIkC,EAAOe,EAAOG,EAAWjL,EACxB,KAAK,QACR,KAAK,OAAS,GACd,KAAK,MAAQ,IAGf,QADIkL,EAAQ,KAAK,gBACRxL,EAAI,EAAGA,EAAIwL,EAAM,OAAQxL,IAEhC,GADAuL,EAAY,KAAK,OAAO,MAAM,KAAK,MAAMC,EAAMxL,CAAC,CAAC,CAAC,EAC9CuL,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGzD,GAFAA,EAAQG,EACRjL,EAAQN,EACJ,KAAK,QAAQ,gBAAiB,CAEhC,GADAqK,EAAQ,KAAK,WAAWkB,EAAWC,EAAMxL,CAAC,CAAC,EACvCqK,IAAU,GACZ,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1Be,EAAQ,GACR,QAChB,KACgB,OAAO,EAEV,SAAU,CAAC,KAAK,QAAQ,KACvB,MAIN,OAAIA,GACFf,EAAQ,KAAK,WAAWe,EAAOI,EAAMlL,CAAK,CAAC,EACvC+J,IAAU,GACLA,EAEF,IAEL,KAAK,SAAW,GACX,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,eAAgB,CACtH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACvB,CAAW,CAEJ,EAAE,MAAM,EAET,IAAqBlC,EAAO,UAAe,CACzC,IAAIxC,EAAI,KAAK,OACb,OAAIA,GAGK,KAAK,KAEf,EAAE,KAAK,EAER,MAAuBwC,EAAO,SAAesD,EAAW,CACtD,KAAK,eAAe,KAAKA,CAAS,CACnC,EAAE,OAAO,EAEV,SAA0BtD,EAAO,UAAoB,CACnD,IAAI1E,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACC,KAAK,eAAe,MAEpB,KAAK,eAAe,CAAC,CAE/B,EAAE,UAAU,EAEb,cAA+B0E,EAAO,UAAyB,CAC7D,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAC3E,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAErC,EAAE,eAAe,EAElB,SAA0BA,EAAO,SAAkB1E,EAAG,CAEpD,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACA,KAAK,eAAeA,CAAC,EAErB,SAEV,EAAE,UAAU,EAEb,UAA2B0E,EAAO,SAAmBsD,EAAW,CAC9D,KAAK,MAAMA,CAAS,CACrB,EAAE,WAAW,EAEd,eAAgCtD,EAAO,UAA0B,CAC/D,OAAO,KAAK,eAAe,MAC5B,EAAE,gBAAgB,EACnB,QAAS,CAAE,mBAAoB,EAAM,EACrC,cAA+BA,EAAO,SAAmBS,EAAI8C,EAAKC,EAA2BC,EAAU,CAErG,OAAQD,EAAyB,CAC/B,IAAK,GACH,YAAK,UAAU,KAAK,EACb,EAET,IAAK,GACH,MAAO,IAET,IAAK,GACH,MAAO,GAET,IAAK,GACH,MAAO,IAET,IAAK,GACH,YAAK,UAAU,cAAc,EACtB,GAET,IAAK,GACH,MAAO,IAET,IAAK,GACH,YAAK,SAAS,cAAc,EACrB,GAET,IAAK,GACH,MAAO,GAEV,CACF,EAAE,WAAW,EACd,MAAO,CAAC,sBAAuB,UAAW,kCAAmC,iBAAkB,iBAAkB,qDAAsD,6BAA8B,kGAAkG,EACvS,WAAY,CAAE,IAAO,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAAG,UAAa,EAAK,EAAI,aAAgB,CAAE,MAAS,CAAC,EAAG,CAAC,EAAG,UAAa,EAAO,EAAE,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,CAAC,EAAG,UAAa,GAAQ,CACjN,EACI,OAAO7B,CACX,IACEtB,EAAQ,MAAQsC,EAChB,SAASe,GAAS,CAChB,KAAK,GAAK,EACX,CACD,OAAA1D,EAAO0D,EAAQ,QAAQ,EACvBA,EAAO,UAAYrD,EACnBA,EAAQ,OAASqD,EACV,IAAIA,CACb,IACA5D,GAAO,OAASA,GAChB,IAAI6D,EAAiB7D,GAGjBrF,EAAQ,CAAA,EACRb,EAAQ,CAAA,EACRgK,EAA2B,IAAI,IAC/BC,GAAyB7D,EAAO,IAAM,CACxCvF,EAAQ,CAAA,EACRb,EAAQ,CAAA,EACRgK,EAA2B,IAAI,IAC/BE,IACF,EAAG,OAAO,EACNC,GAAa,KAAM,CACrB,YAAY7I,EAAQC,EAAQjD,EAAQ,EAAG,CACrC,KAAK,OAASgD,EACd,KAAK,OAASC,EACd,KAAK,MAAQjD,CACd,CACD,MAAA,CACE8H,EAAO,KAAM,YAAY,CAC1B,CACH,EACIgE,GAA0BhE,EAAO,CAAC9E,EAAQC,EAAQjD,IAAU,CAC9DuC,EAAM,KAAK,IAAIsJ,GAAW7I,EAAQC,EAAQjD,CAAK,CAAC,CAClD,EAAG,SAAS,EACR+L,GAAa,KAAM,CACrB,YAAYC,EAAI,CACd,KAAK,GAAKA,CACX,CACD,MAAA,CACElE,EAAO,KAAM,YAAY,CAC1B,CACH,EACImE,GAAmCnE,EAAQkE,GAAO,CACpDA,EAAKE,GAAe,aAAaF,EAAIG,GAAW,CAAA,EAChD,IAAI5L,EAAOmL,EAAS,IAAIM,CAAE,EAC1B,OAAIzL,IAAS,SACXA,EAAO,IAAIwL,GAAWC,CAAE,EACxBN,EAAS,IAAIM,EAAIzL,CAAI,EACrBmB,EAAM,KAAKnB,CAAI,GAEVA,CACT,EAAG,kBAAkB,EACjB6L,GAA2BtE,EAAO,IAAMpG,EAAO,UAAU,EACzD2K,GAA2BvE,EAAO,IAAMvF,EAAO,UAAU,EACzD+J,GAA2BxE,EAAO,KAAO,CAC3C,MAAOpG,EAAM,IAAKnB,IAAU,CAAE,GAAIA,EAAK,EAAE,EAAG,EAC5C,MAAOgC,EAAM,IAAKV,IAAU,CAC1B,OAAQA,EAAK,OAAO,GACpB,OAAQA,EAAK,OAAO,GACpB,MAAOA,EAAK,KAChB,EAAI,CACJ,GAAI,UAAU,EACV0K,GAAmB,CACrB,SAAAb,EACA,UAA2B5D,EAAO,IAAMqE,GAAS,EAAG,OAAQ,WAAW,EACvE,SAAAC,GACA,SAAAC,GACA,SAAAC,GACA,QAAAR,GACA,iBAAAG,GACA,YAAAO,GACA,YAAAC,GACA,kBAAAC,GACA,kBAAAC,GACA,gBAAAC,GACA,gBAAAC,GACA,MAAOlB,EACT,EAkBImB,GAAM,MAAMC,EAAK,CACnB,MAAA,CACEjF,EAAO,KAAM,KAAK,CACnB,CACD,MAAA,CACE,KAAK,MAAQ,CACd,CACD,OAAO,KAAKkF,EAAM,CAChB,OAAO,IAAID,GAAKC,GAAO,EAAED,GAAK,KAAK,CACpC,CACD,YAAYvL,EAAI,CACd,KAAK,GAAKA,EACV,KAAK,KAAO,IAAIA,CAAE,EACnB,CACD,UAAW,CACT,MAAO,OAAS,KAAK,KAAO,GAC7B,CACH,EAGIyL,GAAgB,CAClB,KAAMC,GACN,MAAOC,GACP,OAAQC,GACR,QAASC,EACX,EACIC,GAAuBxF,EAAO,SAASyF,EAAM/L,EAAIgM,EAAUC,EAAS,CACtE,KAAM,CAAE,cAAAC,EAAe,OAAQC,CAAM,EAAGxB,GAAS,EAC3CyB,EAAsBC,GAAc,OAC1C,IAAIC,EACAJ,IAAkB,YACpBI,EAAiBC,EAAS,KAAOvM,CAAE,GAErC,MAAMwM,EAAON,IAAkB,UAAYK,EAASD,EAAe,MAAK,EAAG,CAAC,EAAE,gBAAgB,IAAI,EAAIC,EAAS,MAAM,EAC/GE,EAAMP,IAAkB,UAAYM,EAAK,OAAO,QAAQxM,CAAE,IAAI,EAAIuM,EAAS,QAAQvM,CAAE,IAAI,EACzFqD,EAAQ8I,GAAM,OAASC,EAAoB,MAC3CM,EAASP,GAAM,QAAUC,EAAoB,MAC7CO,EAAcR,GAAM,aAAeC,EAAoB,YACvDQ,EAAgBT,GAAM,eAAiBC,EAAoB,cAC3DS,EAASV,GAAM,QAAUC,EAAoB,OAC7CU,EAASX,GAAM,QAAUC,EAAoB,OAC7CW,EAAaZ,GAAM,YAAcC,EAAoB,WACrDxM,EAAQqM,EAAQ,GAAG,SAAQ,EAC3Be,EAAYvB,GAAcmB,CAAa,EAE9BK,GAAQ,EAAG,OAAQ,GAAM,EAAE,EAAE,EAAE,UAD5B,EAC+C,EAAE,YAAY,IAAMF,EAAa,GAAK,EAAE,EAAE,UAAUC,CAAS,EAAE,OAAO,CACrI,CAAC,EAAG,CAAC,EACL,CAAC3J,EAAOqJ,CAAM,CAClB,CAAG,EACM9M,CAAK,EACZ,MAAMsN,EAAcC,GAAe/O,EAAiB,EACpDqO,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAAE,UAAU,OAAO,EAAE,KAAK7M,EAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,QAAS,MAAM,EAAE,KAAK,KAAO,IAAO,EAAE,IAAM0L,GAAI,KAAK,OAAO,GAAG,EAAE,EAAE,KAAK,YAAa,SAAS,EAAG,CAC1L,MAAO,aAAe,EAAE,GAAK,IAAM,EAAE,GAAK,GAC9C,CAAG,EAAE,KAAK,IAAM,GAAM,EAAE,EAAE,EAAE,KAAK,IAAM,GAAM,EAAE,EAAE,EAAE,OAAO,MAAM,EAAE,KAAK,SAAW,GACvE,EAAE,GAAK,EAAE,EACjB,EAAE,KAAK,QAAU,GAAM,EAAE,GAAK,EAAE,EAAE,EAAE,KAAK,OAAS,GAAM4B,EAAY,EAAE,EAAE,CAAC,EAC1E,MAAME,EAA0B9G,EAAO,CAAC,CAAE,GAAI+G,EAAK,MAAA7O,KAC5CuO,EAGE,GAAGM,CAAG;AAAA,EACfR,CAAM,GAAG,KAAK,MAAMrO,EAAQ,GAAG,EAAI,GAAG,GAAGsO,CAAM,GAHpCO,EAIR,SAAS,EACZZ,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,aAAa,EAAE,KAAK,YAAa,EAAE,EAAE,UAAU,MAAM,EAAE,KAAK7M,EAAM,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,IAAM,GAAM,EAAE,GAAKyD,EAAQ,EAAI,EAAE,GAAK,EAAI,EAAE,GAAK,CAAC,EAAE,KAAK,IAAM,IAAO,EAAE,GAAK,EAAE,IAAM,CAAC,EAAE,KAAK,KAAM,GAAG0J,EAAa,IAAM,MAAM,IAAI,EAAE,KAAK,cAAgB,GAAM,EAAE,GAAK1J,EAAQ,EAAI,QAAU,KAAK,EAAE,KAAK+J,CAAO,EACzU,MAAM/M,EAAOoM,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,OAAO,EAAE,KAAK,OAAQ,MAAM,EAAE,KAAK,iBAAkB,EAAG,EAAE,UAAU,OAAO,EAAE,KAAK7M,EAAM,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,QAAS,MAAM,EAAE,MAAM,iBAAkB,UAAU,EACtM0N,EAAYnB,GAAM,WAAa,WACrC,GAAImB,IAAc,WAAY,CAC5B,MAAMC,EAAWlN,EAAK,OAAO,gBAAgB,EAAE,KAAK,KAAOxB,IAAOA,EAAE,IAAMyM,GAAI,KAAK,iBAAiB,GAAG,EAAE,EAAE,KAAK,gBAAiB,gBAAgB,EAAE,KAAK,KAAOzM,GAAMA,EAAE,OAAO,EAAE,EAAE,KAAK,KAAOA,GAAMA,EAAE,OAAO,EAAE,EAC/M0O,EAAS,OAAO,MAAM,EAAE,KAAK,SAAU,IAAI,EAAE,KAAK,aAAe1O,GAAMqO,EAAYrO,EAAE,OAAO,EAAE,CAAC,EAC/F0O,EAAS,OAAO,MAAM,EAAE,KAAK,SAAU,MAAM,EAAE,KAAK,aAAe1O,GAAMqO,EAAYrO,EAAE,OAAO,EAAE,CAAC,CAClG,CACD,IAAI2O,EACJ,OAAQF,EAAS,CACf,IAAK,WACHE,EAA2BlH,EAAQ,GAAM,EAAE,IAAK,UAAU,EAC1D,MACF,IAAK,SACHkH,EAA2BlH,EAAQ,GAAM4G,EAAY,EAAE,OAAO,EAAE,EAAG,UAAU,EAC7E,MACF,IAAK,SACHM,EAA2BlH,EAAQ,GAAM4G,EAAY,EAAE,OAAO,EAAE,EAAG,UAAU,EAC7E,MACF,QACEM,EAAWF,CACd,CACDjN,EAAK,OAAO,MAAM,EAAE,KAAK,IAAK8F,GAAwB,CAAA,EAAE,KAAK,SAAUqH,CAAQ,EAAE,KAAK,eAAiB,GAAM,KAAK,IAAI,EAAG,EAAE,KAAK,CAAC,EACjIC,GAAkB,OAAQhB,EAAK,EAAGE,CAAW,CAC/C,EAAG,MAAM,EACLe,GAAyB,CAC3B,KAAA5B,EACF,EAGI6B,GAAwCrH,EAAQyF,GAC9BA,EAAK,WAAW,2BAA4B,EAAE,EAAE,WAAW,aAAc;AAAA,CAAI,EAAE,KAAI,EAEtG,uBAAuB,EAGtB6B,GAA4BtH,EAAQuH,GAAY;AAAA,qBAC/BA,EAAQ,UAAU;AAAA,OAC/B,WAAW,EACfC,GAAiBF,GAGjBG,GAAgB9D,EAAe,MAAM,KAAKA,CAAc,EAC5DA,EAAe,MAAS8B,GAASgC,GAAcJ,GAAsB5B,CAAI,CAAC,EACvE,IAACiC,GAAU,CACZ,OAAQF,GACR,OAAQ7D,EACR,GAAIc,GACJ,SAAU2C,EACZ", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}