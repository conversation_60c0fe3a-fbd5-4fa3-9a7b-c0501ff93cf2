{"version": 3, "file": "rgbdDecode.fragment-CEbhyVHR.js", "sources": ["../../../../node_modules/.pnpm/@babylonjs+core@8.2.0/node_modules/@babylonjs/core/Shaders/rgbdDecode.fragment.js"], "sourcesContent": ["// Do not edit.\nimport { ShaderStore } from \"../Engines/shaderStore.js\";\nimport \"./ShadersInclude/helperFunctions.js\";\nconst name = \"rgbdDecodePixelShader\";\nconst shader = `varying vec2 vUV;uniform sampler2D textureSampler;\n#include<helperFunctions>\n#define CUSTOM_FRAGMENT_DEFINITIONS\nvoid main(void) \n{gl_FragColor=vec4(fromRGBD(texture2D(textureSampler,vUV)),1.0);}`;\n// Sideeffect\nif (!ShaderStore.ShadersStore[name]) {\n    ShaderStore.ShadersStore[name] = shader;\n}\n/** @internal */\nexport const rgbdDecodePixelShader = { name, shader };\n//# sourceMappingURL=rgbdDecode.fragment.js.map"], "names": ["name", "shader", "ShaderStore", "rgbdDecodePixelShader"], "mappings": "qIAGA,MAAMA,EAAO,wBACPC,EAAS;AAAA;AAAA;AAAA;AAAA,mEAMVC,EAAY,aAAaF,CAAI,IAC9BE,EAAY,aAAaF,CAAI,EAAIC,GAGzB,MAACE,EAAwB,CAAE,KAAAH,EAAM,OAAAC,CAAM", "x_google_ignoreList": [0]}