{"version": 3, "file": "xychartDiagram-NJOKMNIP-DChUQIgb.js", "sources": ["../../../../node_modules/.pnpm/d3-scale@4.0.2/node_modules/d3-scale/src/band.js", "../../../../node_modules/.pnpm/mermaid@11.5.0/node_modules/mermaid/dist/chunks/mermaid.core/xychartDiagram-NJOKMNIP.mjs"], "sourcesContent": ["import {range as sequence} from \"d3-array\";\nimport {initRange} from \"./init.js\";\nimport ordinal from \"./ordinal.js\";\n\nexport default function band() {\n  var scale = ordinal().unknown(undefined),\n      domain = scale.domain,\n      ordinalRange = scale.range,\n      r0 = 0,\n      r1 = 1,\n      step,\n      bandwidth,\n      round = false,\n      paddingInner = 0,\n      paddingOuter = 0,\n      align = 0.5;\n\n  delete scale.unknown;\n\n  function rescale() {\n    var n = domain().length,\n        reverse = r1 < r0,\n        start = reverse ? r1 : r0,\n        stop = reverse ? r0 : r1;\n    step = (stop - start) / Math.max(1, n - paddingInner + paddingOuter * 2);\n    if (round) step = Math.floor(step);\n    start += (stop - start - step * (n - paddingInner)) * align;\n    bandwidth = step * (1 - paddingInner);\n    if (round) start = Math.round(start), bandwidth = Math.round(bandwidth);\n    var values = sequence(n).map(function(i) { return start + step * i; });\n    return ordinalRange(reverse ? values.reverse() : values);\n  }\n\n  scale.domain = function(_) {\n    return arguments.length ? (domain(_), rescale()) : domain();\n  };\n\n  scale.range = function(_) {\n    return arguments.length ? ([r0, r1] = _, r0 = +r0, r1 = +r1, rescale()) : [r0, r1];\n  };\n\n  scale.rangeRound = function(_) {\n    return [r0, r1] = _, r0 = +r0, r1 = +r1, round = true, rescale();\n  };\n\n  scale.bandwidth = function() {\n    return bandwidth;\n  };\n\n  scale.step = function() {\n    return step;\n  };\n\n  scale.round = function(_) {\n    return arguments.length ? (round = !!_, rescale()) : round;\n  };\n\n  scale.padding = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, paddingOuter = +_), rescale()) : paddingInner;\n  };\n\n  scale.paddingInner = function(_) {\n    return arguments.length ? (paddingInner = Math.min(1, _), rescale()) : paddingInner;\n  };\n\n  scale.paddingOuter = function(_) {\n    return arguments.length ? (paddingOuter = +_, rescale()) : paddingOuter;\n  };\n\n  scale.align = function(_) {\n    return arguments.length ? (align = Math.max(0, Math.min(1, _)), rescale()) : align;\n  };\n\n  scale.copy = function() {\n    return band(domain(), [r0, r1])\n        .round(round)\n        .paddingInner(paddingInner)\n        .paddingOuter(paddingOuter)\n        .align(align);\n  };\n\n  return initRange.apply(rescale(), arguments);\n}\n\nfunction pointish(scale) {\n  var copy = scale.copy;\n\n  scale.padding = scale.paddingOuter;\n  delete scale.paddingInner;\n  delete scale.paddingOuter;\n\n  scale.copy = function() {\n    return pointish(copy());\n  };\n\n  return scale;\n}\n\nexport function point() {\n  return pointish(band.apply(null, arguments).paddingInner(1));\n}\n", "import {\n  computeDimensionOfText\n} from \"./chunk-4BQVQIO5.mjs\";\nimport {\n  cleanAndMerge\n} from \"./chunk-ABD7OU7K.mjs\";\nimport {\n  selectSvgElement\n} from \"./chunk-XYJ2X5CJ.mjs\";\nimport {\n  __name,\n  clear,\n  configureSvgSize,\n  defaultConfig_default,\n  getAccDescription,\n  getAccTitle,\n  getConfig,\n  getDiagramTitle,\n  getThemeVariables,\n  log,\n  sanitizeText,\n  setAccDescription,\n  setAccTitle,\n  setDiagramTitle\n} from \"./chunk-O7R7247Q.mjs\";\n\n// src/diagrams/xychart/parser/xychart.jison\nvar parser = function() {\n  var o = /* @__PURE__ */ __name(function(k, v, o2, l) {\n    for (o2 = o2 || {}, l = k.length; l--; o2[k[l]] = v) ;\n    return o2;\n  }, \"o\"), $V0 = [1, 10, 12, 14, 16, 18, 19, 21, 23], $V1 = [2, 6], $V2 = [1, 3], $V3 = [1, 5], $V4 = [1, 6], $V5 = [1, 7], $V6 = [1, 5, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $V7 = [1, 25], $V8 = [1, 26], $V9 = [1, 28], $Va = [1, 29], $Vb = [1, 30], $Vc = [1, 31], $Vd = [1, 32], $Ve = [1, 33], $Vf = [1, 34], $Vg = [1, 35], $Vh = [1, 36], $Vi = [1, 37], $Vj = [1, 43], $Vk = [1, 42], $Vl = [1, 47], $Vm = [1, 50], $Vn = [1, 10, 12, 14, 16, 18, 19, 21, 23, 34, 35, 36], $Vo = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36], $Vp = [1, 10, 12, 14, 16, 18, 19, 21, 23, 24, 26, 27, 28, 34, 35, 36, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50], $Vq = [1, 64];\n  var parser2 = {\n    trace: /* @__PURE__ */ __name(function trace() {\n    }, \"trace\"),\n    yy: {},\n    symbols_: { \"error\": 2, \"start\": 3, \"eol\": 4, \"XYCHART\": 5, \"chartConfig\": 6, \"document\": 7, \"CHART_ORIENTATION\": 8, \"statement\": 9, \"title\": 10, \"text\": 11, \"X_AXIS\": 12, \"parseXAxis\": 13, \"Y_AXIS\": 14, \"parseYAxis\": 15, \"LINE\": 16, \"plotData\": 17, \"BAR\": 18, \"acc_title\": 19, \"acc_title_value\": 20, \"acc_descr\": 21, \"acc_descr_value\": 22, \"acc_descr_multiline_value\": 23, \"SQUARE_BRACES_START\": 24, \"commaSeparatedNumbers\": 25, \"SQUARE_BRACES_END\": 26, \"NUMBER_WITH_DECIMAL\": 27, \"COMMA\": 28, \"xAxisData\": 29, \"bandData\": 30, \"ARROW_DELIMITER\": 31, \"commaSeparatedTexts\": 32, \"yAxisData\": 33, \"NEWLINE\": 34, \"SEMI\": 35, \"EOF\": 36, \"alphaNum\": 37, \"STR\": 38, \"MD_STR\": 39, \"alphaNumToken\": 40, \"AMP\": 41, \"NUM\": 42, \"ALPHA\": 43, \"PLUS\": 44, \"EQUALS\": 45, \"MULT\": 46, \"DOT\": 47, \"BRKT\": 48, \"MINUS\": 49, \"UNDERSCORE\": 50, \"$accept\": 0, \"$end\": 1 },\n    terminals_: { 2: \"error\", 5: \"XYCHART\", 8: \"CHART_ORIENTATION\", 10: \"title\", 12: \"X_AXIS\", 14: \"Y_AXIS\", 16: \"LINE\", 18: \"BAR\", 19: \"acc_title\", 20: \"acc_title_value\", 21: \"acc_descr\", 22: \"acc_descr_value\", 23: \"acc_descr_multiline_value\", 24: \"SQUARE_BRACES_START\", 26: \"SQUARE_BRACES_END\", 27: \"NUMBER_WITH_DECIMAL\", 28: \"COMMA\", 31: \"ARROW_DELIMITER\", 34: \"NEWLINE\", 35: \"SEMI\", 36: \"EOF\", 38: \"STR\", 39: \"MD_STR\", 41: \"AMP\", 42: \"NUM\", 43: \"ALPHA\", 44: \"PLUS\", 45: \"EQUALS\", 46: \"MULT\", 47: \"DOT\", 48: \"BRKT\", 49: \"MINUS\", 50: \"UNDERSCORE\" },\n    productions_: [0, [3, 2], [3, 3], [3, 2], [3, 1], [6, 1], [7, 0], [7, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 2], [9, 3], [9, 2], [9, 3], [9, 2], [9, 2], [9, 1], [17, 3], [25, 3], [25, 1], [13, 1], [13, 2], [13, 1], [29, 1], [29, 3], [30, 3], [32, 3], [32, 1], [15, 1], [15, 2], [15, 1], [33, 3], [4, 1], [4, 1], [4, 1], [11, 1], [11, 1], [11, 1], [37, 1], [37, 2], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1], [40, 1]],\n    performAction: /* @__PURE__ */ __name(function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$) {\n      var $0 = $$.length - 1;\n      switch (yystate) {\n        case 5:\n          yy.setOrientation($$[$0]);\n          break;\n        case 9:\n          yy.setDiagramTitle($$[$0].text.trim());\n          break;\n        case 12:\n          yy.setLineData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 13:\n          yy.setLineData($$[$0 - 1], $$[$0]);\n          break;\n        case 14:\n          yy.setBarData({ text: \"\", type: \"text\" }, $$[$0]);\n          break;\n        case 15:\n          yy.setBarData($$[$0 - 1], $$[$0]);\n          break;\n        case 16:\n          this.$ = $$[$0].trim();\n          yy.setAccTitle(this.$);\n          break;\n        case 17:\n        case 18:\n          this.$ = $$[$0].trim();\n          yy.setAccDescription(this.$);\n          break;\n        case 19:\n          this.$ = $$[$0 - 1];\n          break;\n        case 20:\n          this.$ = [Number($$[$0 - 2]), ...$$[$0]];\n          break;\n        case 21:\n          this.$ = [Number($$[$0])];\n          break;\n        case 22:\n          yy.setXAxisTitle($$[$0]);\n          break;\n        case 23:\n          yy.setXAxisTitle($$[$0 - 1]);\n          break;\n        case 24:\n          yy.setXAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 25:\n          yy.setXAxisBand($$[$0]);\n          break;\n        case 26:\n          yy.setXAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 27:\n          this.$ = $$[$0 - 1];\n          break;\n        case 28:\n          this.$ = [$$[$0 - 2], ...$$[$0]];\n          break;\n        case 29:\n          this.$ = [$$[$0]];\n          break;\n        case 30:\n          yy.setYAxisTitle($$[$0]);\n          break;\n        case 31:\n          yy.setYAxisTitle($$[$0 - 1]);\n          break;\n        case 32:\n          yy.setYAxisTitle({ type: \"text\", text: \"\" });\n          break;\n        case 33:\n          yy.setYAxisRangeData(Number($$[$0 - 2]), Number($$[$0]));\n          break;\n        case 37:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 38:\n          this.$ = { text: $$[$0], type: \"text\" };\n          break;\n        case 39:\n          this.$ = { text: $$[$0], type: \"markdown\" };\n          break;\n        case 40:\n          this.$ = $$[$0];\n          break;\n        case 41:\n          this.$ = $$[$0 - 1] + \"\" + $$[$0];\n          break;\n      }\n    }, \"anonymous\"),\n    table: [o($V0, $V1, { 3: 1, 4: 2, 7: 4, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [3] }, o($V0, $V1, { 4: 2, 7: 4, 3: 8, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), o($V0, $V1, { 4: 2, 7: 4, 6: 9, 3: 10, 5: $V2, 8: [1, 11], 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 4], 9: 12, 10: [1, 13], 12: [1, 14], 14: [1, 15], 16: [1, 16], 18: [1, 17], 19: [1, 18], 21: [1, 19], 23: [1, 20] }, o($V6, [2, 34]), o($V6, [2, 35]), o($V6, [2, 36]), { 1: [2, 1] }, o($V0, $V1, { 4: 2, 7: 4, 3: 21, 5: $V2, 34: $V3, 35: $V4, 36: $V5 }), { 1: [2, 3] }, o($V6, [2, 5]), o($V0, [2, 7], { 4: 22, 34: $V3, 35: $V4, 36: $V5 }), { 11: 23, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 39, 13: 38, 24: $Vj, 27: $Vk, 29: 40, 30: 41, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 45, 15: 44, 27: $Vl, 33: 46, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 49, 17: 48, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 11: 52, 17: 51, 24: $Vm, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, { 20: [1, 53] }, { 22: [1, 54] }, o($Vn, [2, 18]), { 1: [2, 2] }, o($Vn, [2, 8]), o($Vn, [2, 9]), o($Vo, [2, 37], { 40: 55, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }), o($Vo, [2, 38]), o($Vo, [2, 39]), o($Vp, [2, 40]), o($Vp, [2, 42]), o($Vp, [2, 43]), o($Vp, [2, 44]), o($Vp, [2, 45]), o($Vp, [2, 46]), o($Vp, [2, 47]), o($Vp, [2, 48]), o($Vp, [2, 49]), o($Vp, [2, 50]), o($Vp, [2, 51]), o($Vn, [2, 10]), o($Vn, [2, 22], { 30: 41, 29: 56, 24: $Vj, 27: $Vk }), o($Vn, [2, 24]), o($Vn, [2, 25]), { 31: [1, 57] }, { 11: 59, 32: 58, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 11]), o($Vn, [2, 30], { 33: 60, 27: $Vl }), o($Vn, [2, 32]), { 31: [1, 61] }, o($Vn, [2, 12]), { 17: 62, 24: $Vm }, { 25: 63, 27: $Vq }, o($Vn, [2, 14]), { 17: 65, 24: $Vm }, o($Vn, [2, 16]), o($Vn, [2, 17]), o($Vp, [2, 41]), o($Vn, [2, 23]), { 27: [1, 66] }, { 26: [1, 67] }, { 26: [2, 29], 28: [1, 68] }, o($Vn, [2, 31]), { 27: [1, 69] }, o($Vn, [2, 13]), { 26: [1, 70] }, { 26: [2, 21], 28: [1, 71] }, o($Vn, [2, 15]), o($Vn, [2, 26]), o($Vn, [2, 27]), { 11: 59, 32: 72, 37: 24, 38: $V7, 39: $V8, 40: 27, 41: $V9, 42: $Va, 43: $Vb, 44: $Vc, 45: $Vd, 46: $Ve, 47: $Vf, 48: $Vg, 49: $Vh, 50: $Vi }, o($Vn, [2, 33]), o($Vn, [2, 19]), { 25: 73, 27: $Vq }, { 26: [2, 28] }, { 26: [2, 20] }],\n    defaultActions: { 8: [2, 1], 10: [2, 3], 21: [2, 2], 72: [2, 28], 73: [2, 20] },\n    parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n      if (hash.recoverable) {\n        this.trace(str);\n      } else {\n        var error = new Error(str);\n        error.hash = hash;\n        throw error;\n      }\n    }, \"parseError\"),\n    parse: /* @__PURE__ */ __name(function parse(input) {\n      var self = this, stack = [0], tstack = [], vstack = [null], lstack = [], table = this.table, yytext = \"\", yylineno = 0, yyleng = 0, recovering = 0, TERROR = 2, EOF = 1;\n      var args = lstack.slice.call(arguments, 1);\n      var lexer2 = Object.create(this.lexer);\n      var sharedState = { yy: {} };\n      for (var k in this.yy) {\n        if (Object.prototype.hasOwnProperty.call(this.yy, k)) {\n          sharedState.yy[k] = this.yy[k];\n        }\n      }\n      lexer2.setInput(input, sharedState.yy);\n      sharedState.yy.lexer = lexer2;\n      sharedState.yy.parser = this;\n      if (typeof lexer2.yylloc == \"undefined\") {\n        lexer2.yylloc = {};\n      }\n      var yyloc = lexer2.yylloc;\n      lstack.push(yyloc);\n      var ranges = lexer2.options && lexer2.options.ranges;\n      if (typeof sharedState.yy.parseError === \"function\") {\n        this.parseError = sharedState.yy.parseError;\n      } else {\n        this.parseError = Object.getPrototypeOf(this).parseError;\n      }\n      function popStack(n) {\n        stack.length = stack.length - 2 * n;\n        vstack.length = vstack.length - n;\n        lstack.length = lstack.length - n;\n      }\n      __name(popStack, \"popStack\");\n      function lex() {\n        var token;\n        token = tstack.pop() || lexer2.lex() || EOF;\n        if (typeof token !== \"number\") {\n          if (token instanceof Array) {\n            tstack = token;\n            token = tstack.pop();\n          }\n          token = self.symbols_[token] || token;\n        }\n        return token;\n      }\n      __name(lex, \"lex\");\n      var symbol, preErrorSymbol, state, action, a, r, yyval = {}, p, len, newState, expected;\n      while (true) {\n        state = stack[stack.length - 1];\n        if (this.defaultActions[state]) {\n          action = this.defaultActions[state];\n        } else {\n          if (symbol === null || typeof symbol == \"undefined\") {\n            symbol = lex();\n          }\n          action = table[state] && table[state][symbol];\n        }\n        if (typeof action === \"undefined\" || !action.length || !action[0]) {\n          var errStr = \"\";\n          expected = [];\n          for (p in table[state]) {\n            if (this.terminals_[p] && p > TERROR) {\n              expected.push(\"'\" + this.terminals_[p] + \"'\");\n            }\n          }\n          if (lexer2.showPosition) {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \":\\n\" + lexer2.showPosition() + \"\\nExpecting \" + expected.join(\", \") + \", got '\" + (this.terminals_[symbol] || symbol) + \"'\";\n          } else {\n            errStr = \"Parse error on line \" + (yylineno + 1) + \": Unexpected \" + (symbol == EOF ? \"end of input\" : \"'\" + (this.terminals_[symbol] || symbol) + \"'\");\n          }\n          this.parseError(errStr, {\n            text: lexer2.match,\n            token: this.terminals_[symbol] || symbol,\n            line: lexer2.yylineno,\n            loc: yyloc,\n            expected\n          });\n        }\n        if (action[0] instanceof Array && action.length > 1) {\n          throw new Error(\"Parse Error: multiple actions possible at state: \" + state + \", token: \" + symbol);\n        }\n        switch (action[0]) {\n          case 1:\n            stack.push(symbol);\n            vstack.push(lexer2.yytext);\n            lstack.push(lexer2.yylloc);\n            stack.push(action[1]);\n            symbol = null;\n            if (!preErrorSymbol) {\n              yyleng = lexer2.yyleng;\n              yytext = lexer2.yytext;\n              yylineno = lexer2.yylineno;\n              yyloc = lexer2.yylloc;\n              if (recovering > 0) {\n                recovering--;\n              }\n            } else {\n              symbol = preErrorSymbol;\n              preErrorSymbol = null;\n            }\n            break;\n          case 2:\n            len = this.productions_[action[1]][1];\n            yyval.$ = vstack[vstack.length - len];\n            yyval._$ = {\n              first_line: lstack[lstack.length - (len || 1)].first_line,\n              last_line: lstack[lstack.length - 1].last_line,\n              first_column: lstack[lstack.length - (len || 1)].first_column,\n              last_column: lstack[lstack.length - 1].last_column\n            };\n            if (ranges) {\n              yyval._$.range = [\n                lstack[lstack.length - (len || 1)].range[0],\n                lstack[lstack.length - 1].range[1]\n              ];\n            }\n            r = this.performAction.apply(yyval, [\n              yytext,\n              yyleng,\n              yylineno,\n              sharedState.yy,\n              action[1],\n              vstack,\n              lstack\n            ].concat(args));\n            if (typeof r !== \"undefined\") {\n              return r;\n            }\n            if (len) {\n              stack = stack.slice(0, -1 * len * 2);\n              vstack = vstack.slice(0, -1 * len);\n              lstack = lstack.slice(0, -1 * len);\n            }\n            stack.push(this.productions_[action[1]][0]);\n            vstack.push(yyval.$);\n            lstack.push(yyval._$);\n            newState = table[stack[stack.length - 2]][stack[stack.length - 1]];\n            stack.push(newState);\n            break;\n          case 3:\n            return true;\n        }\n      }\n      return true;\n    }, \"parse\")\n  };\n  var lexer = /* @__PURE__ */ function() {\n    var lexer2 = {\n      EOF: 1,\n      parseError: /* @__PURE__ */ __name(function parseError(str, hash) {\n        if (this.yy.parser) {\n          this.yy.parser.parseError(str, hash);\n        } else {\n          throw new Error(str);\n        }\n      }, \"parseError\"),\n      // resets the lexer, sets new input\n      setInput: /* @__PURE__ */ __name(function(input, yy) {\n        this.yy = yy || this.yy || {};\n        this._input = input;\n        this._more = this._backtrack = this.done = false;\n        this.yylineno = this.yyleng = 0;\n        this.yytext = this.matched = this.match = \"\";\n        this.conditionStack = [\"INITIAL\"];\n        this.yylloc = {\n          first_line: 1,\n          first_column: 0,\n          last_line: 1,\n          last_column: 0\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [0, 0];\n        }\n        this.offset = 0;\n        return this;\n      }, \"setInput\"),\n      // consumes and returns one char from the input\n      input: /* @__PURE__ */ __name(function() {\n        var ch = this._input[0];\n        this.yytext += ch;\n        this.yyleng++;\n        this.offset++;\n        this.match += ch;\n        this.matched += ch;\n        var lines = ch.match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno++;\n          this.yylloc.last_line++;\n        } else {\n          this.yylloc.last_column++;\n        }\n        if (this.options.ranges) {\n          this.yylloc.range[1]++;\n        }\n        this._input = this._input.slice(1);\n        return ch;\n      }, \"input\"),\n      // unshifts one char (or a string) into the input\n      unput: /* @__PURE__ */ __name(function(ch) {\n        var len = ch.length;\n        var lines = ch.split(/(?:\\r\\n?|\\n)/g);\n        this._input = ch + this._input;\n        this.yytext = this.yytext.substr(0, this.yytext.length - len);\n        this.offset -= len;\n        var oldLines = this.match.split(/(?:\\r\\n?|\\n)/g);\n        this.match = this.match.substr(0, this.match.length - 1);\n        this.matched = this.matched.substr(0, this.matched.length - 1);\n        if (lines.length - 1) {\n          this.yylineno -= lines.length - 1;\n        }\n        var r = this.yylloc.range;\n        this.yylloc = {\n          first_line: this.yylloc.first_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.first_column,\n          last_column: lines ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length : this.yylloc.first_column - len\n        };\n        if (this.options.ranges) {\n          this.yylloc.range = [r[0], r[0] + this.yyleng - len];\n        }\n        this.yyleng = this.yytext.length;\n        return this;\n      }, \"unput\"),\n      // When called from action, caches matched text and appends it on next action\n      more: /* @__PURE__ */ __name(function() {\n        this._more = true;\n        return this;\n      }, \"more\"),\n      // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.\n      reject: /* @__PURE__ */ __name(function() {\n        if (this.options.backtrack_lexer) {\n          this._backtrack = true;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n        return this;\n      }, \"reject\"),\n      // retain first n characters of the match\n      less: /* @__PURE__ */ __name(function(n) {\n        this.unput(this.match.slice(n));\n      }, \"less\"),\n      // displays already matched input, i.e. for error messages\n      pastInput: /* @__PURE__ */ __name(function() {\n        var past = this.matched.substr(0, this.matched.length - this.match.length);\n        return (past.length > 20 ? \"...\" : \"\") + past.substr(-20).replace(/\\n/g, \"\");\n      }, \"pastInput\"),\n      // displays upcoming input, i.e. for error messages\n      upcomingInput: /* @__PURE__ */ __name(function() {\n        var next = this.match;\n        if (next.length < 20) {\n          next += this._input.substr(0, 20 - next.length);\n        }\n        return (next.substr(0, 20) + (next.length > 20 ? \"...\" : \"\")).replace(/\\n/g, \"\");\n      }, \"upcomingInput\"),\n      // displays the character position where the lexing error occurred, i.e. for error messages\n      showPosition: /* @__PURE__ */ __name(function() {\n        var pre = this.pastInput();\n        var c = new Array(pre.length + 1).join(\"-\");\n        return pre + this.upcomingInput() + \"\\n\" + c + \"^\";\n      }, \"showPosition\"),\n      // test the lexed token: return FALSE when not a match, otherwise return token\n      test_match: /* @__PURE__ */ __name(function(match, indexed_rule) {\n        var token, lines, backup;\n        if (this.options.backtrack_lexer) {\n          backup = {\n            yylineno: this.yylineno,\n            yylloc: {\n              first_line: this.yylloc.first_line,\n              last_line: this.last_line,\n              first_column: this.yylloc.first_column,\n              last_column: this.yylloc.last_column\n            },\n            yytext: this.yytext,\n            match: this.match,\n            matches: this.matches,\n            matched: this.matched,\n            yyleng: this.yyleng,\n            offset: this.offset,\n            _more: this._more,\n            _input: this._input,\n            yy: this.yy,\n            conditionStack: this.conditionStack.slice(0),\n            done: this.done\n          };\n          if (this.options.ranges) {\n            backup.yylloc.range = this.yylloc.range.slice(0);\n          }\n        }\n        lines = match[0].match(/(?:\\r\\n?|\\n).*/g);\n        if (lines) {\n          this.yylineno += lines.length;\n        }\n        this.yylloc = {\n          first_line: this.yylloc.last_line,\n          last_line: this.yylineno + 1,\n          first_column: this.yylloc.last_column,\n          last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\\r?\\n?/)[0].length : this.yylloc.last_column + match[0].length\n        };\n        this.yytext += match[0];\n        this.match += match[0];\n        this.matches = match;\n        this.yyleng = this.yytext.length;\n        if (this.options.ranges) {\n          this.yylloc.range = [this.offset, this.offset += this.yyleng];\n        }\n        this._more = false;\n        this._backtrack = false;\n        this._input = this._input.slice(match[0].length);\n        this.matched += match[0];\n        token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);\n        if (this.done && this._input) {\n          this.done = false;\n        }\n        if (token) {\n          return token;\n        } else if (this._backtrack) {\n          for (var k in backup) {\n            this[k] = backup[k];\n          }\n          return false;\n        }\n        return false;\n      }, \"test_match\"),\n      // return next match in input\n      next: /* @__PURE__ */ __name(function() {\n        if (this.done) {\n          return this.EOF;\n        }\n        if (!this._input) {\n          this.done = true;\n        }\n        var token, match, tempMatch, index;\n        if (!this._more) {\n          this.yytext = \"\";\n          this.match = \"\";\n        }\n        var rules = this._currentRules();\n        for (var i = 0; i < rules.length; i++) {\n          tempMatch = this._input.match(this.rules[rules[i]]);\n          if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {\n            match = tempMatch;\n            index = i;\n            if (this.options.backtrack_lexer) {\n              token = this.test_match(tempMatch, rules[i]);\n              if (token !== false) {\n                return token;\n              } else if (this._backtrack) {\n                match = false;\n                continue;\n              } else {\n                return false;\n              }\n            } else if (!this.options.flex) {\n              break;\n            }\n          }\n        }\n        if (match) {\n          token = this.test_match(match, rules[index]);\n          if (token !== false) {\n            return token;\n          }\n          return false;\n        }\n        if (this._input === \"\") {\n          return this.EOF;\n        } else {\n          return this.parseError(\"Lexical error on line \" + (this.yylineno + 1) + \". Unrecognized text.\\n\" + this.showPosition(), {\n            text: \"\",\n            token: null,\n            line: this.yylineno\n          });\n        }\n      }, \"next\"),\n      // return next match that has a token\n      lex: /* @__PURE__ */ __name(function lex() {\n        var r = this.next();\n        if (r) {\n          return r;\n        } else {\n          return this.lex();\n        }\n      }, \"lex\"),\n      // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)\n      begin: /* @__PURE__ */ __name(function begin(condition) {\n        this.conditionStack.push(condition);\n      }, \"begin\"),\n      // pop the previously active lexer condition state off the condition stack\n      popState: /* @__PURE__ */ __name(function popState() {\n        var n = this.conditionStack.length - 1;\n        if (n > 0) {\n          return this.conditionStack.pop();\n        } else {\n          return this.conditionStack[0];\n        }\n      }, \"popState\"),\n      // produce the lexer rule set which is active for the currently active lexer condition state\n      _currentRules: /* @__PURE__ */ __name(function _currentRules() {\n        if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {\n          return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;\n        } else {\n          return this.conditions[\"INITIAL\"].rules;\n        }\n      }, \"_currentRules\"),\n      // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available\n      topState: /* @__PURE__ */ __name(function topState(n) {\n        n = this.conditionStack.length - 1 - Math.abs(n || 0);\n        if (n >= 0) {\n          return this.conditionStack[n];\n        } else {\n          return \"INITIAL\";\n        }\n      }, \"topState\"),\n      // alias for begin(condition)\n      pushState: /* @__PURE__ */ __name(function pushState(condition) {\n        this.begin(condition);\n      }, \"pushState\"),\n      // return the number of states currently on the stack\n      stateStackSize: /* @__PURE__ */ __name(function stateStackSize() {\n        return this.conditionStack.length;\n      }, \"stateStackSize\"),\n      options: { \"case-insensitive\": true },\n      performAction: /* @__PURE__ */ __name(function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {\n        var YYSTATE = YY_START;\n        switch ($avoiding_name_collisions) {\n          case 0:\n            break;\n          case 1:\n            break;\n          case 2:\n            this.popState();\n            return 34;\n            break;\n          case 3:\n            this.popState();\n            return 34;\n            break;\n          case 4:\n            return 34;\n            break;\n          case 5:\n            break;\n          case 6:\n            return 10;\n            break;\n          case 7:\n            this.pushState(\"acc_title\");\n            return 19;\n            break;\n          case 8:\n            this.popState();\n            return \"acc_title_value\";\n            break;\n          case 9:\n            this.pushState(\"acc_descr\");\n            return 21;\n            break;\n          case 10:\n            this.popState();\n            return \"acc_descr_value\";\n            break;\n          case 11:\n            this.pushState(\"acc_descr_multiline\");\n            break;\n          case 12:\n            this.popState();\n            break;\n          case 13:\n            return \"acc_descr_multiline_value\";\n            break;\n          case 14:\n            return 5;\n            break;\n          case 15:\n            return 8;\n            break;\n          case 16:\n            this.pushState(\"axis_data\");\n            return \"X_AXIS\";\n            break;\n          case 17:\n            this.pushState(\"axis_data\");\n            return \"Y_AXIS\";\n            break;\n          case 18:\n            this.pushState(\"axis_band_data\");\n            return 24;\n            break;\n          case 19:\n            return 31;\n            break;\n          case 20:\n            this.pushState(\"data\");\n            return 16;\n            break;\n          case 21:\n            this.pushState(\"data\");\n            return 18;\n            break;\n          case 22:\n            this.pushState(\"data_inner\");\n            return 24;\n            break;\n          case 23:\n            return 27;\n            break;\n          case 24:\n            this.popState();\n            return 26;\n            break;\n          case 25:\n            this.popState();\n            break;\n          case 26:\n            this.pushState(\"string\");\n            break;\n          case 27:\n            this.popState();\n            break;\n          case 28:\n            return \"STR\";\n            break;\n          case 29:\n            return 24;\n            break;\n          case 30:\n            return 26;\n            break;\n          case 31:\n            return 43;\n            break;\n          case 32:\n            return \"COLON\";\n            break;\n          case 33:\n            return 44;\n            break;\n          case 34:\n            return 28;\n            break;\n          case 35:\n            return 45;\n            break;\n          case 36:\n            return 46;\n            break;\n          case 37:\n            return 48;\n            break;\n          case 38:\n            return 50;\n            break;\n          case 39:\n            return 47;\n            break;\n          case 40:\n            return 41;\n            break;\n          case 41:\n            return 49;\n            break;\n          case 42:\n            return 42;\n            break;\n          case 43:\n            break;\n          case 44:\n            return 35;\n            break;\n          case 45:\n            return 36;\n            break;\n        }\n      }, \"anonymous\"),\n      rules: [/^(?:%%(?!\\{)[^\\n]*)/i, /^(?:[^\\}]%%[^\\n]*)/i, /^(?:(\\r?\\n))/i, /^(?:(\\r?\\n))/i, /^(?:[\\n\\r]+)/i, /^(?:%%[^\\n]*)/i, /^(?:title\\b)/i, /^(?:accTitle\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*:\\s*)/i, /^(?:(?!\\n||)*[^\\n]*)/i, /^(?:accDescr\\s*\\{\\s*)/i, /^(?:\\{)/i, /^(?:[^\\}]*)/i, /^(?:xychart-beta\\b)/i, /^(?:(?:vertical|horizontal))/i, /^(?:x-axis\\b)/i, /^(?:y-axis\\b)/i, /^(?:\\[)/i, /^(?:-->)/i, /^(?:line\\b)/i, /^(?:bar\\b)/i, /^(?:\\[)/i, /^(?:[+-]?(?:\\d+(?:\\.\\d+)?|\\.\\d+))/i, /^(?:\\])/i, /^(?:(?:`\\)                                    \\{ this\\.pushState\\(md_string\\); \\}\\n<md_string>\\(\\?:\\(\\?!`\"\\)\\.\\)\\+                  \\{ return MD_STR; \\}\\n<md_string>\\(\\?:`))/i, /^(?:[\"])/i, /^(?:[\"])/i, /^(?:[^\"]*)/i, /^(?:\\[)/i, /^(?:\\])/i, /^(?:[A-Za-z]+)/i, /^(?::)/i, /^(?:\\+)/i, /^(?:,)/i, /^(?:=)/i, /^(?:\\*)/i, /^(?:#)/i, /^(?:[\\_])/i, /^(?:\\.)/i, /^(?:&)/i, /^(?:-)/i, /^(?:[0-9]+)/i, /^(?:\\s+)/i, /^(?:;)/i, /^(?:$)/i],\n      conditions: { \"data_inner\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 23, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"data\": { \"rules\": [0, 1, 3, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 22, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_band_data\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 24, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"axis_data\": { \"rules\": [0, 1, 2, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true }, \"acc_descr_multiline\": { \"rules\": [12, 13], \"inclusive\": false }, \"acc_descr\": { \"rules\": [10], \"inclusive\": false }, \"acc_title\": { \"rules\": [8], \"inclusive\": false }, \"title\": { \"rules\": [], \"inclusive\": false }, \"md_string\": { \"rules\": [], \"inclusive\": false }, \"string\": { \"rules\": [27, 28], \"inclusive\": false }, \"INITIAL\": { \"rules\": [0, 1, 4, 5, 6, 7, 9, 11, 14, 15, 16, 17, 20, 21, 25, 26, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45], \"inclusive\": true } }\n    };\n    return lexer2;\n  }();\n  parser2.lexer = lexer;\n  function Parser() {\n    this.yy = {};\n  }\n  __name(Parser, \"Parser\");\n  Parser.prototype = parser2;\n  parser2.Parser = Parser;\n  return new Parser();\n}();\nparser.parser = parser;\nvar xychart_default = parser;\n\n// src/diagrams/xychart/chartBuilder/interfaces.ts\nfunction isBarPlot(data) {\n  return data.type === \"bar\";\n}\n__name(isBarPlot, \"isBarPlot\");\nfunction isBandAxisData(data) {\n  return data.type === \"band\";\n}\n__name(isBandAxisData, \"isBandAxisData\");\nfunction isLinearAxisData(data) {\n  return data.type === \"linear\";\n}\n__name(isLinearAxisData, \"isLinearAxisData\");\n\n// src/diagrams/xychart/chartBuilder/textDimensionCalculator.ts\nvar TextDimensionCalculatorWithFont = class {\n  constructor(parentGroup) {\n    this.parentGroup = parentGroup;\n  }\n  static {\n    __name(this, \"TextDimensionCalculatorWithFont\");\n  }\n  getMaxDimension(texts, fontSize) {\n    if (!this.parentGroup) {\n      return {\n        width: texts.reduce((acc, cur) => Math.max(cur.length, acc), 0) * fontSize,\n        height: fontSize\n      };\n    }\n    const dimension = {\n      width: 0,\n      height: 0\n    };\n    const elem = this.parentGroup.append(\"g\").attr(\"visibility\", \"hidden\").attr(\"font-size\", fontSize);\n    for (const t of texts) {\n      const bbox = computeDimensionOfText(elem, 1, t);\n      const width = bbox ? bbox.width : t.length * fontSize;\n      const height = bbox ? bbox.height : fontSize;\n      dimension.width = Math.max(dimension.width, width);\n      dimension.height = Math.max(dimension.height, height);\n    }\n    elem.remove();\n    return dimension;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nimport { scaleBand } from \"d3\";\n\n// src/diagrams/xychart/chartBuilder/components/axis/baseAxis.ts\nvar BAR_WIDTH_TO_TICK_WIDTH_RATIO = 0.7;\nvar MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL = 0.2;\nvar BaseAxis = class {\n  constructor(axisConfig, title, textDimensionCalculator, axisThemeConfig) {\n    this.axisConfig = axisConfig;\n    this.title = title;\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.axisThemeConfig = axisThemeConfig;\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n    this.showTitle = false;\n    this.showLabel = false;\n    this.showTick = false;\n    this.showAxisLine = false;\n    this.outerPadding = 0;\n    this.titleTextHeight = 0;\n    this.labelTextHeight = 0;\n    this.range = [0, 10];\n    this.boundingRect = { x: 0, y: 0, width: 0, height: 0 };\n    this.axisPosition = \"left\";\n  }\n  static {\n    __name(this, \"BaseAxis\");\n  }\n  setRange(range) {\n    this.range = range;\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.boundingRect.height = range[1] - range[0];\n    } else {\n      this.boundingRect.width = range[1] - range[0];\n    }\n    this.recalculateScale();\n  }\n  getRange() {\n    return [this.range[0] + this.outerPadding, this.range[1] - this.outerPadding];\n  }\n  setAxisPosition(axisPosition) {\n    this.axisPosition = axisPosition;\n    this.setRange(this.range);\n  }\n  getTickDistance() {\n    const range = this.getRange();\n    return Math.abs(range[0] - range[1]) / this.getTickValues().length;\n  }\n  getAxisOuterPadding() {\n    return this.outerPadding;\n  }\n  getLabelDimension() {\n    return this.textDimensionCalculator.getMaxDimension(\n      this.getTickValues().map((tick) => tick.toString()),\n      this.axisConfig.labelFontSize\n    );\n  }\n  recalculateOuterPaddingToDrawBar() {\n    if (BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() > this.outerPadding * 2) {\n      this.outerPadding = Math.floor(BAR_WIDTH_TO_TICK_WIDTH_RATIO * this.getTickDistance() / 2);\n    }\n    this.recalculateScale();\n  }\n  calculateSpaceIfDrawnHorizontally(availableSpace) {\n    let availableHeight = availableSpace.height;\n    if (this.axisConfig.showAxisLine && availableHeight > this.axisConfig.axisLineWidth) {\n      availableHeight -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.width;\n      this.outerPadding = Math.min(spaceRequired.width / 2, maxPadding);\n      const heightRequired = spaceRequired.height + this.axisConfig.labelPadding * 2;\n      this.labelTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableHeight >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableHeight -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const heightRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (heightRequired <= availableHeight) {\n        availableHeight -= heightRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height - availableHeight;\n  }\n  calculateSpaceIfDrawnVertical(availableSpace) {\n    let availableWidth = availableSpace.width;\n    if (this.axisConfig.showAxisLine && availableWidth > this.axisConfig.axisLineWidth) {\n      availableWidth -= this.axisConfig.axisLineWidth;\n      this.showAxisLine = true;\n    }\n    if (this.axisConfig.showLabel) {\n      const spaceRequired = this.getLabelDimension();\n      const maxPadding = MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL * availableSpace.height;\n      this.outerPadding = Math.min(spaceRequired.height / 2, maxPadding);\n      const widthRequired = spaceRequired.width + this.axisConfig.labelPadding * 2;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showLabel = true;\n      }\n    }\n    if (this.axisConfig.showTick && availableWidth >= this.axisConfig.tickLength) {\n      this.showTick = true;\n      availableWidth -= this.axisConfig.tickLength;\n    }\n    if (this.axisConfig.showTitle && this.title) {\n      const spaceRequired = this.textDimensionCalculator.getMaxDimension(\n        [this.title],\n        this.axisConfig.titleFontSize\n      );\n      const widthRequired = spaceRequired.height + this.axisConfig.titlePadding * 2;\n      this.titleTextHeight = spaceRequired.height;\n      if (widthRequired <= availableWidth) {\n        availableWidth -= widthRequired;\n        this.showTitle = true;\n      }\n    }\n    this.boundingRect.width = availableSpace.width - availableWidth;\n    this.boundingRect.height = availableSpace.height;\n  }\n  calculateSpace(availableSpace) {\n    if (this.axisPosition === \"left\" || this.axisPosition === \"right\") {\n      this.calculateSpaceIfDrawnVertical(availableSpace);\n    } else {\n      this.calculateSpaceIfDrawnHorizontally(availableSpace);\n    }\n    this.recalculateScale();\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  getDrawableElementsForLeftAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const x = this.boundingRect.x + this.boundingRect.width - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"axisl-line\"],\n        data: [\n          {\n            path: `M ${x},${this.boundingRect.y} L ${x},${this.boundingRect.y + this.boundingRect.height} `,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.boundingRect.x + this.boundingRect.width - (this.showLabel ? this.axisConfig.labelPadding : 0) - (this.showTick ? this.axisConfig.tickLength : 0) - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          y: this.getScaleValue(tick),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"middle\",\n          horizontalPos: \"right\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const x = this.boundingRect.x + this.boundingRect.width - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"left-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${x},${this.getScaleValue(tick)} L ${x - this.axisConfig.tickLength},${this.getScaleValue(tick)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"left-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.axisConfig.titlePadding,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 270,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForBottomAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + this.axisConfig.labelPadding + (this.showTick ? this.axisConfig.tickLength : 0) + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0),\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y + (this.showAxisLine ? this.axisConfig.axisLineWidth : 0);\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"bottom-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y} L ${this.getScaleValue(tick)},${y + this.axisConfig.tickLength}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"bottom-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.range[0] + (this.range[1] - this.range[0]) / 2,\n            y: this.boundingRect.y + this.boundingRect.height - this.axisConfig.titlePadding - this.titleTextHeight,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElementsForTopAxis() {\n    const drawableElement = [];\n    if (this.showAxisLine) {\n      const y = this.boundingRect.y + this.boundingRect.height - this.axisConfig.axisLineWidth / 2;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"axis-line\"],\n        data: [\n          {\n            path: `M ${this.boundingRect.x},${y} L ${this.boundingRect.x + this.boundingRect.width},${y}`,\n            strokeFill: this.axisThemeConfig.axisLineColor,\n            strokeWidth: this.axisConfig.axisLineWidth\n          }\n        ]\n      });\n    }\n    if (this.showLabel) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"label\"],\n        data: this.getTickValues().map((tick) => ({\n          text: tick.toString(),\n          x: this.getScaleValue(tick),\n          y: this.boundingRect.y + (this.showTitle ? this.titleTextHeight + this.axisConfig.titlePadding * 2 : 0) + this.axisConfig.labelPadding,\n          fill: this.axisThemeConfig.labelColor,\n          fontSize: this.axisConfig.labelFontSize,\n          rotation: 0,\n          verticalPos: \"top\",\n          horizontalPos: \"center\"\n        }))\n      });\n    }\n    if (this.showTick) {\n      const y = this.boundingRect.y;\n      drawableElement.push({\n        type: \"path\",\n        groupTexts: [\"top-axis\", \"ticks\"],\n        data: this.getTickValues().map((tick) => ({\n          path: `M ${this.getScaleValue(tick)},${y + this.boundingRect.height - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)} L ${this.getScaleValue(tick)},${y + this.boundingRect.height - this.axisConfig.tickLength - (this.showAxisLine ? this.axisConfig.axisLineWidth : 0)}`,\n          strokeFill: this.axisThemeConfig.tickColor,\n          strokeWidth: this.axisConfig.tickWidth\n        }))\n      });\n    }\n    if (this.showTitle) {\n      drawableElement.push({\n        type: \"text\",\n        groupTexts: [\"top-axis\", \"title\"],\n        data: [\n          {\n            text: this.title,\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.axisConfig.titlePadding,\n            fill: this.axisThemeConfig.titleColor,\n            fontSize: this.axisConfig.titleFontSize,\n            rotation: 0,\n            verticalPos: \"top\",\n            horizontalPos: \"center\"\n          }\n        ]\n      });\n    }\n    return drawableElement;\n  }\n  getDrawableElements() {\n    if (this.axisPosition === \"left\") {\n      return this.getDrawableElementsForLeftAxis();\n    }\n    if (this.axisPosition === \"right\") {\n      throw Error(\"Drawing of right axis is not implemented\");\n    }\n    if (this.axisPosition === \"bottom\") {\n      return this.getDrawableElementsForBottomAxis();\n    }\n    if (this.axisPosition === \"top\") {\n      return this.getDrawableElementsForTopAxis();\n    }\n    return [];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/bandAxis.ts\nvar BandAxis = class extends BaseAxis {\n  static {\n    __name(this, \"BandAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, categories, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.categories = categories;\n    this.scale = scaleBand().domain(this.categories).range(this.getRange());\n  }\n  setRange(range) {\n    super.setRange(range);\n  }\n  recalculateScale() {\n    this.scale = scaleBand().domain(this.categories).range(this.getRange()).paddingInner(1).paddingOuter(0).align(0.5);\n    log.trace(\"BandAxis axis final categories, range: \", this.categories, this.getRange());\n  }\n  getTickValues() {\n    return this.categories;\n  }\n  getScaleValue(value) {\n    return this.scale(value) ?? this.getRange()[0];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/linearAxis.ts\nimport { scaleLinear } from \"d3\";\nvar LinearAxis = class extends BaseAxis {\n  static {\n    __name(this, \"LinearAxis\");\n  }\n  constructor(axisConfig, axisThemeConfig, domain, title, textDimensionCalculator) {\n    super(axisConfig, title, textDimensionCalculator, axisThemeConfig);\n    this.domain = domain;\n    this.scale = scaleLinear().domain(this.domain).range(this.getRange());\n  }\n  getTickValues() {\n    return this.scale.ticks();\n  }\n  recalculateScale() {\n    const domain = [...this.domain];\n    if (this.axisPosition === \"left\") {\n      domain.reverse();\n    }\n    this.scale = scaleLinear().domain(domain).range(this.getRange());\n  }\n  getScaleValue(value) {\n    return this.scale(value);\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/axis/index.ts\nfunction getAxis(data, axisConfig, axisThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  if (isBandAxisData(data)) {\n    return new BandAxis(\n      axisConfig,\n      axisThemeConfig,\n      data.categories,\n      data.title,\n      textDimensionCalculator\n    );\n  }\n  return new LinearAxis(\n    axisConfig,\n    axisThemeConfig,\n    [data.min, data.max],\n    data.title,\n    textDimensionCalculator\n  );\n}\n__name(getAxis, \"getAxis\");\n\n// src/diagrams/xychart/chartBuilder/components/chartTitle.ts\nvar ChartTitle = class {\n  constructor(textDimensionCalculator, chartConfig, chartData, chartThemeConfig) {\n    this.textDimensionCalculator = textDimensionCalculator;\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    this.showChartTitle = false;\n  }\n  static {\n    __name(this, \"ChartTitle\");\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    const titleDimension = this.textDimensionCalculator.getMaxDimension(\n      [this.chartData.title],\n      this.chartConfig.titleFontSize\n    );\n    const widthRequired = Math.max(titleDimension.width, availableSpace.width);\n    const heightRequired = titleDimension.height + 2 * this.chartConfig.titlePadding;\n    if (titleDimension.width <= widthRequired && titleDimension.height <= heightRequired && this.chartConfig.showTitle && this.chartData.title) {\n      this.boundingRect.width = widthRequired;\n      this.boundingRect.height = heightRequired;\n      this.showChartTitle = true;\n    }\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    const drawableElem = [];\n    if (this.showChartTitle) {\n      drawableElem.push({\n        groupTexts: [\"chart-title\"],\n        type: \"text\",\n        data: [\n          {\n            fontSize: this.chartConfig.titleFontSize,\n            text: this.chartData.title,\n            verticalPos: \"middle\",\n            horizontalPos: \"center\",\n            x: this.boundingRect.x + this.boundingRect.width / 2,\n            y: this.boundingRect.y + this.boundingRect.height / 2,\n            fill: this.chartThemeConfig.titleColor,\n            rotation: 0\n          }\n        ]\n      });\n    }\n    return drawableElem;\n  }\n};\nfunction getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n  const textDimensionCalculator = new TextDimensionCalculatorWithFont(tmpSVGGroup2);\n  return new ChartTitle(textDimensionCalculator, chartConfig, chartData, chartThemeConfig);\n}\n__name(getChartTitleComponent, \"getChartTitleComponent\");\n\n// src/diagrams/xychart/chartBuilder/components/plot/linePlot.ts\nimport { line } from \"d3\";\nvar LinePlot = class {\n  constructor(plotData, xAxis, yAxis, orientation, plotIndex2) {\n    this.plotData = plotData;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"LinePlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.plotData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    let path;\n    if (this.orientation === \"horizontal\") {\n      path = line().y((d) => d[0]).x((d) => d[1])(finalData);\n    } else {\n      path = line().x((d) => d[0]).y((d) => d[1])(finalData);\n    }\n    if (!path) {\n      return [];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `line-plot-${this.plotIndex}`],\n        type: \"path\",\n        data: [\n          {\n            path,\n            strokeFill: this.plotData.strokeFill,\n            strokeWidth: this.plotData.strokeWidth\n          }\n        ]\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/barPlot.ts\nvar BarPlot = class {\n  constructor(barData, boundingRect, xAxis, yAxis, orientation, plotIndex2) {\n    this.barData = barData;\n    this.boundingRect = boundingRect;\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n    this.orientation = orientation;\n    this.plotIndex = plotIndex2;\n  }\n  static {\n    __name(this, \"BarPlot\");\n  }\n  getDrawableElement() {\n    const finalData = this.barData.data.map((d) => [\n      this.xAxis.getScaleValue(d[0]),\n      this.yAxis.getScaleValue(d[1])\n    ]);\n    const barPaddingPercent = 0.05;\n    const barWidth = Math.min(this.xAxis.getAxisOuterPadding() * 2, this.xAxis.getTickDistance()) * (1 - barPaddingPercent);\n    const barWidthHalf = barWidth / 2;\n    if (this.orientation === \"horizontal\") {\n      return [\n        {\n          groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n          type: \"rect\",\n          data: finalData.map((data) => ({\n            x: this.boundingRect.x,\n            y: data[0] - barWidthHalf,\n            height: barWidth,\n            width: data[1] - this.boundingRect.x,\n            fill: this.barData.fill,\n            strokeWidth: 0,\n            strokeFill: this.barData.fill\n          }))\n        }\n      ];\n    }\n    return [\n      {\n        groupTexts: [\"plot\", `bar-plot-${this.plotIndex}`],\n        type: \"rect\",\n        data: finalData.map((data) => ({\n          x: data[0] - barWidthHalf,\n          y: data[1],\n          width: barWidth,\n          height: this.boundingRect.y + this.boundingRect.height - data[1],\n          fill: this.barData.fill,\n          strokeWidth: 0,\n          strokeFill: this.barData.fill\n        }))\n      }\n    ];\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/components/plot/index.ts\nvar BasePlot = class {\n  constructor(chartConfig, chartData, chartThemeConfig) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.chartThemeConfig = chartThemeConfig;\n    this.boundingRect = {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  static {\n    __name(this, \"BasePlot\");\n  }\n  setAxes(xAxis, yAxis) {\n    this.xAxis = xAxis;\n    this.yAxis = yAxis;\n  }\n  setBoundingBoxXY(point) {\n    this.boundingRect.x = point.x;\n    this.boundingRect.y = point.y;\n  }\n  calculateSpace(availableSpace) {\n    this.boundingRect.width = availableSpace.width;\n    this.boundingRect.height = availableSpace.height;\n    return {\n      width: this.boundingRect.width,\n      height: this.boundingRect.height\n    };\n  }\n  getDrawableElements() {\n    if (!(this.xAxis && this.yAxis)) {\n      throw Error(\"Axes must be passed to render Plots\");\n    }\n    const drawableElem = [];\n    for (const [i, plot] of this.chartData.plots.entries()) {\n      switch (plot.type) {\n        case \"line\":\n          {\n            const linePlot = new LinePlot(\n              plot,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...linePlot.getDrawableElement());\n          }\n          break;\n        case \"bar\":\n          {\n            const barPlot = new BarPlot(\n              plot,\n              this.boundingRect,\n              this.xAxis,\n              this.yAxis,\n              this.chartConfig.chartOrientation,\n              i\n            );\n            drawableElem.push(...barPlot.getDrawableElement());\n          }\n          break;\n      }\n    }\n    return drawableElem;\n  }\n};\nfunction getPlotComponent(chartConfig, chartData, chartThemeConfig) {\n  return new BasePlot(chartConfig, chartData, chartThemeConfig);\n}\n__name(getPlotComponent, \"getPlotComponent\");\n\n// src/diagrams/xychart/chartBuilder/orchestrator.ts\nvar Orchestrator = class {\n  constructor(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2) {\n    this.chartConfig = chartConfig;\n    this.chartData = chartData;\n    this.componentStore = {\n      title: getChartTitleComponent(chartConfig, chartData, chartThemeConfig, tmpSVGGroup2),\n      plot: getPlotComponent(chartConfig, chartData, chartThemeConfig),\n      xAxis: getAxis(\n        chartData.xAxis,\n        chartConfig.xAxis,\n        {\n          titleColor: chartThemeConfig.xAxisTitleColor,\n          labelColor: chartThemeConfig.xAxisLabelColor,\n          tickColor: chartThemeConfig.xAxisTickColor,\n          axisLineColor: chartThemeConfig.xAxisLineColor\n        },\n        tmpSVGGroup2\n      ),\n      yAxis: getAxis(\n        chartData.yAxis,\n        chartConfig.yAxis,\n        {\n          titleColor: chartThemeConfig.yAxisTitleColor,\n          labelColor: chartThemeConfig.yAxisLabelColor,\n          tickColor: chartThemeConfig.yAxisTickColor,\n          axisLineColor: chartThemeConfig.yAxisLineColor\n        },\n        tmpSVGGroup2\n      )\n    };\n  }\n  static {\n    __name(this, \"Orchestrator\");\n  }\n  calculateVerticalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    plotY = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"bottom\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    this.componentStore.yAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    plotX = spaceUsed.width;\n    availableWidth -= spaceUsed.width;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.xAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: plotX, y: plotY + chartHeight });\n    this.componentStore.yAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateHorizontalSpace() {\n    let availableWidth = this.chartConfig.width;\n    let availableHeight = this.chartConfig.height;\n    let titleYEnd = 0;\n    let plotX = 0;\n    let plotY = 0;\n    let chartWidth = Math.floor(availableWidth * this.chartConfig.plotReservedSpacePercent / 100);\n    let chartHeight = Math.floor(\n      availableHeight * this.chartConfig.plotReservedSpacePercent / 100\n    );\n    let spaceUsed = this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    availableWidth -= spaceUsed.width;\n    availableHeight -= spaceUsed.height;\n    spaceUsed = this.componentStore.title.calculateSpace({\n      width: this.chartConfig.width,\n      height: availableHeight\n    });\n    titleYEnd = spaceUsed.height;\n    availableHeight -= spaceUsed.height;\n    this.componentStore.xAxis.setAxisPosition(\"left\");\n    spaceUsed = this.componentStore.xAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableWidth -= spaceUsed.width;\n    plotX = spaceUsed.width;\n    this.componentStore.yAxis.setAxisPosition(\"top\");\n    spaceUsed = this.componentStore.yAxis.calculateSpace({\n      width: availableWidth,\n      height: availableHeight\n    });\n    availableHeight -= spaceUsed.height;\n    plotY = titleYEnd + spaceUsed.height;\n    if (availableWidth > 0) {\n      chartWidth += availableWidth;\n      availableWidth = 0;\n    }\n    if (availableHeight > 0) {\n      chartHeight += availableHeight;\n      availableHeight = 0;\n    }\n    this.componentStore.plot.calculateSpace({\n      width: chartWidth,\n      height: chartHeight\n    });\n    this.componentStore.plot.setBoundingBoxXY({ x: plotX, y: plotY });\n    this.componentStore.yAxis.setRange([plotX, plotX + chartWidth]);\n    this.componentStore.yAxis.setBoundingBoxXY({ x: plotX, y: titleYEnd });\n    this.componentStore.xAxis.setRange([plotY, plotY + chartHeight]);\n    this.componentStore.xAxis.setBoundingBoxXY({ x: 0, y: plotY });\n    if (this.chartData.plots.some((p) => isBarPlot(p))) {\n      this.componentStore.xAxis.recalculateOuterPaddingToDrawBar();\n    }\n  }\n  calculateSpace() {\n    if (this.chartConfig.chartOrientation === \"horizontal\") {\n      this.calculateHorizontalSpace();\n    } else {\n      this.calculateVerticalSpace();\n    }\n  }\n  getDrawableElement() {\n    this.calculateSpace();\n    const drawableElem = [];\n    this.componentStore.plot.setAxes(this.componentStore.xAxis, this.componentStore.yAxis);\n    for (const component of Object.values(this.componentStore)) {\n      drawableElem.push(...component.getDrawableElements());\n    }\n    return drawableElem;\n  }\n};\n\n// src/diagrams/xychart/chartBuilder/index.ts\nvar XYChartBuilder = class {\n  static {\n    __name(this, \"XYChartBuilder\");\n  }\n  static build(config, chartData, chartThemeConfig, tmpSVGGroup2) {\n    const orchestrator = new Orchestrator(config, chartData, chartThemeConfig, tmpSVGGroup2);\n    return orchestrator.getDrawableElement();\n  }\n};\n\n// src/diagrams/xychart/xychartDb.ts\nvar plotIndex = 0;\nvar tmpSVGGroup;\nvar xyChartConfig = getChartDefaultConfig();\nvar xyChartThemeConfig = getChartDefaultThemeConfig();\nvar xyChartData = getChartDefaultData();\nvar plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\nvar hasSetXAxis = false;\nvar hasSetYAxis = false;\nfunction getChartDefaultThemeConfig() {\n  const defaultThemeVariables = getThemeVariables();\n  const config = getConfig();\n  return cleanAndMerge(defaultThemeVariables.xyChart, config.themeVariables.xyChart);\n}\n__name(getChartDefaultThemeConfig, \"getChartDefaultThemeConfig\");\nfunction getChartDefaultConfig() {\n  const config = getConfig();\n  return cleanAndMerge(\n    defaultConfig_default.xyChart,\n    config.xyChart\n  );\n}\n__name(getChartDefaultConfig, \"getChartDefaultConfig\");\nfunction getChartDefaultData() {\n  return {\n    yAxis: {\n      type: \"linear\",\n      title: \"\",\n      min: Infinity,\n      max: -Infinity\n    },\n    xAxis: {\n      type: \"band\",\n      title: \"\",\n      categories: []\n    },\n    title: \"\",\n    plots: []\n  };\n}\n__name(getChartDefaultData, \"getChartDefaultData\");\nfunction textSanitizer(text) {\n  const config = getConfig();\n  return sanitizeText(text.trim(), config);\n}\n__name(textSanitizer, \"textSanitizer\");\nfunction setTmpSVGG(SVGG) {\n  tmpSVGGroup = SVGG;\n}\n__name(setTmpSVGG, \"setTmpSVGG\");\nfunction setOrientation(orientation) {\n  if (orientation === \"horizontal\") {\n    xyChartConfig.chartOrientation = \"horizontal\";\n  } else {\n    xyChartConfig.chartOrientation = \"vertical\";\n  }\n}\n__name(setOrientation, \"setOrientation\");\nfunction setXAxisTitle(title) {\n  xyChartData.xAxis.title = textSanitizer(title.text);\n}\n__name(setXAxisTitle, \"setXAxisTitle\");\nfunction setXAxisRangeData(min, max) {\n  xyChartData.xAxis = { type: \"linear\", title: xyChartData.xAxis.title, min, max };\n  hasSetXAxis = true;\n}\n__name(setXAxisRangeData, \"setXAxisRangeData\");\nfunction setXAxisBand(categories) {\n  xyChartData.xAxis = {\n    type: \"band\",\n    title: xyChartData.xAxis.title,\n    categories: categories.map((c) => textSanitizer(c.text))\n  };\n  hasSetXAxis = true;\n}\n__name(setXAxisBand, \"setXAxisBand\");\nfunction setYAxisTitle(title) {\n  xyChartData.yAxis.title = textSanitizer(title.text);\n}\n__name(setYAxisTitle, \"setYAxisTitle\");\nfunction setYAxisRangeData(min, max) {\n  xyChartData.yAxis = { type: \"linear\", title: xyChartData.yAxis.title, min, max };\n  hasSetYAxis = true;\n}\n__name(setYAxisRangeData, \"setYAxisRangeData\");\nfunction setYAxisRangeFromPlotData(data) {\n  const minValue = Math.min(...data);\n  const maxValue = Math.max(...data);\n  const prevMinValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.min : Infinity;\n  const prevMaxValue = isLinearAxisData(xyChartData.yAxis) ? xyChartData.yAxis.max : -Infinity;\n  xyChartData.yAxis = {\n    type: \"linear\",\n    title: xyChartData.yAxis.title,\n    min: Math.min(prevMinValue, minValue),\n    max: Math.max(prevMaxValue, maxValue)\n  };\n}\n__name(setYAxisRangeFromPlotData, \"setYAxisRangeFromPlotData\");\nfunction transformDataWithoutCategory(data) {\n  let retData = [];\n  if (data.length === 0) {\n    return retData;\n  }\n  if (!hasSetXAxis) {\n    const prevMinValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.min : Infinity;\n    const prevMaxValue = isLinearAxisData(xyChartData.xAxis) ? xyChartData.xAxis.max : -Infinity;\n    setXAxisRangeData(Math.min(prevMinValue, 1), Math.max(prevMaxValue, data.length));\n  }\n  if (!hasSetYAxis) {\n    setYAxisRangeFromPlotData(data);\n  }\n  if (isBandAxisData(xyChartData.xAxis)) {\n    retData = xyChartData.xAxis.categories.map((c, i) => [c, data[i]]);\n  }\n  if (isLinearAxisData(xyChartData.xAxis)) {\n    const min = xyChartData.xAxis.min;\n    const max = xyChartData.xAxis.max;\n    const step = (max - min) / (data.length - 1);\n    const categories = [];\n    for (let i = min; i <= max; i += step) {\n      categories.push(`${i}`);\n    }\n    retData = categories.map((c, i) => [c, data[i]]);\n  }\n  return retData;\n}\n__name(transformDataWithoutCategory, \"transformDataWithoutCategory\");\nfunction getPlotColorFromPalette(plotIndex2) {\n  return plotColorPalette[plotIndex2 === 0 ? 0 : plotIndex2 % plotColorPalette.length];\n}\n__name(getPlotColorFromPalette, \"getPlotColorFromPalette\");\nfunction setLineData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"line\",\n    strokeFill: getPlotColorFromPalette(plotIndex),\n    strokeWidth: 2,\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setLineData, \"setLineData\");\nfunction setBarData(title, data) {\n  const plotData = transformDataWithoutCategory(data);\n  xyChartData.plots.push({\n    type: \"bar\",\n    fill: getPlotColorFromPalette(plotIndex),\n    data: plotData\n  });\n  plotIndex++;\n}\n__name(setBarData, \"setBarData\");\nfunction getDrawableElem() {\n  if (xyChartData.plots.length === 0) {\n    throw Error(\"No Plot to render, please provide a plot with some data\");\n  }\n  xyChartData.title = getDiagramTitle();\n  return XYChartBuilder.build(xyChartConfig, xyChartData, xyChartThemeConfig, tmpSVGGroup);\n}\n__name(getDrawableElem, \"getDrawableElem\");\nfunction getChartThemeConfig() {\n  return xyChartThemeConfig;\n}\n__name(getChartThemeConfig, \"getChartThemeConfig\");\nfunction getChartConfig() {\n  return xyChartConfig;\n}\n__name(getChartConfig, \"getChartConfig\");\nvar clear2 = /* @__PURE__ */ __name(function() {\n  clear();\n  plotIndex = 0;\n  xyChartConfig = getChartDefaultConfig();\n  xyChartData = getChartDefaultData();\n  xyChartThemeConfig = getChartDefaultThemeConfig();\n  plotColorPalette = xyChartThemeConfig.plotColorPalette.split(\",\").map((color) => color.trim());\n  hasSetXAxis = false;\n  hasSetYAxis = false;\n}, \"clear\");\nvar xychartDb_default = {\n  getDrawableElem,\n  clear: clear2,\n  setAccTitle,\n  getAccTitle,\n  setDiagramTitle,\n  getDiagramTitle,\n  getAccDescription,\n  setAccDescription,\n  setOrientation,\n  setXAxisTitle,\n  setXAxisRangeData,\n  setXAxisBand,\n  setYAxisTitle,\n  setYAxisRangeData,\n  setLineData,\n  setBarData,\n  setTmpSVGG,\n  getChartThemeConfig,\n  getChartConfig\n};\n\n// src/diagrams/xychart/xychartRenderer.ts\nvar draw = /* @__PURE__ */ __name((txt, id, _version, diagObj) => {\n  const db = diagObj.db;\n  const themeConfig = db.getChartThemeConfig();\n  const chartConfig = db.getChartConfig();\n  function getDominantBaseLine(horizontalPos) {\n    return horizontalPos === \"top\" ? \"text-before-edge\" : \"middle\";\n  }\n  __name(getDominantBaseLine, \"getDominantBaseLine\");\n  function getTextAnchor(verticalPos) {\n    return verticalPos === \"left\" ? \"start\" : verticalPos === \"right\" ? \"end\" : \"middle\";\n  }\n  __name(getTextAnchor, \"getTextAnchor\");\n  function getTextTransformation(data) {\n    return `translate(${data.x}, ${data.y}) rotate(${data.rotation || 0})`;\n  }\n  __name(getTextTransformation, \"getTextTransformation\");\n  log.debug(\"Rendering xychart chart\\n\" + txt);\n  const svg = selectSvgElement(id);\n  const group = svg.append(\"g\").attr(\"class\", \"main\");\n  const background = group.append(\"rect\").attr(\"width\", chartConfig.width).attr(\"height\", chartConfig.height).attr(\"class\", \"background\");\n  configureSvgSize(svg, chartConfig.height, chartConfig.width, true);\n  svg.attr(\"viewBox\", `0 0 ${chartConfig.width} ${chartConfig.height}`);\n  background.attr(\"fill\", themeConfig.backgroundColor);\n  db.setTmpSVGG(svg.append(\"g\").attr(\"class\", \"mermaid-tmp-group\"));\n  const shapes = db.getDrawableElem();\n  const groups = {};\n  function getGroup(gList) {\n    let elem = group;\n    let prefix = \"\";\n    for (const [i] of gList.entries()) {\n      let parent = group;\n      if (i > 0 && groups[prefix]) {\n        parent = groups[prefix];\n      }\n      prefix += gList[i];\n      elem = groups[prefix];\n      if (!elem) {\n        elem = groups[prefix] = parent.append(\"g\").attr(\"class\", gList[i]);\n      }\n    }\n    return elem;\n  }\n  __name(getGroup, \"getGroup\");\n  for (const shape of shapes) {\n    if (shape.data.length === 0) {\n      continue;\n    }\n    const shapeGroup = getGroup(shape.groupTexts);\n    switch (shape.type) {\n      case \"rect\":\n        shapeGroup.selectAll(\"rect\").data(shape.data).enter().append(\"rect\").attr(\"x\", (data) => data.x).attr(\"y\", (data) => data.y).attr(\"width\", (data) => data.width).attr(\"height\", (data) => data.height).attr(\"fill\", (data) => data.fill).attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n      case \"text\":\n        shapeGroup.selectAll(\"text\").data(shape.data).enter().append(\"text\").attr(\"x\", 0).attr(\"y\", 0).attr(\"fill\", (data) => data.fill).attr(\"font-size\", (data) => data.fontSize).attr(\"dominant-baseline\", (data) => getDominantBaseLine(data.verticalPos)).attr(\"text-anchor\", (data) => getTextAnchor(data.horizontalPos)).attr(\"transform\", (data) => getTextTransformation(data)).text((data) => data.text);\n        break;\n      case \"path\":\n        shapeGroup.selectAll(\"path\").data(shape.data).enter().append(\"path\").attr(\"d\", (data) => data.path).attr(\"fill\", (data) => data.fill ? data.fill : \"none\").attr(\"stroke\", (data) => data.strokeFill).attr(\"stroke-width\", (data) => data.strokeWidth);\n        break;\n    }\n  }\n}, \"draw\");\nvar xychartRenderer_default = {\n  draw\n};\n\n// src/diagrams/xychart/xychartDiagram.ts\nvar diagram = {\n  parser: xychart_default,\n  db: xychartDb_default,\n  renderer: xychartRenderer_default\n};\nexport {\n  diagram\n};\n"], "names": ["band", "scale", "ordinal", "domain", "ordinalRange", "r0", "r1", "step", "bandwidth", "round", "paddingInner", "paddingOuter", "align", "rescale", "n", "reverse", "start", "stop", "values", "sequence", "i", "_", "initRange", "parser", "o", "__name", "k", "v", "o2", "l", "$V0", "$V1", "$V2", "$V3", "$V4", "$V5", "$V6", "$V7", "$V8", "$V9", "$Va", "$Vb", "$Vc", "$Vd", "$Ve", "$Vf", "$Vg", "$Vh", "$Vi", "$Vj", "$Vk", "$Vl", "$Vm", "$Vn", "$Vo", "$Vp", "$Vq", "parser2", "yytext", "yyleng", "y<PERSON><PERSON>o", "yy", "yystate", "$$", "_$", "$0", "str", "hash", "error", "input", "self", "stack", "tstack", "vstack", "lstack", "table", "TERROR", "EOF", "args", "lexer2", "sharedState", "yyloc", "ranges", "popStack", "lex", "token", "symbol", "state", "action", "r", "yyval", "p", "len", "newState", "expected", "errStr", "lexer", "ch", "lines", "oldLines", "past", "next", "pre", "c", "match", "indexed_rule", "backup", "tempMatch", "index", "rules", "condition", "yy_", "$avoiding_name_collisions", "YY_START", "<PERSON><PERSON><PERSON>", "xychart_default", "isBarPlot", "data", "isBandAxisData", "isLinearAxisData", "TextDimensionCalculatorWithFont", "parentGroup", "texts", "fontSize", "acc", "cur", "dimension", "elem", "t", "bbox", "computeDimensionOfText", "width", "height", "BAR_WIDTH_TO_TICK_WIDTH_RATIO", "MAX_OUTER_PADDING_PERCENT_FOR_WRT_LABEL", "BaseAxis", "axisConfig", "title", "textDimensionCalculator", "axisThemeConfig", "range", "axisPosition", "tick", "availableSpace", "availableHeight", "spaceRequired", "maxPadding", "heightRequired", "availableWidth", "widthRequired", "point", "drawableElement", "x", "y", "BandAxis", "categories", "scaleBand", "log", "value", "LinearAxis", "scaleLinear", "getAxis", "tmpSVGGroup2", "ChartTitle", "chartConfig", "chartData", "chartThemeConfig", "titleDimension", "drawableElem", "getChartTitleComponent", "LinePlot", "plotData", "xAxis", "yAxis", "orientation", "plotIndex2", "finalData", "d", "path", "line", "BarPlot", "barData", "boundingRect", "<PERSON><PERSON><PERSON><PERSON>", "bar<PERSON><PERSON>thHalf", "BasePlot", "plot", "linePlot", "barPlot", "getPlotComponent", "Orchestrator", "plotX", "plotY", "chartWidth", "chartHeight", "spaceUsed", "titleYEnd", "component", "XYChartBuilder", "config", "plotIndex", "tmpSVGGroup", "xyChartConfig", "getChartDefaultConfig", "xyChartThemeConfig", "getChartDefaultThemeConfig", "xyChartData", "getChartDefaultData", "plotColorPalette", "color", "hasSetXAxis", "hasSetYAxis", "defaultThemeVariables", "getThemeVariables", "getConfig", "cleanAndMerge", "defaultConfig_default", "textSanitizer", "text", "sanitizeText", "setTmpSVGG", "SVGG", "setOrientation", "setXAxisTitle", "setXAxisRangeData", "min", "max", "setXAxisBand", "setYAxisTitle", "setYAxisRangeData", "setYAxisRangeFromPlotData", "minValue", "maxValue", "prevMinValue", "prevMaxValue", "transformDataWithoutCategory", "retData", "getPlotColorFromPalette", "setLineData", "setBarData", "getDrawableElem", "getDiagramTitle", "getChartThemeConfig", "getChartConfig", "clear2", "clear", "xychartDb_default", "setAccTitle", "getAccTitle", "setDiagramTitle", "getAccDescription", "setAccDescription", "draw", "txt", "id", "_version", "diagObj", "db", "themeConfig", "getDominantBaseLine", "horizontalPos", "getTextAnchor", "verticalPos", "getTextTransformation", "svg", "selectSvgElement", "group", "background", "configureSvgSize", "shapes", "groups", "getGroup", "gList", "prefix", "parent", "shape", "shapeGroup", "xychartRenderer_default", "diagram"], "mappings": "ifAIe,SAASA,IAAO,CAC7B,IAAIC,EAAQC,KAAU,QAAQ,MAAS,EACnCC,EAASF,EAAM,OACfG,EAAeH,EAAM,MACrBI,EAAK,EACLC,EAAK,EACLC,EACAC,EACAC,EAAQ,GACRC,EAAe,EACfC,EAAe,EACfC,EAAQ,GAEZ,OAAOX,EAAM,QAEb,SAASY,GAAU,CACjB,IAAIC,EAAIX,EAAM,EAAG,OACbY,EAAUT,EAAKD,EACfW,EAAQD,EAAUT,EAAKD,EACvBY,EAAOF,EAAUV,EAAKC,EAC1BC,GAAQU,EAAOD,GAAS,KAAK,IAAI,EAAGF,EAAIJ,EAAeC,EAAe,CAAC,EACnEF,IAAOF,EAAO,KAAK,MAAMA,CAAI,GACjCS,IAAUC,EAAOD,EAAQT,GAAQO,EAAIJ,IAAiBE,EACtDJ,EAAYD,GAAQ,EAAIG,GACpBD,IAAOO,EAAQ,KAAK,MAAMA,CAAK,EAAGR,EAAY,KAAK,MAAMA,CAAS,GACtE,IAAIU,EAASC,GAASL,CAAC,EAAE,IAAI,SAASM,EAAG,CAAE,OAAOJ,EAAQT,EAAOa,CAAI,CAAA,EACrE,OAAOhB,EAAaW,EAAUG,EAAO,QAAS,EAAGA,CAAM,CACxD,CAED,OAAAjB,EAAM,OAAS,SAASoB,EAAG,CACzB,OAAO,UAAU,QAAUlB,EAAOkB,CAAC,EAAGR,EAAO,GAAMV,GACvD,EAEEF,EAAM,MAAQ,SAASoB,EAAG,CACxB,OAAO,UAAU,QAAU,CAAChB,EAAIC,CAAE,EAAIe,EAAGhB,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIO,EAAS,GAAI,CAACR,EAAIC,CAAE,CACrF,EAEEL,EAAM,WAAa,SAASoB,EAAG,CAC7B,MAAO,CAAChB,EAAIC,CAAE,EAAIe,EAAGhB,EAAK,CAACA,EAAIC,EAAK,CAACA,EAAIG,EAAQ,GAAMI,EAAO,CAClE,EAEEZ,EAAM,UAAY,UAAW,CAC3B,OAAOO,CACX,EAEEP,EAAM,KAAO,UAAW,CACtB,OAAOM,CACX,EAEEN,EAAM,MAAQ,SAASoB,EAAG,CACxB,OAAO,UAAU,QAAUZ,EAAQ,CAAC,CAACY,EAAGR,EAAS,GAAIJ,CACzD,EAEER,EAAM,QAAU,SAASoB,EAAG,CAC1B,OAAO,UAAU,QAAUX,EAAe,KAAK,IAAI,EAAGC,EAAe,CAACU,CAAC,EAAGR,EAAO,GAAMH,CAC3F,EAEET,EAAM,aAAe,SAASoB,EAAG,CAC/B,OAAO,UAAU,QAAUX,EAAe,KAAK,IAAI,EAAGW,CAAC,EAAGR,EAAS,GAAIH,CAC3E,EAEET,EAAM,aAAe,SAASoB,EAAG,CAC/B,OAAO,UAAU,QAAUV,EAAe,CAACU,EAAGR,EAAS,GAAIF,CAC/D,EAEEV,EAAM,MAAQ,SAASoB,EAAG,CACxB,OAAO,UAAU,QAAUT,EAAQ,KAAK,IAAI,EAAG,KAAK,IAAI,EAAGS,CAAC,CAAC,EAAGR,EAAO,GAAMD,CACjF,EAEEX,EAAM,KAAO,UAAW,CACtB,OAAOD,GAAKG,EAAM,EAAI,CAACE,EAAIC,CAAE,CAAC,EACzB,MAAMG,CAAK,EACX,aAAaC,CAAY,EACzB,aAAaC,CAAY,EACzB,MAAMC,CAAK,CACpB,EAESU,GAAU,MAAMT,EAAS,EAAE,SAAS,CAC7C,CCvDA,IAAIU,GAAS,UAAW,CACtB,IAAIC,EAAoBC,EAAO,SAASC,EAAGC,EAAGC,EAAIC,EAAG,CACnD,IAAKD,EAAKA,GAAM,CAAE,EAAEC,EAAIH,EAAE,OAAQG,IAAKD,EAAGF,EAAEG,CAAC,CAAC,EAAIF,EAAG,CACrD,OAAOC,CACX,EAAK,GAAG,EAAGE,EAAM,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,CAAC,EAAGC,EAAM,CAAC,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,EAAE,EAAGC,EAAM,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,EAAM,CAAC,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAGC,GAAM,CAAC,EAAG,EAAE,EACzpBC,GAAU,CACZ,MAAuBhC,EAAO,UAAiB,CAC9C,EAAE,OAAO,EACV,GAAI,CAAE,EACN,SAAU,CAAE,MAAS,EAAG,MAAS,EAAG,IAAO,EAAG,QAAW,EAAG,YAAe,EAAG,SAAY,EAAG,kBAAqB,EAAG,UAAa,EAAG,MAAS,GAAI,KAAQ,GAAI,OAAU,GAAI,WAAc,GAAI,OAAU,GAAI,WAAc,GAAI,KAAQ,GAAI,SAAY,GAAI,IAAO,GAAI,UAAa,GAAI,gBAAmB,GAAI,UAAa,GAAI,gBAAmB,GAAI,0BAA6B,GAAI,oBAAuB,GAAI,sBAAyB,GAAI,kBAAqB,GAAI,oBAAuB,GAAI,MAAS,GAAI,UAAa,GAAI,SAAY,GAAI,gBAAmB,GAAI,oBAAuB,GAAI,UAAa,GAAI,QAAW,GAAI,KAAQ,GAAI,IAAO,GAAI,SAAY,GAAI,IAAO,GAAI,OAAU,GAAI,cAAiB,GAAI,IAAO,GAAI,IAAO,GAAI,MAAS,GAAI,KAAQ,GAAI,OAAU,GAAI,KAAQ,GAAI,IAAO,GAAI,KAAQ,GAAI,MAAS,GAAI,WAAc,GAAI,QAAW,EAAG,KAAQ,CAAG,EAC/0B,WAAY,CAAE,EAAG,QAAS,EAAG,UAAW,EAAG,oBAAqB,GAAI,QAAS,GAAI,SAAU,GAAI,SAAU,GAAI,OAAQ,GAAI,MAAO,GAAI,YAAa,GAAI,kBAAmB,GAAI,YAAa,GAAI,kBAAmB,GAAI,4BAA6B,GAAI,sBAAuB,GAAI,oBAAqB,GAAI,sBAAuB,GAAI,QAAS,GAAI,kBAAmB,GAAI,UAAW,GAAI,OAAQ,GAAI,MAAO,GAAI,MAAO,GAAI,SAAU,GAAI,MAAO,GAAI,MAAO,GAAI,QAAS,GAAI,OAAQ,GAAI,SAAU,GAAI,OAAQ,GAAI,MAAO,GAAI,OAAQ,GAAI,QAAS,GAAI,YAAc,EACliB,aAAc,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,EAAG,CAAC,GAAI,CAAC,CAAC,EACtc,cAA+BA,EAAO,SAAmBiC,EAAQC,EAAQC,EAAUC,EAAIC,EAASC,EAAIC,EAAI,CACtG,IAAIC,EAAKF,EAAG,OAAS,EACrB,OAAQD,EAAO,CACb,IAAK,GACHD,EAAG,eAAeE,EAAGE,CAAE,CAAC,EACxB,MACF,IAAK,GACHJ,EAAG,gBAAgBE,EAAGE,CAAE,EAAE,KAAK,KAAI,CAAE,EACrC,MACF,IAAK,IACHJ,EAAG,YAAY,CAAE,KAAM,GAAI,KAAM,QAAUE,EAAGE,CAAE,CAAC,EACjD,MACF,IAAK,IACHJ,EAAG,YAAYE,EAAGE,EAAK,CAAC,EAAGF,EAAGE,CAAE,CAAC,EACjC,MACF,IAAK,IACHJ,EAAG,WAAW,CAAE,KAAM,GAAI,KAAM,QAAUE,EAAGE,CAAE,CAAC,EAChD,MACF,IAAK,IACHJ,EAAG,WAAWE,EAAGE,EAAK,CAAC,EAAGF,EAAGE,CAAE,CAAC,EAChC,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,CAAE,EAAE,KAAI,EACpBJ,EAAG,YAAY,KAAK,CAAC,EACrB,MACF,IAAK,IACL,IAAK,IACH,KAAK,EAAIE,EAAGE,CAAE,EAAE,KAAI,EACpBJ,EAAG,kBAAkB,KAAK,CAAC,EAC3B,MACF,IAAK,IACH,KAAK,EAAIE,EAAGE,EAAK,CAAC,EAClB,MACF,IAAK,IACH,KAAK,EAAI,CAAC,OAAOF,EAAGE,EAAK,CAAC,CAAC,EAAG,GAAGF,EAAGE,CAAE,CAAC,EACvC,MACF,IAAK,IACH,KAAK,EAAI,CAAC,OAAOF,EAAGE,CAAE,CAAC,CAAC,EACxB,MACF,IAAK,IACHJ,EAAG,cAAcE,EAAGE,CAAE,CAAC,EACvB,MACF,IAAK,IACHJ,EAAG,cAAcE,EAAGE,EAAK,CAAC,CAAC,EAC3B,MACF,IAAK,IACHJ,EAAG,cAAc,CAAE,KAAM,OAAQ,KAAM,EAAE,CAAE,EAC3C,MACF,IAAK,IACHA,EAAG,aAAaE,EAAGE,CAAE,CAAC,EACtB,MACF,IAAK,IACHJ,EAAG,kBAAkB,OAAOE,EAAGE,EAAK,CAAC,CAAC,EAAG,OAAOF,EAAGE,CAAE,CAAC,CAAC,EACvD,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAClB,MACF,IAAK,IACH,KAAK,EAAI,CAACF,EAAGE,EAAK,CAAC,EAAG,GAAGF,EAAGE,CAAE,CAAC,EAC/B,MACF,IAAK,IACH,KAAK,EAAI,CAACF,EAAGE,CAAE,CAAC,EAChB,MACF,IAAK,IACHJ,EAAG,cAAcE,EAAGE,CAAE,CAAC,EACvB,MACF,IAAK,IACHJ,EAAG,cAAcE,EAAGE,EAAK,CAAC,CAAC,EAC3B,MACF,IAAK,IACHJ,EAAG,cAAc,CAAE,KAAM,OAAQ,KAAM,EAAE,CAAE,EAC3C,MACF,IAAK,IACHA,EAAG,kBAAkB,OAAOE,EAAGE,EAAK,CAAC,CAAC,EAAG,OAAOF,EAAGE,CAAE,CAAC,CAAC,EACvD,MACF,IAAK,IACH,KAAK,EAAI,CAAE,KAAMF,EAAGE,CAAE,EAAG,KAAM,QAC/B,MACF,IAAK,IACH,KAAK,EAAI,CAAE,KAAMF,EAAGE,CAAE,EAAG,KAAM,QAC/B,MACF,IAAK,IACH,KAAK,EAAI,CAAE,KAAMF,EAAGE,CAAE,EAAG,KAAM,YAC/B,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,CAAE,EACd,MACF,IAAK,IACH,KAAK,EAAIF,EAAGE,EAAK,CAAC,EAAI,GAAKF,EAAGE,CAAE,EAChC,KACH,CACF,EAAE,WAAW,EACd,MAAO,CAACzC,EAAEM,EAAKC,EAAK,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,CAAE,EAAG,CAAE,EAAG,CAAC,CAAC,GAAKX,EAAEM,EAAKC,EAAK,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,EAAGX,EAAEM,EAAKC,EAAK,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAGC,EAAK,EAAG,CAAC,EAAG,EAAE,EAAG,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,EAAG,EAAG,GAAI,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,CAAC,EAAIX,EAAEY,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGZ,EAAEY,EAAK,CAAC,EAAG,EAAE,CAAC,EAAGZ,EAAEY,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAG,EAAEZ,EAAEM,EAAKC,EAAK,CAAE,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,EAAGC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,CAAE,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAG,EAAEX,EAAEY,EAAK,CAAC,EAAG,CAAC,CAAC,EAAGZ,EAAEM,EAAK,CAAC,EAAG,CAAC,EAAG,CAAE,EAAG,GAAI,GAAIG,EAAK,GAAIC,EAAK,GAAIC,CAAG,CAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIE,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIC,EAAK,GAAIC,GAAK,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIb,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIG,GAAK,GAAI,GAAI,GAAI,GAAI,GAAId,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAII,EAAK,GAAI,GAAI,GAAIf,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,GAAO,CAAE,GAAI,GAAI,GAAI,GAAI,GAAII,EAAK,GAAI,GAAI,GAAIf,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,EAAI,CAAE,GAAI,CAAC,EAAG,EAAE,CAAG,EAAE,CAAE,GAAI,CAAC,EAAG,EAAE,CAAC,EAAIxB,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,EAAG,CAAC,EAAG,CAAC,CAAC,EAAI7B,EAAE6B,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG7B,EAAE6B,EAAK,CAAC,EAAG,CAAC,CAAC,EAAG7B,EAAE8B,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAIf,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,CAAE,EAAGxB,EAAE8B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG9B,EAAE8B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG9B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAIJ,EAAK,GAAIC,GAAK,EAAG1B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAG,EAAE,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIhB,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAG,EAAIxB,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,EAAG,CAAE,GAAI,GAAI,GAAIF,EAAK,CAAA,EAAG3B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAC,EAAI7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,GAAI,GAAID,GAAO,CAAE,GAAI,GAAI,GAAII,EAAK,EAAEhC,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,GAAI,GAAID,CAAK,EAAE5B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG7B,EAAE+B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG/B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAC,EAAI,CAAE,GAAI,CAAC,EAAG,EAAE,CAAG,EAAE,CAAE,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,CAAG,EAAE7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAG,EAAE7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,CAAC,EAAG,EAAE,CAAC,EAAI,CAAE,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,CAAC,EAAI7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAIhB,EAAK,GAAIC,EAAK,GAAI,GAAI,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,EAAK,GAAIC,CAAK,EAAExB,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG7B,EAAE6B,EAAK,CAAC,EAAG,EAAE,CAAC,EAAG,CAAE,GAAI,GAAI,GAAIG,EAAK,EAAE,CAAE,GAAI,CAAC,EAAG,EAAE,CAAC,EAAI,CAAE,GAAI,CAAC,EAAG,EAAE,EAAG,EAC7tF,eAAgB,CAAE,EAAG,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,CAAC,EAAG,GAAI,CAAC,EAAG,EAAE,EAAG,GAAI,CAAC,EAAG,EAAE,CAAG,EAC/E,WAA4B/B,EAAO,SAAoByC,EAAKC,EAAM,CAChE,GAAIA,EAAK,YACP,KAAK,MAAMD,CAAG,MACT,CACL,IAAIE,EAAQ,IAAI,MAAMF,CAAG,EACzB,MAAAE,EAAM,KAAOD,EACPC,CACP,CACF,EAAE,YAAY,EACf,MAAuB3C,EAAO,SAAe4C,EAAO,CAC/C,IAACC,EAAO,KAAMC,EAAQ,CAAC,CAAC,EAAGC,EAAS,CAAE,EAAEC,EAAS,CAAC,IAAI,EAAGC,EAAS,CAAE,EAAEC,EAAQ,KAAK,MAAOjB,EAAS,GAAIE,EAAW,EAAGD,GAAS,EAAmBiB,GAAS,EAAGC,GAAM,EAClKC,GAAOJ,EAAO,MAAM,KAAK,UAAW,CAAC,EACrCK,EAAS,OAAO,OAAO,KAAK,KAAK,EACjCC,EAAc,CAAE,GAAI,CAAA,GACxB,QAAStD,MAAK,KAAK,GACb,OAAO,UAAU,eAAe,KAAK,KAAK,GAAIA,EAAC,IACjDsD,EAAY,GAAGtD,EAAC,EAAI,KAAK,GAAGA,EAAC,GAGjCqD,EAAO,SAASV,EAAOW,EAAY,EAAE,EACrCA,EAAY,GAAG,MAAQD,EACvBC,EAAY,GAAG,OAAS,KACpB,OAAOD,EAAO,OAAU,MAC1BA,EAAO,OAAS,IAElB,IAAIE,GAAQF,EAAO,OACnBL,EAAO,KAAKO,EAAK,EACjB,IAAIC,GAASH,EAAO,SAAWA,EAAO,QAAQ,OAC1C,OAAOC,EAAY,GAAG,YAAe,WACvC,KAAK,WAAaA,EAAY,GAAG,WAEjC,KAAK,WAAa,OAAO,eAAe,IAAI,EAAE,WAEhD,SAASG,GAASrE,EAAG,CACnByD,EAAM,OAASA,EAAM,OAAS,EAAIzD,EAClC2D,EAAO,OAASA,EAAO,OAAS3D,EAChC4D,EAAO,OAASA,EAAO,OAAS5D,CACjC,CACDW,EAAO0D,GAAU,UAAU,EAC3B,SAASC,IAAM,CACb,IAAIC,EACJ,OAAAA,EAAQb,EAAO,IAAG,GAAMO,EAAO,IAAK,GAAIF,GACpC,OAAOQ,GAAU,WACfA,aAAiB,QACnBb,EAASa,EACTA,EAAQb,EAAO,OAEjBa,EAAQf,EAAK,SAASe,CAAK,GAAKA,GAE3BA,CACR,CACD5D,EAAO2D,GAAK,KAAK,EAEjB,QADIE,EAAwBC,EAAOC,EAAWC,GAAGC,EAAQ,CAAE,EAAEC,EAAGC,EAAKC,GAAUC,IAClE,CAUX,GATAP,EAAQhB,EAAMA,EAAM,OAAS,CAAC,EAC1B,KAAK,eAAegB,CAAK,EAC3BC,EAAS,KAAK,eAAeD,CAAK,IAE9BD,IAAW,MAAQ,OAAOA,EAAU,OACtCA,EAASF,GAAG,GAEdI,EAASb,EAAMY,CAAK,GAAKZ,EAAMY,CAAK,EAAED,CAAM,GAE1C,OAAOE,EAAW,KAAe,CAACA,EAAO,QAAU,CAACA,EAAO,CAAC,EAAG,CACjE,IAAIO,GAAS,GACbD,EAAW,CAAA,EACX,IAAKH,KAAKhB,EAAMY,CAAK,EACf,KAAK,WAAWI,CAAC,GAAKA,EAAIf,IAC5BkB,EAAS,KAAK,IAAM,KAAK,WAAWH,CAAC,EAAI,GAAG,EAG5CZ,EAAO,aACTgB,GAAS,wBAA0BnC,EAAW,GAAK;AAAA,EAAQmB,EAAO,aAAc,EAAG;AAAA,YAAiBe,EAAS,KAAK,IAAI,EAAI,WAAa,KAAK,WAAWR,CAAM,GAAKA,GAAU,IAE5KS,GAAS,wBAA0BnC,EAAW,GAAK,iBAAmB0B,GAAUT,GAAM,eAAiB,KAAO,KAAK,WAAWS,CAAM,GAAKA,GAAU,KAErJ,KAAK,WAAWS,GAAQ,CACtB,KAAMhB,EAAO,MACb,MAAO,KAAK,WAAWO,CAAM,GAAKA,EAClC,KAAMP,EAAO,SACb,IAAKE,GACL,SAAAa,CACZ,CAAW,CACF,CACD,GAAIN,EAAO,CAAC,YAAa,OAASA,EAAO,OAAS,EAChD,MAAM,IAAI,MAAM,oDAAsDD,EAAQ,YAAcD,CAAM,EAEpG,OAAQE,EAAO,CAAC,EAAC,CACf,IAAK,GACHjB,EAAM,KAAKe,CAAM,EACjBb,EAAO,KAAKM,EAAO,MAAM,EACzBL,EAAO,KAAKK,EAAO,MAAM,EACzBR,EAAM,KAAKiB,EAAO,CAAC,CAAC,EACpBF,EAAS,KAEP3B,GAASoB,EAAO,OAChBrB,EAASqB,EAAO,OAChBnB,EAAWmB,EAAO,SAClBE,GAAQF,EAAO,OAQjB,MACF,IAAK,GAwBH,GAvBAa,EAAM,KAAK,aAAaJ,EAAO,CAAC,CAAC,EAAE,CAAC,EACpCE,EAAM,EAAIjB,EAAOA,EAAO,OAASmB,CAAG,EACpCF,EAAM,GAAK,CACT,WAAYhB,EAAOA,EAAO,QAAUkB,GAAO,EAAE,EAAE,WAC/C,UAAWlB,EAAOA,EAAO,OAAS,CAAC,EAAE,UACrC,aAAcA,EAAOA,EAAO,QAAUkB,GAAO,EAAE,EAAE,aACjD,YAAalB,EAAOA,EAAO,OAAS,CAAC,EAAE,WACrD,EACgBQ,KACFQ,EAAM,GAAG,MAAQ,CACfhB,EAAOA,EAAO,QAAUkB,GAAO,EAAE,EAAE,MAAM,CAAC,EAC1ClB,EAAOA,EAAO,OAAS,CAAC,EAAE,MAAM,CAAC,CACjD,GAEYe,GAAI,KAAK,cAAc,MAAMC,EAAO,CAClChC,EACAC,GACAC,EACAoB,EAAY,GACZQ,EAAO,CAAC,EACRf,EACAC,CACd,EAAc,OAAOI,EAAI,CAAC,EACV,OAAOW,GAAM,IACf,OAAOA,GAELG,IACFrB,EAAQA,EAAM,MAAM,EAAG,GAAKqB,EAAM,CAAC,EACnCnB,EAASA,EAAO,MAAM,EAAG,GAAKmB,CAAG,EACjClB,EAASA,EAAO,MAAM,EAAG,GAAKkB,CAAG,GAEnCrB,EAAM,KAAK,KAAK,aAAaiB,EAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAC1Cf,EAAO,KAAKiB,EAAM,CAAC,EACnBhB,EAAO,KAAKgB,EAAM,EAAE,EACpBG,GAAWlB,EAAMJ,EAAMA,EAAM,OAAS,CAAC,CAAC,EAAEA,EAAMA,EAAM,OAAS,CAAC,CAAC,EACjEA,EAAM,KAAKsB,EAAQ,EACnB,MACF,IAAK,GACH,MAAO,EACV,CACF,CACD,MAAO,EACR,EAAE,OAAO,CACd,EACMG,GAAwB,UAAW,CACrC,IAAIjB,EAAS,CACX,IAAK,EACL,WAA4BtD,EAAO,SAAoByC,EAAKC,EAAM,CAChE,GAAI,KAAK,GAAG,OACV,KAAK,GAAG,OAAO,WAAWD,EAAKC,CAAI,MAEnC,OAAM,IAAI,MAAMD,CAAG,CAEtB,EAAE,YAAY,EAEf,SAA0BzC,EAAO,SAAS4C,EAAOR,EAAI,CACnD,YAAK,GAAKA,GAAM,KAAK,IAAM,CAAA,EAC3B,KAAK,OAASQ,EACd,KAAK,MAAQ,KAAK,WAAa,KAAK,KAAO,GAC3C,KAAK,SAAW,KAAK,OAAS,EAC9B,KAAK,OAAS,KAAK,QAAU,KAAK,MAAQ,GAC1C,KAAK,eAAiB,CAAC,SAAS,EAChC,KAAK,OAAS,CACZ,WAAY,EACZ,aAAc,EACd,UAAW,EACX,YAAa,CACvB,EACY,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,EAAG,CAAC,GAE3B,KAAK,OAAS,EACP,IACR,EAAE,UAAU,EAEb,MAAuB5C,EAAO,UAAW,CACvC,IAAIwE,EAAK,KAAK,OAAO,CAAC,EACtB,KAAK,QAAUA,EACf,KAAK,SACL,KAAK,SACL,KAAK,OAASA,EACd,KAAK,SAAWA,EAChB,IAAIC,EAAQD,EAAG,MAAM,iBAAiB,EACtC,OAAIC,GACF,KAAK,WACL,KAAK,OAAO,aAEZ,KAAK,OAAO,cAEV,KAAK,QAAQ,QACf,KAAK,OAAO,MAAM,CAAC,IAErB,KAAK,OAAS,KAAK,OAAO,MAAM,CAAC,EAC1BD,CACR,EAAE,OAAO,EAEV,MAAuBxE,EAAO,SAASwE,EAAI,CACzC,IAAIL,EAAMK,EAAG,OACTC,EAAQD,EAAG,MAAM,eAAe,EACpC,KAAK,OAASA,EAAK,KAAK,OACxB,KAAK,OAAS,KAAK,OAAO,OAAO,EAAG,KAAK,OAAO,OAASL,CAAG,EAC5D,KAAK,QAAUA,EACf,IAAIO,EAAW,KAAK,MAAM,MAAM,eAAe,EAC/C,KAAK,MAAQ,KAAK,MAAM,OAAO,EAAG,KAAK,MAAM,OAAS,CAAC,EACvD,KAAK,QAAU,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,CAAC,EACzDD,EAAM,OAAS,IACjB,KAAK,UAAYA,EAAM,OAAS,GAElC,IAAIT,EAAI,KAAK,OAAO,MACpB,YAAK,OAAS,CACZ,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,aAC1B,YAAaS,GAASA,EAAM,SAAWC,EAAS,OAAS,KAAK,OAAO,aAAe,GAAKA,EAASA,EAAS,OAASD,EAAM,MAAM,EAAE,OAASA,EAAM,CAAC,EAAE,OAAS,KAAK,OAAO,aAAeN,CAClM,EACY,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAACH,EAAE,CAAC,EAAGA,EAAE,CAAC,EAAI,KAAK,OAASG,CAAG,GAErD,KAAK,OAAS,KAAK,OAAO,OACnB,IACR,EAAE,OAAO,EAEV,KAAsBnE,EAAO,UAAW,CACtC,YAAK,MAAQ,GACN,IACR,EAAE,MAAM,EAET,OAAwBA,EAAO,UAAW,CACxC,GAAI,KAAK,QAAQ,gBACf,KAAK,WAAa,OAElB,QAAO,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAAqI,KAAK,eAAgB,CAChO,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACvB,CAAW,EAEH,OAAO,IACR,EAAE,QAAQ,EAEX,KAAsBA,EAAO,SAASX,EAAG,CACvC,KAAK,MAAM,KAAK,MAAM,MAAMA,CAAC,CAAC,CAC/B,EAAE,MAAM,EAET,UAA2BW,EAAO,UAAW,CAC3C,IAAI2E,EAAO,KAAK,QAAQ,OAAO,EAAG,KAAK,QAAQ,OAAS,KAAK,MAAM,MAAM,EACzE,OAAQA,EAAK,OAAS,GAAK,MAAQ,IAAMA,EAAK,OAAO,GAAG,EAAE,QAAQ,MAAO,EAAE,CAC5E,EAAE,WAAW,EAEd,cAA+B3E,EAAO,UAAW,CAC/C,IAAI4E,EAAO,KAAK,MAChB,OAAIA,EAAK,OAAS,KAChBA,GAAQ,KAAK,OAAO,OAAO,EAAG,GAAKA,EAAK,MAAM,IAExCA,EAAK,OAAO,EAAG,EAAE,GAAKA,EAAK,OAAS,GAAK,MAAQ,KAAK,QAAQ,MAAO,EAAE,CAChF,EAAE,eAAe,EAElB,aAA8B5E,EAAO,UAAW,CAC9C,IAAI6E,EAAM,KAAK,YACXC,EAAI,IAAI,MAAMD,EAAI,OAAS,CAAC,EAAE,KAAK,GAAG,EAC1C,OAAOA,EAAM,KAAK,cAAe,EAAG;AAAA,EAAOC,EAAI,GAChD,EAAE,cAAc,EAEjB,WAA4B9E,EAAO,SAAS+E,EAAOC,EAAc,CAC/D,IAAIpB,EAAOa,EAAOQ,EAmDlB,GAlDI,KAAK,QAAQ,kBACfA,EAAS,CACP,SAAU,KAAK,SACf,OAAQ,CACN,WAAY,KAAK,OAAO,WACxB,UAAW,KAAK,UAChB,aAAc,KAAK,OAAO,aAC1B,YAAa,KAAK,OAAO,WAC1B,EACD,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,QAAS,KAAK,QACd,QAAS,KAAK,QACd,OAAQ,KAAK,OACb,OAAQ,KAAK,OACb,MAAO,KAAK,MACZ,OAAQ,KAAK,OACb,GAAI,KAAK,GACT,eAAgB,KAAK,eAAe,MAAM,CAAC,EAC3C,KAAM,KAAK,IACvB,EACc,KAAK,QAAQ,SACfA,EAAO,OAAO,MAAQ,KAAK,OAAO,MAAM,MAAM,CAAC,IAGnDR,EAAQM,EAAM,CAAC,EAAE,MAAM,iBAAiB,EACpCN,IACF,KAAK,UAAYA,EAAM,QAEzB,KAAK,OAAS,CACZ,WAAY,KAAK,OAAO,UACxB,UAAW,KAAK,SAAW,EAC3B,aAAc,KAAK,OAAO,YAC1B,YAAaA,EAAQA,EAAMA,EAAM,OAAS,CAAC,EAAE,OAASA,EAAMA,EAAM,OAAS,CAAC,EAAE,MAAM,QAAQ,EAAE,CAAC,EAAE,OAAS,KAAK,OAAO,YAAcM,EAAM,CAAC,EAAE,MACvJ,EACQ,KAAK,QAAUA,EAAM,CAAC,EACtB,KAAK,OAASA,EAAM,CAAC,EACrB,KAAK,QAAUA,EACf,KAAK,OAAS,KAAK,OAAO,OACtB,KAAK,QAAQ,SACf,KAAK,OAAO,MAAQ,CAAC,KAAK,OAAQ,KAAK,QAAU,KAAK,MAAM,GAE9D,KAAK,MAAQ,GACb,KAAK,WAAa,GAClB,KAAK,OAAS,KAAK,OAAO,MAAMA,EAAM,CAAC,EAAE,MAAM,EAC/C,KAAK,SAAWA,EAAM,CAAC,EACvBnB,EAAQ,KAAK,cAAc,KAAK,KAAM,KAAK,GAAI,KAAMoB,EAAc,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAClH,KAAK,MAAQ,KAAK,SACpB,KAAK,KAAO,IAEVpB,EACF,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1B,QAAS3D,KAAKgF,EACZ,KAAKhF,CAAC,EAAIgF,EAAOhF,CAAC,EAEpB,MAAO,EACR,CACD,MAAO,EACR,EAAE,YAAY,EAEf,KAAsBD,EAAO,UAAW,CACtC,GAAI,KAAK,KACP,OAAO,KAAK,IAET,KAAK,SACR,KAAK,KAAO,IAEd,IAAI4D,EAAOmB,EAAOG,EAAWC,EACxB,KAAK,QACR,KAAK,OAAS,GACd,KAAK,MAAQ,IAGf,QADIC,EAAQ,KAAK,gBACRzF,EAAI,EAAGA,EAAIyF,EAAM,OAAQzF,IAEhC,GADAuF,EAAY,KAAK,OAAO,MAAM,KAAK,MAAME,EAAMzF,CAAC,CAAC,CAAC,EAC9CuF,IAAc,CAACH,GAASG,EAAU,CAAC,EAAE,OAASH,EAAM,CAAC,EAAE,SAGzD,GAFAA,EAAQG,EACRC,EAAQxF,EACJ,KAAK,QAAQ,gBAAiB,CAEhC,GADAiE,EAAQ,KAAK,WAAWsB,EAAWE,EAAMzF,CAAC,CAAC,EACvCiE,IAAU,GACZ,OAAOA,EACF,GAAI,KAAK,WAAY,CAC1BmB,EAAQ,GACR,QAChB,KACgB,OAAO,EAEV,SAAU,CAAC,KAAK,QAAQ,KACvB,MAIN,OAAIA,GACFnB,EAAQ,KAAK,WAAWmB,EAAOK,EAAMD,CAAK,CAAC,EACvCvB,IAAU,GACLA,EAEF,IAEL,KAAK,SAAW,GACX,KAAK,IAEL,KAAK,WAAW,0BAA4B,KAAK,SAAW,GAAK;AAAA,EAA2B,KAAK,eAAgB,CACtH,KAAM,GACN,MAAO,KACP,KAAM,KAAK,QACvB,CAAW,CAEJ,EAAE,MAAM,EAET,IAAqB5D,EAAO,UAAe,CACzC,IAAIgE,EAAI,KAAK,OACb,OAAIA,GAGK,KAAK,KAEf,EAAE,KAAK,EAER,MAAuBhE,EAAO,SAAeqF,EAAW,CACtD,KAAK,eAAe,KAAKA,CAAS,CACnC,EAAE,OAAO,EAEV,SAA0BrF,EAAO,UAAoB,CACnD,IAAIX,EAAI,KAAK,eAAe,OAAS,EACrC,OAAIA,EAAI,EACC,KAAK,eAAe,MAEpB,KAAK,eAAe,CAAC,CAE/B,EAAE,UAAU,EAEb,cAA+BW,EAAO,UAAyB,CAC7D,OAAI,KAAK,eAAe,QAAU,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,EAC3E,KAAK,WAAW,KAAK,eAAe,KAAK,eAAe,OAAS,CAAC,CAAC,EAAE,MAErE,KAAK,WAAW,QAAW,KAErC,EAAE,eAAe,EAElB,SAA0BA,EAAO,SAAkBX,EAAG,CAEpD,OADAA,EAAI,KAAK,eAAe,OAAS,EAAI,KAAK,IAAIA,GAAK,CAAC,EAChDA,GAAK,EACA,KAAK,eAAeA,CAAC,EAErB,SAEV,EAAE,UAAU,EAEb,UAA2BW,EAAO,SAAmBqF,EAAW,CAC9D,KAAK,MAAMA,CAAS,CACrB,EAAE,WAAW,EAEd,eAAgCrF,EAAO,UAA0B,CAC/D,OAAO,KAAK,eAAe,MAC5B,EAAE,gBAAgB,EACnB,QAAS,CAAE,mBAAoB,EAAM,EACrC,cAA+BA,EAAO,SAAmBoC,EAAIkD,EAAKC,EAA2BC,EAAU,CAErG,OAAQD,EAAyB,CAC/B,IAAK,GACH,MACF,IAAK,GACH,MACF,IAAK,GACH,YAAK,SAAQ,EACN,GAET,IAAK,GACH,YAAK,SAAQ,EACN,GAET,IAAK,GACH,MAAO,IAET,IAAK,GACH,MACF,IAAK,GACH,MAAO,IAET,IAAK,GACH,YAAK,UAAU,WAAW,EACnB,GAET,IAAK,GACH,YAAK,SAAQ,EACN,kBAET,IAAK,GACH,YAAK,UAAU,WAAW,EACnB,GAET,IAAK,IACH,YAAK,SAAQ,EACN,kBAET,IAAK,IACH,KAAK,UAAU,qBAAqB,EACpC,MACF,IAAK,IACH,KAAK,SAAQ,EACb,MACF,IAAK,IACH,MAAO,4BAET,IAAK,IACH,MAAO,GAET,IAAK,IACH,MAAO,GAET,IAAK,IACH,YAAK,UAAU,WAAW,EACnB,SAET,IAAK,IACH,YAAK,UAAU,WAAW,EACnB,SAET,IAAK,IACH,YAAK,UAAU,gBAAgB,EACxB,GAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,YAAK,UAAU,MAAM,EACd,GAET,IAAK,IACH,YAAK,UAAU,MAAM,EACd,GAET,IAAK,IACH,YAAK,UAAU,YAAY,EACpB,GAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,YAAK,SAAQ,EACN,GAET,IAAK,IACH,KAAK,SAAQ,EACb,MACF,IAAK,IACH,KAAK,UAAU,QAAQ,EACvB,MACF,IAAK,IACH,KAAK,SAAQ,EACb,MACF,IAAK,IACH,MAAO,MAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,QAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,IAET,IAAK,IACH,MACF,IAAK,IACH,MAAO,IAET,IAAK,IACH,MAAO,GAEV,CACF,EAAE,WAAW,EACd,MAAO,CAAC,uBAAwB,sBAAuB,gBAAiB,gBAAiB,gBAAiB,iBAAkB,gBAAiB,wBAAyB,wBAAyB,wBAAyB,wBAAyB,yBAA0B,WAAY,eAAgB,uBAAwB,gCAAiC,iBAAkB,iBAAkB,WAAY,YAAa,eAAgB,cAAe,WAAY,qCAAsC,WAAY,iLAAkL,YAAa,YAAa,cAAe,WAAY,WAAY,kBAAmB,UAAW,WAAY,UAAW,UAAW,WAAY,UAAW,aAAc,WAAY,UAAW,UAAW,eAAgB,YAAa,UAAW,SAAS,EACr6B,WAAY,CAAE,WAAc,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAE,KAAQ,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAI,EAAI,eAAkB,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAM,EAAE,UAAa,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAI,EAAI,oBAAuB,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,EAAO,EAAE,UAAa,CAAE,MAAS,CAAC,EAAE,EAAG,UAAa,EAAO,EAAE,UAAa,CAAE,MAAS,CAAC,CAAC,EAAG,UAAa,EAAK,EAAI,MAAS,CAAE,MAAS,CAAE,EAAE,UAAa,EAAK,EAAI,UAAa,CAAE,MAAS,CAAE,EAAE,UAAa,EAAK,EAAI,OAAU,CAAE,MAAS,CAAC,GAAI,EAAE,EAAG,UAAa,EAAO,EAAE,QAAW,CAAE,MAAS,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAAG,UAAa,EAAI,CAAI,CAC/sC,EACI,OAAOjC,CACX,IACEtB,GAAQ,MAAQuC,GAChB,SAASkB,GAAS,CAChB,KAAK,GAAK,EACX,CACD,OAAAzF,EAAOyF,EAAQ,QAAQ,EACvBA,EAAO,UAAYzD,GACnBA,GAAQ,OAASyD,EACV,IAAIA,CACb,IACA3F,GAAO,OAASA,GAChB,IAAI4F,GAAkB5F,GAGtB,SAAS6F,GAAUC,EAAM,CACvB,OAAOA,EAAK,OAAS,KACvB,CACA5F,EAAO2F,GAAW,WAAW,EAC7B,SAASE,GAAeD,EAAM,CAC5B,OAAOA,EAAK,OAAS,MACvB,CACA5F,EAAO6F,GAAgB,gBAAgB,EACvC,SAASC,EAAiBF,EAAM,CAC9B,OAAOA,EAAK,OAAS,QACvB,CACA5F,EAAO8F,EAAkB,kBAAkB,EAG3C,IAAIC,GAAkC,KAAM,CAC1C,YAAYC,EAAa,CACvB,KAAK,YAAcA,CACpB,CACD,MAAA,CACEhG,EAAO,KAAM,iCAAiC,CAC/C,CACD,gBAAgBiG,EAAOC,EAAU,CAC/B,GAAI,CAAC,KAAK,YACR,MAAO,CACL,MAAOD,EAAM,OAAO,CAACE,EAAKC,IAAQ,KAAK,IAAIA,EAAI,OAAQD,CAAG,EAAG,CAAC,EAAID,EAClE,OAAQA,CAChB,EAEI,MAAMG,EAAY,CAChB,MAAO,EACP,OAAQ,CACd,EACUC,EAAO,KAAK,YAAY,OAAO,GAAG,EAAE,KAAK,aAAc,QAAQ,EAAE,KAAK,YAAaJ,CAAQ,EACjG,UAAWK,KAAKN,EAAO,CACrB,MAAMO,EAAOC,GAAuBH,EAAM,EAAGC,CAAC,EACxCG,EAAQF,EAAOA,EAAK,MAAQD,EAAE,OAASL,EACvCS,EAASH,EAAOA,EAAK,OAASN,EACpCG,EAAU,MAAQ,KAAK,IAAIA,EAAU,MAAOK,CAAK,EACjDL,EAAU,OAAS,KAAK,IAAIA,EAAU,OAAQM,CAAM,CACrD,CACD,OAAAL,EAAK,OAAM,EACJD,CACR,CACH,EAMIO,GAAgC,GAChCC,GAA0C,GAC1CC,GAAW,KAAM,CACnB,YAAYC,EAAYC,EAAOC,EAAyBC,EAAiB,CACvE,KAAK,WAAaH,EAClB,KAAK,MAAQC,EACb,KAAK,wBAA0BC,EAC/B,KAAK,gBAAkBC,EACvB,KAAK,aAAe,CAAE,EAAG,EAAG,EAAG,EAAG,MAAO,EAAG,OAAQ,CAAC,EACrD,KAAK,aAAe,OACpB,KAAK,UAAY,GACjB,KAAK,UAAY,GACjB,KAAK,SAAW,GAChB,KAAK,aAAe,GACpB,KAAK,aAAe,EACpB,KAAK,gBAAkB,EACvB,KAAK,gBAAkB,EACvB,KAAK,MAAQ,CAAC,EAAG,EAAE,EACnB,KAAK,aAAe,CAAE,EAAG,EAAG,EAAG,EAAG,MAAO,EAAG,OAAQ,CAAC,EACrD,KAAK,aAAe,MACrB,CACD,MAAA,CACElH,EAAO,KAAM,UAAU,CACxB,CACD,SAASmH,EAAO,CACd,KAAK,MAAQA,EACT,KAAK,eAAiB,QAAU,KAAK,eAAiB,QACxD,KAAK,aAAa,OAASA,EAAM,CAAC,EAAIA,EAAM,CAAC,EAE7C,KAAK,aAAa,MAAQA,EAAM,CAAC,EAAIA,EAAM,CAAC,EAE9C,KAAK,iBAAgB,CACtB,CACD,UAAW,CACT,MAAO,CAAC,KAAK,MAAM,CAAC,EAAI,KAAK,aAAc,KAAK,MAAM,CAAC,EAAI,KAAK,YAAY,CAC7E,CACD,gBAAgBC,EAAc,CAC5B,KAAK,aAAeA,EACpB,KAAK,SAAS,KAAK,KAAK,CACzB,CACD,iBAAkB,CAChB,MAAMD,EAAQ,KAAK,WACnB,OAAO,KAAK,IAAIA,EAAM,CAAC,EAAIA,EAAM,CAAC,CAAC,EAAI,KAAK,cAAa,EAAG,MAC7D,CACD,qBAAsB,CACpB,OAAO,KAAK,YACb,CACD,mBAAoB,CAClB,OAAO,KAAK,wBAAwB,gBAClC,KAAK,cAAa,EAAG,IAAKE,GAASA,EAAK,UAAU,EAClD,KAAK,WAAW,aACtB,CACG,CACD,kCAAmC,CAC7BT,GAAgC,KAAK,gBAAiB,EAAG,KAAK,aAAe,IAC/E,KAAK,aAAe,KAAK,MAAMA,GAAgC,KAAK,kBAAoB,CAAC,GAE3F,KAAK,iBAAgB,CACtB,CACD,kCAAkCU,EAAgB,CAChD,IAAIC,EAAkBD,EAAe,OAKrC,GAJI,KAAK,WAAW,cAAgBC,EAAkB,KAAK,WAAW,gBACpEA,GAAmB,KAAK,WAAW,cACnC,KAAK,aAAe,IAElB,KAAK,WAAW,UAAW,CAC7B,MAAMC,EAAgB,KAAK,oBACrBC,EAAaZ,GAA0CS,EAAe,MAC5E,KAAK,aAAe,KAAK,IAAIE,EAAc,MAAQ,EAAGC,CAAU,EAChE,MAAMC,EAAiBF,EAAc,OAAS,KAAK,WAAW,aAAe,EAC7E,KAAK,gBAAkBA,EAAc,OACjCE,GAAkBH,IACpBA,GAAmBG,EACnB,KAAK,UAAY,GAEpB,CAKD,GAJI,KAAK,WAAW,UAAYH,GAAmB,KAAK,WAAW,aACjE,KAAK,SAAW,GAChBA,GAAmB,KAAK,WAAW,YAEjC,KAAK,WAAW,WAAa,KAAK,MAAO,CAC3C,MAAMC,EAAgB,KAAK,wBAAwB,gBACjD,CAAC,KAAK,KAAK,EACX,KAAK,WAAW,aACxB,EACYE,EAAiBF,EAAc,OAAS,KAAK,WAAW,aAAe,EAC7E,KAAK,gBAAkBA,EAAc,OACjCE,GAAkBH,IACpBA,GAAmBG,EACnB,KAAK,UAAY,GAEpB,CACD,KAAK,aAAa,MAAQJ,EAAe,MACzC,KAAK,aAAa,OAASA,EAAe,OAASC,CACpD,CACD,8BAA8BD,EAAgB,CAC5C,IAAIK,EAAiBL,EAAe,MAKpC,GAJI,KAAK,WAAW,cAAgBK,EAAiB,KAAK,WAAW,gBACnEA,GAAkB,KAAK,WAAW,cAClC,KAAK,aAAe,IAElB,KAAK,WAAW,UAAW,CAC7B,MAAMH,EAAgB,KAAK,oBACrBC,EAAaZ,GAA0CS,EAAe,OAC5E,KAAK,aAAe,KAAK,IAAIE,EAAc,OAAS,EAAGC,CAAU,EACjE,MAAMG,EAAgBJ,EAAc,MAAQ,KAAK,WAAW,aAAe,EACvEI,GAAiBD,IACnBA,GAAkBC,EAClB,KAAK,UAAY,GAEpB,CAKD,GAJI,KAAK,WAAW,UAAYD,GAAkB,KAAK,WAAW,aAChE,KAAK,SAAW,GAChBA,GAAkB,KAAK,WAAW,YAEhC,KAAK,WAAW,WAAa,KAAK,MAAO,CAC3C,MAAMH,EAAgB,KAAK,wBAAwB,gBACjD,CAAC,KAAK,KAAK,EACX,KAAK,WAAW,aACxB,EACYI,EAAgBJ,EAAc,OAAS,KAAK,WAAW,aAAe,EAC5E,KAAK,gBAAkBA,EAAc,OACjCI,GAAiBD,IACnBA,GAAkBC,EAClB,KAAK,UAAY,GAEpB,CACD,KAAK,aAAa,MAAQN,EAAe,MAAQK,EACjD,KAAK,aAAa,OAASL,EAAe,MAC3C,CACD,eAAeA,EAAgB,CAC7B,OAAI,KAAK,eAAiB,QAAU,KAAK,eAAiB,QACxD,KAAK,8BAA8BA,CAAc,EAEjD,KAAK,kCAAkCA,CAAc,EAEvD,KAAK,iBAAgB,EACd,CACL,MAAO,KAAK,aAAa,MACzB,OAAQ,KAAK,aAAa,MAChC,CACG,CACD,iBAAiBO,EAAO,CACtB,KAAK,aAAa,EAAIA,EAAM,EAC5B,KAAK,aAAa,EAAIA,EAAM,CAC7B,CACD,gCAAiC,CAC/B,MAAMC,EAAkB,CAAA,EACxB,GAAI,KAAK,aAAc,CACrB,MAAMC,EAAI,KAAK,aAAa,EAAI,KAAK,aAAa,MAAQ,KAAK,WAAW,cAAgB,EAC1FD,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,YAAa,YAAY,EACtC,KAAM,CACJ,CACE,KAAM,KAAKC,CAAC,IAAI,KAAK,aAAa,CAAC,MAAMA,CAAC,IAAI,KAAK,aAAa,EAAI,KAAK,aAAa,MAAM,IAC5F,WAAY,KAAK,gBAAgB,cACjC,YAAa,KAAK,WAAW,aAC9B,CACF,CACT,CAAO,CACF,CAiBD,GAhBI,KAAK,WACPD,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,YAAa,OAAO,EACjC,KAAM,KAAK,cAAe,EAAC,IAAKT,IAAU,CACxC,KAAMA,EAAK,SAAU,EACrB,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,OAAS,KAAK,UAAY,KAAK,WAAW,aAAe,IAAM,KAAK,SAAW,KAAK,WAAW,WAAa,IAAM,KAAK,aAAe,KAAK,WAAW,cAAgB,GACjN,EAAG,KAAK,cAAcA,CAAI,EAC1B,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,SACb,cAAe,OACzB,EAAU,CACV,CAAO,EAEC,KAAK,SAAU,CACjB,MAAMU,EAAI,KAAK,aAAa,EAAI,KAAK,aAAa,OAAS,KAAK,aAAe,KAAK,WAAW,cAAgB,GAC/GD,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,YAAa,OAAO,EACjC,KAAM,KAAK,cAAe,EAAC,IAAKT,IAAU,CACxC,KAAM,KAAKU,CAAC,IAAI,KAAK,cAAcV,CAAI,CAAC,MAAMU,EAAI,KAAK,WAAW,UAAU,IAAI,KAAK,cAAcV,CAAI,CAAC,GACxG,WAAY,KAAK,gBAAgB,UACjC,YAAa,KAAK,WAAW,SACvC,EAAU,CACV,CAAO,CACF,CACD,OAAI,KAAK,WACPS,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,YAAa,OAAO,EACjC,KAAM,CACJ,CACE,KAAM,KAAK,MACX,EAAG,KAAK,aAAa,EAAI,KAAK,WAAW,aACzC,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,OAAS,EACpD,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,IACV,YAAa,MACb,cAAe,QAChB,CACF,CACT,CAAO,EAEIA,CACR,CACD,kCAAmC,CACjC,MAAMA,EAAkB,CAAA,EACxB,GAAI,KAAK,aAAc,CACrB,MAAME,EAAI,KAAK,aAAa,EAAI,KAAK,WAAW,cAAgB,EAChEF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,cAAe,WAAW,EACvC,KAAM,CACJ,CACE,KAAM,KAAK,KAAK,aAAa,CAAC,IAAIE,CAAC,MAAM,KAAK,aAAa,EAAI,KAAK,aAAa,KAAK,IAAIA,CAAC,GAC3F,WAAY,KAAK,gBAAgB,cACjC,YAAa,KAAK,WAAW,aAC9B,CACF,CACT,CAAO,CACF,CAiBD,GAhBI,KAAK,WACPF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,cAAe,OAAO,EACnC,KAAM,KAAK,cAAe,EAAC,IAAKT,IAAU,CACxC,KAAMA,EAAK,SAAU,EACrB,EAAG,KAAK,cAAcA,CAAI,EAC1B,EAAG,KAAK,aAAa,EAAI,KAAK,WAAW,cAAgB,KAAK,SAAW,KAAK,WAAW,WAAa,IAAM,KAAK,aAAe,KAAK,WAAW,cAAgB,GAChK,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,MACb,cAAe,QACzB,EAAU,CACV,CAAO,EAEC,KAAK,SAAU,CACjB,MAAMW,EAAI,KAAK,aAAa,GAAK,KAAK,aAAe,KAAK,WAAW,cAAgB,GACrFF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,cAAe,OAAO,EACnC,KAAM,KAAK,cAAe,EAAC,IAAKT,IAAU,CACxC,KAAM,KAAK,KAAK,cAAcA,CAAI,CAAC,IAAIW,CAAC,MAAM,KAAK,cAAcX,CAAI,CAAC,IAAIW,EAAI,KAAK,WAAW,UAAU,GACxG,WAAY,KAAK,gBAAgB,UACjC,YAAa,KAAK,WAAW,SACvC,EAAU,CACV,CAAO,CACF,CACD,OAAI,KAAK,WACPF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,cAAe,OAAO,EACnC,KAAM,CACJ,CACE,KAAM,KAAK,MACX,EAAG,KAAK,MAAM,CAAC,GAAK,KAAK,MAAM,CAAC,EAAI,KAAK,MAAM,CAAC,GAAK,EACrD,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,OAAS,KAAK,WAAW,aAAe,KAAK,gBACxF,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,MACb,cAAe,QAChB,CACF,CACT,CAAO,EAEIA,CACR,CACD,+BAAgC,CAC9B,MAAMA,EAAkB,CAAA,EACxB,GAAI,KAAK,aAAc,CACrB,MAAME,EAAI,KAAK,aAAa,EAAI,KAAK,aAAa,OAAS,KAAK,WAAW,cAAgB,EAC3FF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,WAAY,WAAW,EACpC,KAAM,CACJ,CACE,KAAM,KAAK,KAAK,aAAa,CAAC,IAAIE,CAAC,MAAM,KAAK,aAAa,EAAI,KAAK,aAAa,KAAK,IAAIA,CAAC,GAC3F,WAAY,KAAK,gBAAgB,cACjC,YAAa,KAAK,WAAW,aAC9B,CACF,CACT,CAAO,CACF,CAiBD,GAhBI,KAAK,WACPF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,WAAY,OAAO,EAChC,KAAM,KAAK,cAAe,EAAC,IAAKT,IAAU,CACxC,KAAMA,EAAK,SAAU,EACrB,EAAG,KAAK,cAAcA,CAAI,EAC1B,EAAG,KAAK,aAAa,GAAK,KAAK,UAAY,KAAK,gBAAkB,KAAK,WAAW,aAAe,EAAI,GAAK,KAAK,WAAW,aAC1H,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,MACb,cAAe,QACzB,EAAU,CACV,CAAO,EAEC,KAAK,SAAU,CACjB,MAAMW,EAAI,KAAK,aAAa,EAC5BF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,WAAY,OAAO,EAChC,KAAM,KAAK,cAAe,EAAC,IAAKT,IAAU,CACxC,KAAM,KAAK,KAAK,cAAcA,CAAI,CAAC,IAAIW,EAAI,KAAK,aAAa,QAAU,KAAK,aAAe,KAAK,WAAW,cAAgB,EAAE,MAAM,KAAK,cAAcX,CAAI,CAAC,IAAIW,EAAI,KAAK,aAAa,OAAS,KAAK,WAAW,YAAc,KAAK,aAAe,KAAK,WAAW,cAAgB,EAAE,GAClR,WAAY,KAAK,gBAAgB,UACjC,YAAa,KAAK,WAAW,SACvC,EAAU,CACV,CAAO,CACF,CACD,OAAI,KAAK,WACPF,EAAgB,KAAK,CACnB,KAAM,OACN,WAAY,CAAC,WAAY,OAAO,EAChC,KAAM,CACJ,CACE,KAAM,KAAK,MACX,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,MAAQ,EACnD,EAAG,KAAK,aAAa,EAAI,KAAK,WAAW,aACzC,KAAM,KAAK,gBAAgB,WAC3B,SAAU,KAAK,WAAW,cAC1B,SAAU,EACV,YAAa,MACb,cAAe,QAChB,CACF,CACT,CAAO,EAEIA,CACR,CACD,qBAAsB,CACpB,GAAI,KAAK,eAAiB,OACxB,OAAO,KAAK,iCAEd,GAAI,KAAK,eAAiB,QACxB,MAAM,MAAM,0CAA0C,EAExD,OAAI,KAAK,eAAiB,SACjB,KAAK,mCAEV,KAAK,eAAiB,MACjB,KAAK,gCAEP,EACR,CACH,EAGIG,GAAW,cAAcnB,EAAS,CACpC,MAAA,CACE9G,EAAO,KAAM,UAAU,CACxB,CACD,YAAY+G,EAAYG,EAAiBgB,EAAYlB,EAAOC,EAAyB,CACnF,MAAMF,EAAYC,EAAOC,EAAyBC,CAAe,EACjE,KAAK,WAAagB,EAClB,KAAK,MAAQC,GAAW,EAAC,OAAO,KAAK,UAAU,EAAE,MAAM,KAAK,SAAU,CAAA,CACvE,CACD,SAAShB,EAAO,CACd,MAAM,SAASA,CAAK,CACrB,CACD,kBAAmB,CACjB,KAAK,MAAQgB,KAAY,OAAO,KAAK,UAAU,EAAE,MAAM,KAAK,SAAQ,CAAE,EAAE,aAAa,CAAC,EAAE,aAAa,CAAC,EAAE,MAAM,EAAG,EACjHC,GAAI,MAAM,0CAA2C,KAAK,WAAY,KAAK,SAAQ,CAAE,CACtF,CACD,eAAgB,CACd,OAAO,KAAK,UACb,CACD,cAAcC,EAAO,CACnB,OAAO,KAAK,MAAMA,CAAK,GAAK,KAAK,SAAQ,EAAG,CAAC,CAC9C,CACH,EAIIC,GAAa,cAAcxB,EAAS,CACtC,MAAA,CACE9G,EAAO,KAAM,YAAY,CAC1B,CACD,YAAY+G,EAAYG,EAAiBxI,EAAQsI,EAAOC,EAAyB,CAC/E,MAAMF,EAAYC,EAAOC,EAAyBC,CAAe,EACjE,KAAK,OAASxI,EACd,KAAK,MAAQ6J,GAAa,EAAC,OAAO,KAAK,MAAM,EAAE,MAAM,KAAK,SAAU,CAAA,CACrE,CACD,eAAgB,CACd,OAAO,KAAK,MAAM,OACnB,CACD,kBAAmB,CACjB,MAAM7J,EAAS,CAAC,GAAG,KAAK,MAAM,EAC1B,KAAK,eAAiB,QACxBA,EAAO,QAAO,EAEhB,KAAK,MAAQ6J,GAAW,EAAG,OAAO7J,CAAM,EAAE,MAAM,KAAK,SAAQ,CAAE,CAChE,CACD,cAAc2J,EAAO,CACnB,OAAO,KAAK,MAAMA,CAAK,CACxB,CACH,EAGA,SAASG,GAAQ5C,EAAMmB,EAAYG,EAAiBuB,EAAc,CAChE,MAAMxB,EAA0B,IAAIlB,GAAgC0C,CAAY,EAChF,OAAI5C,GAAeD,CAAI,EACd,IAAIqC,GACTlB,EACAG,EACAtB,EAAK,WACLA,EAAK,MACLqB,CACN,EAES,IAAIqB,GACTvB,EACAG,EACA,CAACtB,EAAK,IAAKA,EAAK,GAAG,EACnBA,EAAK,MACLqB,CACJ,CACA,CACAjH,EAAOwI,GAAS,SAAS,EAGzB,IAAIE,GAAa,KAAM,CACrB,YAAYzB,EAAyB0B,EAAaC,EAAWC,EAAkB,CAC7E,KAAK,wBAA0B5B,EAC/B,KAAK,YAAc0B,EACnB,KAAK,UAAYC,EACjB,KAAK,iBAAmBC,EACxB,KAAK,aAAe,CAClB,EAAG,EACH,EAAG,EACH,MAAO,EACP,OAAQ,CACd,EACI,KAAK,eAAiB,EACvB,CACD,MAAA,CACE7I,EAAO,KAAM,YAAY,CAC1B,CACD,iBAAiB6H,EAAO,CACtB,KAAK,aAAa,EAAIA,EAAM,EAC5B,KAAK,aAAa,EAAIA,EAAM,CAC7B,CACD,eAAeP,EAAgB,CAC7B,MAAMwB,EAAiB,KAAK,wBAAwB,gBAClD,CAAC,KAAK,UAAU,KAAK,EACrB,KAAK,YAAY,aACvB,EACUlB,EAAgB,KAAK,IAAIkB,EAAe,MAAOxB,EAAe,KAAK,EACnEI,EAAiBoB,EAAe,OAAS,EAAI,KAAK,YAAY,aACpE,OAAIA,EAAe,OAASlB,GAAiBkB,EAAe,QAAUpB,GAAkB,KAAK,YAAY,WAAa,KAAK,UAAU,QACnI,KAAK,aAAa,MAAQE,EAC1B,KAAK,aAAa,OAASF,EAC3B,KAAK,eAAiB,IAEjB,CACL,MAAO,KAAK,aAAa,MACzB,OAAQ,KAAK,aAAa,MAChC,CACG,CACD,qBAAsB,CACpB,MAAMqB,EAAe,CAAA,EACrB,OAAI,KAAK,gBACPA,EAAa,KAAK,CAChB,WAAY,CAAC,aAAa,EAC1B,KAAM,OACN,KAAM,CACJ,CACE,SAAU,KAAK,YAAY,cAC3B,KAAM,KAAK,UAAU,MACrB,YAAa,SACb,cAAe,SACf,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,MAAQ,EACnD,EAAG,KAAK,aAAa,EAAI,KAAK,aAAa,OAAS,EACpD,KAAM,KAAK,iBAAiB,WAC5B,SAAU,CACX,CACF,CACT,CAAO,EAEIA,CACR,CACH,EACA,SAASC,GAAuBL,EAAaC,EAAWC,EAAkBJ,EAAc,CACtF,MAAMxB,EAA0B,IAAIlB,GAAgC0C,CAAY,EAChF,OAAO,IAAIC,GAAWzB,EAAyB0B,EAAaC,EAAWC,CAAgB,CACzF,CACA7I,EAAOgJ,GAAwB,wBAAwB,EAIvD,IAAIC,GAAW,KAAM,CACnB,YAAYC,EAAUC,EAAOC,EAAOC,EAAaC,EAAY,CAC3D,KAAK,SAAWJ,EAChB,KAAK,MAAQC,EACb,KAAK,MAAQC,EACb,KAAK,YAAcC,EACnB,KAAK,UAAYC,CAClB,CACD,MAAA,CACEtJ,EAAO,KAAM,UAAU,CACxB,CACD,oBAAqB,CACnB,MAAMuJ,EAAY,KAAK,SAAS,KAAK,IAAKC,GAAM,CAC9C,KAAK,MAAM,cAAcA,EAAE,CAAC,CAAC,EAC7B,KAAK,MAAM,cAAcA,EAAE,CAAC,CAAC,CACnC,CAAK,EACD,IAAIC,EAMJ,OALI,KAAK,cAAgB,aACvBA,EAAOC,GAAI,EAAG,EAAGF,GAAMA,EAAE,CAAC,CAAC,EAAE,EAAGA,GAAMA,EAAE,CAAC,CAAC,EAAED,CAAS,EAErDE,EAAOC,GAAI,EAAG,EAAGF,GAAMA,EAAE,CAAC,CAAC,EAAE,EAAGA,GAAMA,EAAE,CAAC,CAAC,EAAED,CAAS,EAElDE,EAGE,CACL,CACE,WAAY,CAAC,OAAQ,aAAa,KAAK,SAAS,EAAE,EAClD,KAAM,OACN,KAAM,CACJ,CACE,KAAAA,EACA,WAAY,KAAK,SAAS,WAC1B,YAAa,KAAK,SAAS,WAC5B,CACF,CACF,CACP,EAda,EAeV,CACH,EAGIE,GAAU,KAAM,CAClB,YAAYC,EAASC,EAAcV,EAAOC,EAAOC,EAAaC,EAAY,CACxE,KAAK,QAAUM,EACf,KAAK,aAAeC,EACpB,KAAK,MAAQV,EACb,KAAK,MAAQC,EACb,KAAK,YAAcC,EACnB,KAAK,UAAYC,CAClB,CACD,MAAA,CACEtJ,EAAO,KAAM,SAAS,CACvB,CACD,oBAAqB,CACnB,MAAMuJ,EAAY,KAAK,QAAQ,KAAK,IAAKC,GAAM,CAC7C,KAAK,MAAM,cAAcA,EAAE,CAAC,CAAC,EAC7B,KAAK,MAAM,cAAcA,EAAE,CAAC,CAAC,CACnC,CAAK,EAEKM,EAAW,KAAK,IAAI,KAAK,MAAM,oBAAqB,EAAG,EAAG,KAAK,MAAM,gBAAe,CAAE,GAAK,EADvE,KAEpBC,EAAeD,EAAW,EAChC,OAAI,KAAK,cAAgB,aAChB,CACL,CACE,WAAY,CAAC,OAAQ,YAAY,KAAK,SAAS,EAAE,EACjD,KAAM,OACN,KAAMP,EAAU,IAAK3D,IAAU,CAC7B,EAAG,KAAK,aAAa,EACrB,EAAGA,EAAK,CAAC,EAAImE,EACb,OAAQD,EACR,MAAOlE,EAAK,CAAC,EAAI,KAAK,aAAa,EACnC,KAAM,KAAK,QAAQ,KACnB,YAAa,EACb,WAAY,KAAK,QAAQ,IACrC,EAAY,CACH,CACT,EAEW,CACL,CACE,WAAY,CAAC,OAAQ,YAAY,KAAK,SAAS,EAAE,EACjD,KAAM,OACN,KAAM2D,EAAU,IAAK3D,IAAU,CAC7B,EAAGA,EAAK,CAAC,EAAImE,EACb,EAAGnE,EAAK,CAAC,EACT,MAAOkE,EACP,OAAQ,KAAK,aAAa,EAAI,KAAK,aAAa,OAASlE,EAAK,CAAC,EAC/D,KAAM,KAAK,QAAQ,KACnB,YAAa,EACb,WAAY,KAAK,QAAQ,IACnC,EAAU,CACH,CACP,CACG,CACH,EAGIoE,GAAW,KAAM,CACnB,YAAYrB,EAAaC,EAAWC,EAAkB,CACpD,KAAK,YAAcF,EACnB,KAAK,UAAYC,EACjB,KAAK,iBAAmBC,EACxB,KAAK,aAAe,CAClB,EAAG,EACH,EAAG,EACH,MAAO,EACP,OAAQ,CACd,CACG,CACD,MAAA,CACE7I,EAAO,KAAM,UAAU,CACxB,CACD,QAAQmJ,EAAOC,EAAO,CACpB,KAAK,MAAQD,EACb,KAAK,MAAQC,CACd,CACD,iBAAiBvB,EAAO,CACtB,KAAK,aAAa,EAAIA,EAAM,EAC5B,KAAK,aAAa,EAAIA,EAAM,CAC7B,CACD,eAAeP,EAAgB,CAC7B,YAAK,aAAa,MAAQA,EAAe,MACzC,KAAK,aAAa,OAASA,EAAe,OACnC,CACL,MAAO,KAAK,aAAa,MACzB,OAAQ,KAAK,aAAa,MAChC,CACG,CACD,qBAAsB,CACpB,GAAI,EAAE,KAAK,OAAS,KAAK,OACvB,MAAM,MAAM,qCAAqC,EAEnD,MAAMyB,EAAe,CAAA,EACrB,SAAW,CAAC,EAAGkB,CAAI,IAAK,KAAK,UAAU,MAAM,UAC3C,OAAQA,EAAK,KAAI,CACf,IAAK,OACH,CACE,MAAMC,EAAW,IAAIjB,GACnBgB,EACA,KAAK,MACL,KAAK,MACL,KAAK,YAAY,iBACjB,CACd,EACYlB,EAAa,KAAK,GAAGmB,EAAS,mBAAoB,CAAA,CACnD,CACD,MACF,IAAK,MACH,CACE,MAAMC,EAAU,IAAIR,GAClBM,EACA,KAAK,aACL,KAAK,MACL,KAAK,MACL,KAAK,YAAY,iBACjB,CACd,EACYlB,EAAa,KAAK,GAAGoB,EAAQ,mBAAoB,CAAA,CAClD,CACD,KACH,CAEH,OAAOpB,CACR,CACH,EACA,SAASqB,GAAiBzB,EAAaC,EAAWC,EAAkB,CAClE,OAAO,IAAImB,GAASrB,EAAaC,EAAWC,CAAgB,CAC9D,CACA7I,EAAOoK,GAAkB,kBAAkB,EAG3C,IAAIC,GAAe,KAAM,CACvB,YAAY1B,EAAaC,EAAWC,EAAkBJ,EAAc,CAClE,KAAK,YAAcE,EACnB,KAAK,UAAYC,EACjB,KAAK,eAAiB,CACpB,MAAOI,GAAuBL,EAAaC,EAAWC,EAAkBJ,CAAY,EACpF,KAAM2B,GAAiBzB,EAAaC,EAAWC,CAAgB,EAC/D,MAAOL,GACLI,EAAU,MACVD,EAAY,MACZ,CACE,WAAYE,EAAiB,gBAC7B,WAAYA,EAAiB,gBAC7B,UAAWA,EAAiB,eAC5B,cAAeA,EAAiB,cACjC,EACDJ,CACD,EACD,MAAOD,GACLI,EAAU,MACVD,EAAY,MACZ,CACE,WAAYE,EAAiB,gBAC7B,WAAYA,EAAiB,gBAC7B,UAAWA,EAAiB,eAC5B,cAAeA,EAAiB,cACjC,EACDJ,CACD,CACP,CACG,CACD,MAAA,CACEzI,EAAO,KAAM,cAAc,CAC5B,CACD,wBAAyB,CACvB,IAAI2H,EAAiB,KAAK,YAAY,MAClCJ,EAAkB,KAAK,YAAY,OACnC+C,EAAQ,EACRC,EAAQ,EACRC,EAAa,KAAK,MAAM7C,EAAiB,KAAK,YAAY,yBAA2B,GAAG,EACxF8C,EAAc,KAAK,MACrBlD,EAAkB,KAAK,YAAY,yBAA2B,GACpE,EACQmD,EAAY,KAAK,eAAe,KAAK,eAAe,CACtD,MAAOF,EACP,OAAQC,CACd,CAAK,EACD9C,GAAkB+C,EAAU,MAC5BnD,GAAmBmD,EAAU,OAC7BA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAO,KAAK,YAAY,MACxB,OAAQnD,CACd,CAAK,EACDgD,EAAQG,EAAU,OAClBnD,GAAmBmD,EAAU,OAC7B,KAAK,eAAe,MAAM,gBAAgB,QAAQ,EAClDA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAO/C,EACP,OAAQJ,CACd,CAAK,EACDA,GAAmBmD,EAAU,OAC7B,KAAK,eAAe,MAAM,gBAAgB,MAAM,EAChDA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAO/C,EACP,OAAQJ,CACd,CAAK,EACD+C,EAAQI,EAAU,MAClB/C,GAAkB+C,EAAU,MACxB/C,EAAiB,IACnB6C,GAAc7C,EACdA,EAAiB,GAEfJ,EAAkB,IACpBkD,GAAelD,EACfA,EAAkB,GAEpB,KAAK,eAAe,KAAK,eAAe,CACtC,MAAOiD,EACP,OAAQC,CACd,CAAK,EACD,KAAK,eAAe,KAAK,iBAAiB,CAAE,EAAGH,EAAO,EAAGC,CAAK,CAAE,EAChE,KAAK,eAAe,MAAM,SAAS,CAACD,EAAOA,EAAQE,CAAU,CAAC,EAC9D,KAAK,eAAe,MAAM,iBAAiB,CAAE,EAAGF,EAAO,EAAGC,EAAQE,CAAW,CAAE,EAC/E,KAAK,eAAe,MAAM,SAAS,CAACF,EAAOA,EAAQE,CAAW,CAAC,EAC/D,KAAK,eAAe,MAAM,iBAAiB,CAAE,EAAG,EAAG,EAAGF,CAAK,CAAE,EACzD,KAAK,UAAU,MAAM,KAAM,GAAM5E,GAAU,CAAC,CAAC,GAC/C,KAAK,eAAe,MAAM,kCAE7B,CACD,0BAA2B,CACzB,IAAIgC,EAAiB,KAAK,YAAY,MAClCJ,EAAkB,KAAK,YAAY,OACnCoD,EAAY,EACZL,EAAQ,EACRC,EAAQ,EACRC,EAAa,KAAK,MAAM7C,EAAiB,KAAK,YAAY,yBAA2B,GAAG,EACxF8C,EAAc,KAAK,MACrBlD,EAAkB,KAAK,YAAY,yBAA2B,GACpE,EACQmD,EAAY,KAAK,eAAe,KAAK,eAAe,CACtD,MAAOF,EACP,OAAQC,CACd,CAAK,EACD9C,GAAkB+C,EAAU,MAC5BnD,GAAmBmD,EAAU,OAC7BA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAO,KAAK,YAAY,MACxB,OAAQnD,CACd,CAAK,EACDoD,EAAYD,EAAU,OACtBnD,GAAmBmD,EAAU,OAC7B,KAAK,eAAe,MAAM,gBAAgB,MAAM,EAChDA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAO/C,EACP,OAAQJ,CACd,CAAK,EACDI,GAAkB+C,EAAU,MAC5BJ,EAAQI,EAAU,MAClB,KAAK,eAAe,MAAM,gBAAgB,KAAK,EAC/CA,EAAY,KAAK,eAAe,MAAM,eAAe,CACnD,MAAO/C,EACP,OAAQJ,CACd,CAAK,EACDA,GAAmBmD,EAAU,OAC7BH,EAAQI,EAAYD,EAAU,OAC1B/C,EAAiB,IACnB6C,GAAc7C,EACdA,EAAiB,GAEfJ,EAAkB,IACpBkD,GAAelD,EACfA,EAAkB,GAEpB,KAAK,eAAe,KAAK,eAAe,CACtC,MAAOiD,EACP,OAAQC,CACd,CAAK,EACD,KAAK,eAAe,KAAK,iBAAiB,CAAE,EAAGH,EAAO,EAAGC,CAAK,CAAE,EAChE,KAAK,eAAe,MAAM,SAAS,CAACD,EAAOA,EAAQE,CAAU,CAAC,EAC9D,KAAK,eAAe,MAAM,iBAAiB,CAAE,EAAGF,EAAO,EAAGK,CAAS,CAAE,EACrE,KAAK,eAAe,MAAM,SAAS,CAACJ,EAAOA,EAAQE,CAAW,CAAC,EAC/D,KAAK,eAAe,MAAM,iBAAiB,CAAE,EAAG,EAAG,EAAGF,CAAK,CAAE,EACzD,KAAK,UAAU,MAAM,KAAMrG,GAAMyB,GAAUzB,CAAC,CAAC,GAC/C,KAAK,eAAe,MAAM,kCAE7B,CACD,gBAAiB,CACX,KAAK,YAAY,mBAAqB,aACxC,KAAK,yBAAwB,EAE7B,KAAK,uBAAsB,CAE9B,CACD,oBAAqB,CACnB,KAAK,eAAc,EACnB,MAAM6E,EAAe,CAAA,EACrB,KAAK,eAAe,KAAK,QAAQ,KAAK,eAAe,MAAO,KAAK,eAAe,KAAK,EACrF,UAAW6B,KAAa,OAAO,OAAO,KAAK,cAAc,EACvD7B,EAAa,KAAK,GAAG6B,EAAU,oBAAqB,CAAA,EAEtD,OAAO7B,CACR,CACH,EAGI8B,GAAiB,KAAM,CACzB,MAAA,CACE7K,EAAO,KAAM,gBAAgB,CAC9B,CACD,OAAO,MAAM8K,EAAQlC,EAAWC,EAAkBJ,EAAc,CAE9D,OADqB,IAAI4B,GAAaS,EAAQlC,EAAWC,EAAkBJ,CAAY,EACnE,oBACrB,CACH,EAGIsC,EAAY,EACZC,GACAC,EAAgBC,GAAqB,EACrCC,EAAqBC,GAA0B,EAC/CC,EAAcC,GAAmB,EACjCC,GAAmBJ,EAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAKK,GAAUA,EAAM,KAAM,CAAA,EAC7FC,EAAc,GACdC,GAAc,GAClB,SAASN,IAA6B,CACpC,MAAMO,EAAwBC,KACxBd,EAASe,KACf,OAAOC,GAAcH,EAAsB,QAASb,EAAO,eAAe,OAAO,CACnF,CACA9K,EAAOoL,GAA4B,4BAA4B,EAC/D,SAASF,IAAwB,CAC/B,MAAMJ,EAASe,KACf,OAAOC,GACLC,GAAsB,QACtBjB,EAAO,OACX,CACA,CACA9K,EAAOkL,GAAuB,uBAAuB,EACrD,SAASI,IAAsB,CAC7B,MAAO,CACL,MAAO,CACL,KAAM,SACN,MAAO,GACP,IAAK,IACL,IAAK,IACN,EACD,MAAO,CACL,KAAM,OACN,MAAO,GACP,WAAY,CAAE,CACf,EACD,MAAO,GACP,MAAO,CAAE,CACb,CACA,CACAtL,EAAOsL,GAAqB,qBAAqB,EACjD,SAASU,EAAcC,EAAM,CAC3B,MAAMnB,EAASe,KACf,OAAOK,GAAaD,EAAK,KAAM,EAAEnB,CAAM,CACzC,CACA9K,EAAOgM,EAAe,eAAe,EACrC,SAASG,GAAWC,EAAM,CACxBpB,GAAcoB,CAChB,CACApM,EAAOmM,GAAY,YAAY,EAC/B,SAASE,GAAehD,EAAa,CAC/BA,IAAgB,aAClB4B,EAAc,iBAAmB,aAEjCA,EAAc,iBAAmB,UAErC,CACAjL,EAAOqM,GAAgB,gBAAgB,EACvC,SAASC,GAActF,EAAO,CAC5BqE,EAAY,MAAM,MAAQW,EAAchF,EAAM,IAAI,CACpD,CACAhH,EAAOsM,GAAe,eAAe,EACrC,SAASC,GAAkBC,EAAKC,EAAK,CACnCpB,EAAY,MAAQ,CAAE,KAAM,SAAU,MAAOA,EAAY,MAAM,MAAO,IAAAmB,EAAK,IAAAC,CAAG,EAC9EhB,EAAc,EAChB,CACAzL,EAAOuM,GAAmB,mBAAmB,EAC7C,SAASG,GAAaxE,EAAY,CAChCmD,EAAY,MAAQ,CAClB,KAAM,OACN,MAAOA,EAAY,MAAM,MACzB,WAAYnD,EAAW,IAAKpD,GAAMkH,EAAclH,EAAE,IAAI,CAAC,CAC3D,EACE2G,EAAc,EAChB,CACAzL,EAAO0M,GAAc,cAAc,EACnC,SAASC,GAAc3F,EAAO,CAC5BqE,EAAY,MAAM,MAAQW,EAAchF,EAAM,IAAI,CACpD,CACAhH,EAAO2M,GAAe,eAAe,EACrC,SAASC,GAAkBJ,EAAKC,EAAK,CACnCpB,EAAY,MAAQ,CAAE,KAAM,SAAU,MAAOA,EAAY,MAAM,MAAO,IAAAmB,EAAK,IAAAC,CAAG,EAC9Ef,GAAc,EAChB,CACA1L,EAAO4M,GAAmB,mBAAmB,EAC7C,SAASC,GAA0BjH,EAAM,CACvC,MAAMkH,EAAW,KAAK,IAAI,GAAGlH,CAAI,EAC3BmH,EAAW,KAAK,IAAI,GAAGnH,CAAI,EAC3BoH,EAAelH,EAAiBuF,EAAY,KAAK,EAAIA,EAAY,MAAM,IAAM,IAC7E4B,EAAenH,EAAiBuF,EAAY,KAAK,EAAIA,EAAY,MAAM,IAAM,KACnFA,EAAY,MAAQ,CAClB,KAAM,SACN,MAAOA,EAAY,MAAM,MACzB,IAAK,KAAK,IAAI2B,EAAcF,CAAQ,EACpC,IAAK,KAAK,IAAIG,EAAcF,CAAQ,CACxC,CACA,CACA/M,EAAO6M,GAA2B,2BAA2B,EAC7D,SAASK,GAA6BtH,EAAM,CAC1C,IAAIuH,EAAU,CAAA,EACd,GAAIvH,EAAK,SAAW,EAClB,OAAOuH,EAET,GAAI,CAAC1B,EAAa,CAChB,MAAMuB,EAAelH,EAAiBuF,EAAY,KAAK,EAAIA,EAAY,MAAM,IAAM,IAC7E4B,EAAenH,EAAiBuF,EAAY,KAAK,EAAIA,EAAY,MAAM,IAAM,KACnFkB,GAAkB,KAAK,IAAIS,EAAc,CAAC,EAAG,KAAK,IAAIC,EAAcrH,EAAK,MAAM,CAAC,CACjF,CAOD,GANK8F,IACHmB,GAA0BjH,CAAI,EAE5BC,GAAewF,EAAY,KAAK,IAClC8B,EAAU9B,EAAY,MAAM,WAAW,IAAI,CAACvG,EAAGnF,IAAM,CAACmF,EAAGc,EAAKjG,CAAC,CAAC,CAAC,GAE/DmG,EAAiBuF,EAAY,KAAK,EAAG,CACvC,MAAMmB,EAAMnB,EAAY,MAAM,IACxBoB,EAAMpB,EAAY,MAAM,IACxBvM,GAAQ2N,EAAMD,IAAQ5G,EAAK,OAAS,GACpCsC,EAAa,CAAA,EACnB,QAASvI,EAAI6M,EAAK7M,GAAK8M,EAAK9M,GAAKb,EAC/BoJ,EAAW,KAAK,GAAGvI,CAAC,EAAE,EAExBwN,EAAUjF,EAAW,IAAI,CAACpD,EAAGnF,IAAM,CAACmF,EAAGc,EAAKjG,CAAC,CAAC,CAAC,CAChD,CACD,OAAOwN,CACT,CACAnN,EAAOkN,GAA8B,8BAA8B,EACnE,SAASE,GAAwB9D,EAAY,CAC3C,OAAOiC,GAAiBjC,IAAe,EAAI,EAAIA,EAAaiC,GAAiB,MAAM,CACrF,CACAvL,EAAOoN,GAAyB,yBAAyB,EACzD,SAASC,GAAYrG,EAAOpB,EAAM,CAChC,MAAMsD,EAAWgE,GAA6BtH,CAAI,EAClDyF,EAAY,MAAM,KAAK,CACrB,KAAM,OACN,WAAY+B,GAAwBrC,CAAS,EAC7C,YAAa,EACb,KAAM7B,CACV,CAAG,EACD6B,GACF,CACA/K,EAAOqN,GAAa,aAAa,EACjC,SAASC,GAAWtG,EAAOpB,EAAM,CAC/B,MAAMsD,EAAWgE,GAA6BtH,CAAI,EAClDyF,EAAY,MAAM,KAAK,CACrB,KAAM,MACN,KAAM+B,GAAwBrC,CAAS,EACvC,KAAM7B,CACV,CAAG,EACD6B,GACF,CACA/K,EAAOsN,GAAY,YAAY,EAC/B,SAASC,IAAkB,CACzB,GAAIlC,EAAY,MAAM,SAAW,EAC/B,MAAM,MAAM,yDAAyD,EAEvE,OAAAA,EAAY,MAAQmC,KACb3C,GAAe,MAAMI,EAAeI,EAAaF,EAAoBH,EAAW,CACzF,CACAhL,EAAOuN,GAAiB,iBAAiB,EACzC,SAASE,IAAsB,CAC7B,OAAOtC,CACT,CACAnL,EAAOyN,GAAqB,qBAAqB,EACjD,SAASC,IAAiB,CACxB,OAAOzC,CACT,CACAjL,EAAO0N,GAAgB,gBAAgB,EACvC,IAAIC,GAAyB3N,EAAO,UAAW,CAC7C4N,KACA7C,EAAY,EACZE,EAAgBC,GAAqB,EACrCG,EAAcC,GAAmB,EACjCH,EAAqBC,GAA0B,EAC/CG,GAAmBJ,EAAmB,iBAAiB,MAAM,GAAG,EAAE,IAAKK,GAAUA,EAAM,KAAM,CAAA,EAC7FC,EAAc,GACdC,GAAc,EAChB,EAAG,OAAO,EACNmC,GAAoB,CACtB,gBAAAN,GACA,MAAOI,GACP,YAAAG,GACA,YAAAC,GACA,gBAAAC,GACA,gBAAAR,GACA,kBAAAS,GACA,kBAAAC,GACA,eAAA7B,GACA,cAAAC,GACA,kBAAAC,GACA,aAAAG,GACA,cAAAC,GACA,kBAAAC,GACA,YAAAS,GACA,WAAAC,GACA,WAAAnB,GACA,oBAAAsB,GACA,eAAAC,EACF,EAGIS,GAAuBnO,EAAO,CAACoO,EAAKC,EAAIC,EAAUC,IAAY,CAChE,MAAMC,EAAKD,EAAQ,GACbE,EAAcD,EAAG,sBACjB7F,EAAc6F,EAAG,iBACvB,SAASE,EAAoBC,EAAe,CAC1C,OAAOA,IAAkB,MAAQ,mBAAqB,QACvD,CACD3O,EAAO0O,EAAqB,qBAAqB,EACjD,SAASE,EAAcC,EAAa,CAClC,OAAOA,IAAgB,OAAS,QAAUA,IAAgB,QAAU,MAAQ,QAC7E,CACD7O,EAAO4O,EAAe,eAAe,EACrC,SAASE,EAAsBlJ,EAAM,CACnC,MAAO,aAAaA,EAAK,CAAC,KAAKA,EAAK,CAAC,YAAYA,EAAK,UAAY,CAAC,GACpE,CACD5F,EAAO8O,EAAuB,uBAAuB,EACrD1G,GAAI,MAAM;AAAA,EAA8BgG,CAAG,EAC3C,MAAMW,EAAMC,GAAiBX,CAAE,EACzBY,EAAQF,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,MAAM,EAC5CG,EAAaD,EAAM,OAAO,MAAM,EAAE,KAAK,QAAStG,EAAY,KAAK,EAAE,KAAK,SAAUA,EAAY,MAAM,EAAE,KAAK,QAAS,YAAY,EACtIwG,GAAiBJ,EAAKpG,EAAY,OAAQA,EAAY,MAAO,EAAI,EACjEoG,EAAI,KAAK,UAAW,OAAOpG,EAAY,KAAK,IAAIA,EAAY,MAAM,EAAE,EACpEuG,EAAW,KAAK,OAAQT,EAAY,eAAe,EACnDD,EAAG,WAAWO,EAAI,OAAO,GAAG,EAAE,KAAK,QAAS,mBAAmB,CAAC,EAChE,MAAMK,EAASZ,EAAG,kBACZa,EAAS,CAAA,EACf,SAASC,EAASC,EAAO,CACvB,IAAIjJ,EAAO2I,EACPO,EAAS,GACb,SAAW,CAAC7P,CAAC,IAAK4P,EAAM,QAAO,EAAI,CACjC,IAAIE,EAASR,EACTtP,EAAI,GAAK0P,EAAOG,CAAM,IACxBC,EAASJ,EAAOG,CAAM,GAExBA,GAAUD,EAAM5P,CAAC,EACjB2G,EAAO+I,EAAOG,CAAM,EACflJ,IACHA,EAAO+I,EAAOG,CAAM,EAAIC,EAAO,OAAO,GAAG,EAAE,KAAK,QAASF,EAAM5P,CAAC,CAAC,EAEpE,CACD,OAAO2G,CACR,CACDtG,EAAOsP,EAAU,UAAU,EAC3B,UAAWI,KAASN,EAAQ,CAC1B,GAAIM,EAAM,KAAK,SAAW,EACxB,SAEF,MAAMC,EAAaL,EAASI,EAAM,UAAU,EAC5C,OAAQA,EAAM,KAAI,CAChB,IAAK,OACHC,EAAW,UAAU,MAAM,EAAE,KAAKD,EAAM,IAAI,EAAE,MAAK,EAAG,OAAO,MAAM,EAAE,KAAK,IAAM9J,GAASA,EAAK,CAAC,EAAE,KAAK,IAAMA,GAASA,EAAK,CAAC,EAAE,KAAK,QAAUA,GAASA,EAAK,KAAK,EAAE,KAAK,SAAWA,GAASA,EAAK,MAAM,EAAE,KAAK,OAASA,GAASA,EAAK,IAAI,EAAE,KAAK,SAAWA,GAASA,EAAK,UAAU,EAAE,KAAK,eAAiBA,GAASA,EAAK,WAAW,EAClU,MACF,IAAK,OACH+J,EAAW,UAAU,MAAM,EAAE,KAAKD,EAAM,IAAI,EAAE,MAAO,EAAC,OAAO,MAAM,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,IAAK,CAAC,EAAE,KAAK,OAAS9J,GAASA,EAAK,IAAI,EAAE,KAAK,YAAcA,GAASA,EAAK,QAAQ,EAAE,KAAK,oBAAsBA,GAAS8I,EAAoB9I,EAAK,WAAW,CAAC,EAAE,KAAK,cAAgBA,GAASgJ,EAAchJ,EAAK,aAAa,CAAC,EAAE,KAAK,YAAcA,GAASkJ,EAAsBlJ,CAAI,CAAC,EAAE,KAAMA,GAASA,EAAK,IAAI,EACzY,MACF,IAAK,OACH+J,EAAW,UAAU,MAAM,EAAE,KAAKD,EAAM,IAAI,EAAE,MAAO,EAAC,OAAO,MAAM,EAAE,KAAK,IAAM9J,GAASA,EAAK,IAAI,EAAE,KAAK,OAASA,GAASA,EAAK,KAAOA,EAAK,KAAO,MAAM,EAAE,KAAK,SAAWA,GAASA,EAAK,UAAU,EAAE,KAAK,eAAiBA,GAASA,EAAK,WAAW,EACpP,KACH,CACF,CACH,EAAG,MAAM,EACLgK,GAA0B,CAC5B,KAAAzB,EACF,EAGI0B,GAAU,CACZ,OAAQnK,GACR,GAAImI,GACJ,SAAU+B,EACZ", "x_google_ignoreList": [0, 1]}