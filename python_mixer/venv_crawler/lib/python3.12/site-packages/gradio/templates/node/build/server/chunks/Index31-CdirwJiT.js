import { c as create_ssr_component, e as escape, f as each } from './ssr-Cwm06D-i.js';

const css = {
  code: ".sketchbox.svelte-1ccw3kh.svelte-1ccw3kh{position:inherit;display:flex;flex-direction:inherit;align-items:inherit;min-height:32px}.sketchbox.svelte-1ccw3kh.svelte-1ccw3kh:not(.function_mode){cursor:pointer}.function_mode.svelte-1ccw3kh .cover.svelte-1ccw3kh{background-color:color-mix(\n			in srgb,\n			var(--background-fill-primary),\n			transparent 80%\n		)}.row.svelte-1ccw3kh>*,.row.svelte-1ccw3kh>.form,.row.svelte-1ccw3kh>.form > *{flex-grow:1 !important}.interaction.svelte-1ccw3kh.svelte-1ccw3kh{position:absolute;top:0;left:0;width:100%;height:100%;z-index:100}.cover.svelte-1ccw3kh.svelte-1ccw3kh{position:absolute;top:0;left:0;width:100%;height:100%;z-index:99}.interaction.svelte-1ccw3kh.svelte-1ccw3kh:hover,.active.svelte-1ccw3kh .interaction.svelte-1ccw3kh{border-color:var(--body-text-color);border-width:1px;border-radius:var(--block-radius)}.active.function_mode.svelte-1ccw3kh .interaction.svelte-1ccw3kh{border-color:var(--color-accent);border-width:2px}.interaction.svelte-1ccw3kh:hover .add.svelte-1ccw3kh,.interaction.svelte-1ccw3kh:hover .action.svelte-1ccw3kh{display:flex}button.svelte-1ccw3kh.svelte-1ccw3kh{border-color:var(--body-text-color);border-width:1px;background-color:var(--button-secondary-background-fill);justify-content:center;align-items:center;font-weight:bold}.add.svelte-1ccw3kh.svelte-1ccw3kh,.action.svelte-1ccw3kh.svelte-1ccw3kh{display:none;position:absolute}.action.svelte-1ccw3kh.svelte-1ccw3kh{border-radius:15px;width:30px;height:30px}.add.svelte-1ccw3kh.svelte-1ccw3kh{border-radius:10px;width:20px;height:20px}.function.svelte-1ccw3kh.svelte-1ccw3kh{border-radius:15px;padding:4px 8px;font-size:10px}.function.selected.svelte-1ccw3kh.svelte-1ccw3kh{color:white}.input.selected.svelte-1ccw3kh.svelte-1ccw3kh{background-color:oklch(0.707 0.165 254.624);border-color:oklch(0.707 0.165 254.624)}.output.selected.svelte-1ccw3kh.svelte-1ccw3kh{background-color:oklch(0.712 0.194 13.428);border-color:oklch(0.712 0.194 13.428)}.event.selected.svelte-1ccw3kh.svelte-1ccw3kh{background-color:oklch(0.702 0.183 293.541);border-color:oklch(0.702 0.183 293.541)}button.svelte-1ccw3kh.svelte-1ccw3kh:hover{background-color:var(--button-secondary-background-fill-hover)}.up.svelte-1ccw3kh.svelte-1ccw3kh{top:-10px;left:50%;transform:translate(-50%, 0);width:80%}.left.svelte-1ccw3kh.svelte-1ccw3kh{top:50%;left:-10px;transform:translate(0, -50%);height:80%}.right.svelte-1ccw3kh.svelte-1ccw3kh{top:50%;right:-10px;transform:translate(0, -50%);height:80%}.down.svelte-1ccw3kh.svelte-1ccw3kh{bottom:-10px;left:50%;transform:translate(-50%, 0);width:80%}.modify.svelte-1ccw3kh.svelte-1ccw3kh{top:50%;left:50%;transform:translate(calc(-50% - 20px), -50%)}.delete.svelte-1ccw3kh.svelte-1ccw3kh{top:50%;left:50%;transform:translate(calc(-50% + 20px), -50%)}.button-set.svelte-1ccw3kh.svelte-1ccw3kh{display:flex;gap:8px;justify-content:center;align-items:center;height:100%;flex-wrap:wrap;padding:0 30px}.component-name.svelte-1ccw3kh.svelte-1ccw3kh{background:var(--block-background-fill);border:var(--block-border-color) var(--block-border-width) solid;border-radius:var(--block-radius);height:100%;display:flex;align-items:center;justify-content:center}.component-name.svelte-1ccw3kh span.svelte-1ccw3kh{color:var(--body-text-color-subdued);font-style:italic}",
  map: '{"version":3,"file":"Index.svelte","sources":["Index.svelte"],"sourcesContent":["<script lang=\\"ts\\">export let row;\\nexport let is_container;\\nexport let component_type;\\nexport let var_name;\\nexport let active = false;\\nexport let function_mode = false;\\nexport let event_list;\\nexport let is_input = false;\\nexport let is_output = false;\\nexport let triggers = [];\\n$: is_function = component_type === \\"function\\";\\nexport let gradio;\\nconst dispatch = (type) => {\\n    return (event) => {\\n        event.stopPropagation();\\n        gradio.dispatch(\\"select\\", { index: 0, value: type });\\n    };\\n};\\nconst invisible_components = [\\"state\\", \\"browserstate\\", \\"function\\"];\\n<\/script>\\n\\n<div class=\\"sketchbox\\" class:function_mode class:row class:active>\\n\\t<div class=\\"cover\\"></div>\\n\\t<!-- svelte-ignore a11y-click-events-have-key-events -->\\n\\t<!-- svelte-ignore a11y-no-static-element-interactions -->\\n\\t<div\\n\\t\\tclass=\\"interaction\\"\\n\\t\\ton:click={is_container ? undefined : dispatch(\\"modify\\")}\\n\\t>\\n\\t\\t{#if invisible_components.includes(component_type)}\\n\\t\\t\\t<div class=\\"component-name\\">\\n\\t\\t\\t\\t<span>{component_type}:</span>&nbsp;{var_name}\\n\\t\\t\\t</div>\\n\\t\\t{/if}\\n\\t\\t{#if function_mode}\\n\\t\\t\\t{#if !is_function && !is_container}\\n\\t\\t\\t\\t<div class=\\"button-set\\">\\n\\t\\t\\t\\t\\t<button\\n\\t\\t\\t\\t\\t\\tclass=\\"function input\\"\\n\\t\\t\\t\\t\\t\\ton:click={dispatch(\\"input\\")}\\n\\t\\t\\t\\t\\t\\tclass:selected={is_input}>input</button\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t<button\\n\\t\\t\\t\\t\\t\\tclass=\\"function output\\"\\n\\t\\t\\t\\t\\t\\ton:click={dispatch(\\"output\\")}\\n\\t\\t\\t\\t\\t\\tclass:selected={is_output}>output</button\\n\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t| {#each event_list as event}\\n\\t\\t\\t\\t\\t\\t<button\\n\\t\\t\\t\\t\\t\\t\\tclass=\\"function event\\"\\n\\t\\t\\t\\t\\t\\t\\ton:click={dispatch(\\"on:\\" + event)}\\n\\t\\t\\t\\t\\t\\t\\tclass:selected={triggers.includes(event)}>on:{event}</button\\n\\t\\t\\t\\t\\t\\t>\\n\\t\\t\\t\\t\\t{/each}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t{/if}\\n\\t\\t{:else}\\n\\t\\t\\t<button class=\\"add up\\" on:click={dispatch(\\"up\\")}>+</button>\\n\\t\\t\\t<button class=\\"add left\\" on:click={dispatch(\\"left\\")}>+</button>\\n\\t\\t\\t<button class=\\"add right\\" on:click={dispatch(\\"right\\")}>+</button>\\n\\t\\t\\t<button class=\\"add down\\" on:click={dispatch(\\"down\\")}>+</button>\\n\\t\\t\\t{#if !is_container}\\n\\t\\t\\t\\t<button class=\\"action modify\\" on:click={dispatch(\\"modify\\")}>✎</button>\\n\\t\\t\\t\\t<button class=\\"action delete\\" on:click={dispatch(\\"delete\\")}>✗</button>\\n\\t\\t\\t{/if}\\n\\t\\t{/if}\\n\\t</div>\\n\\t<slot />\\n</div>\\n\\n<style>\\n\\t.sketchbox {\\n\\t\\tposition: inherit;\\n\\t\\tdisplay: flex;\\n\\t\\tflex-direction: inherit;\\n\\t\\talign-items: inherit;\\n\\t\\tmin-height: 32px;\\n\\t}\\n\\t.sketchbox:not(.function_mode) {\\n\\t\\tcursor: pointer;\\n\\t}\\n\\t.function_mode .cover {\\n\\t\\tbackground-color: color-mix(\\n\\t\\t\\tin srgb,\\n\\t\\t\\tvar(--background-fill-primary),\\n\\t\\t\\ttransparent 80%\\n\\t\\t);\\n\\t}\\n\\n\\t.row > :global(*),\\n\\t.row > :global(.form),\\n\\t.row > :global(.form > *) {\\n\\t\\tflex-grow: 1 !important;\\n\\t}\\n\\t.interaction {\\n\\t\\tposition: absolute;\\n\\t\\ttop: 0;\\n\\t\\tleft: 0;\\n\\t\\twidth: 100%;\\n\\t\\theight: 100%;\\n\\t\\tz-index: 100;\\n\\t}\\n\\t.cover {\\n\\t\\tposition: absolute;\\n\\t\\ttop: 0;\\n\\t\\tleft: 0;\\n\\t\\twidth: 100%;\\n\\t\\theight: 100%;\\n\\t\\tz-index: 99;\\n\\t}\\n\\t.interaction:hover,\\n\\t.active .interaction {\\n\\t\\tborder-color: var(--body-text-color);\\n\\t\\tborder-width: 1px;\\n\\t\\tborder-radius: var(--block-radius);\\n\\t}\\n\\t.active.function_mode .interaction {\\n\\t\\tborder-color: var(--color-accent);\\n\\t\\tborder-width: 2px;\\n\\t}\\n\\t.interaction:hover .add,\\n\\t.interaction:hover .action {\\n\\t\\tdisplay: flex;\\n\\t}\\n\\tbutton {\\n\\t\\tborder-color: var(--body-text-color);\\n\\t\\tborder-width: 1px;\\n\\t\\tbackground-color: var(--button-secondary-background-fill);\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\tfont-weight: bold;\\n\\t}\\n\\t.add,\\n\\t.action {\\n\\t\\tdisplay: none;\\n\\t\\tposition: absolute;\\n\\t}\\n\\t.action {\\n\\t\\tborder-radius: 15px;\\n\\t\\twidth: 30px;\\n\\t\\theight: 30px;\\n\\t}\\n\\t.add {\\n\\t\\tborder-radius: 10px;\\n\\t\\twidth: 20px;\\n\\t\\theight: 20px;\\n\\t}\\n\\t.function {\\n\\t\\tborder-radius: 15px;\\n\\t\\tpadding: 4px 8px;\\n\\t\\tfont-size: 10px;\\n\\t}\\n\\t.function.selected {\\n\\t\\tcolor: white;\\n\\t}\\n\\t.input.selected {\\n\\t\\tbackground-color: oklch(0.707 0.165 254.624);\\n\\t\\tborder-color: oklch(0.707 0.165 254.624);\\n\\t}\\n\\t.output.selected {\\n\\t\\tbackground-color: oklch(0.712 0.194 13.428);\\n\\t\\tborder-color: oklch(0.712 0.194 13.428);\\n\\t}\\n\\t.event.selected {\\n\\t\\tbackground-color: oklch(0.702 0.183 293.541);\\n\\t\\tborder-color: oklch(0.702 0.183 293.541);\\n\\t}\\n\\tbutton:hover {\\n\\t\\tbackground-color: var(--button-secondary-background-fill-hover);\\n\\t}\\n\\t.up {\\n\\t\\ttop: -10px;\\n\\t\\tleft: 50%;\\n\\t\\ttransform: translate(-50%, 0);\\n\\t\\twidth: 80%;\\n\\t}\\n\\t.left {\\n\\t\\ttop: 50%;\\n\\t\\tleft: -10px;\\n\\t\\ttransform: translate(0, -50%);\\n\\t\\theight: 80%;\\n\\t}\\n\\t.right {\\n\\t\\ttop: 50%;\\n\\t\\tright: -10px;\\n\\t\\ttransform: translate(0, -50%);\\n\\t\\theight: 80%;\\n\\t}\\n\\t.down {\\n\\t\\tbottom: -10px;\\n\\t\\tleft: 50%;\\n\\t\\ttransform: translate(-50%, 0);\\n\\t\\twidth: 80%;\\n\\t}\\n\\t.modify {\\n\\t\\ttop: 50%;\\n\\t\\tleft: 50%;\\n\\t\\ttransform: translate(calc(-50% - 20px), -50%);\\n\\t}\\n\\t.delete {\\n\\t\\ttop: 50%;\\n\\t\\tleft: 50%;\\n\\t\\ttransform: translate(calc(-50% + 20px), -50%);\\n\\t}\\n\\t.button-set {\\n\\t\\tdisplay: flex;\\n\\t\\tgap: 8px;\\n\\t\\tjustify-content: center;\\n\\t\\talign-items: center;\\n\\t\\theight: 100%;\\n\\t\\tflex-wrap: wrap;\\n\\t\\tpadding: 0 30px;\\n\\t}\\n\\t.component-name {\\n\\t\\tbackground: var(--block-background-fill);\\n\\t\\tborder: var(--block-border-color) var(--block-border-width) solid;\\n\\t\\tborder-radius: var(--block-radius);\\n\\t\\theight: 100%;\\n\\t\\tdisplay: flex;\\n\\t\\talign-items: center;\\n\\t\\tjustify-content: center;\\n\\t}\\n\\t.component-name span {\\n\\t\\tcolor: var(--body-text-color-subdued);\\n\\t\\tfont-style: italic;\\n\\t}</style>\\n"],"names":[],"mappings":"AAuEC,wCAAW,CACV,QAAQ,CAAE,OAAO,CACjB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,OAAO,CACvB,WAAW,CAAE,OAAO,CACpB,UAAU,CAAE,IACb,CACA,wCAAU,KAAK,cAAc,CAAE,CAC9B,MAAM,CAAE,OACT,CACA,6BAAc,CAAC,qBAAO,CACrB,gBAAgB,CAAE;AACpB,GAAG,EAAE,CAAC,IAAI,CAAC;AACX,GAAG,IAAI,yBAAyB,CAAC,CAAC;AAClC,GAAG,WAAW,CAAC,GAAG;AAClB,GACC,CAEA,mBAAI,CAAW,CAAE,CACjB,mBAAI,CAAW,KAAM,CACrB,mBAAI,CAAW,SAAW,CACzB,SAAS,CAAE,CAAC,CAAC,UACd,CACA,0CAAa,CACZ,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,GACV,CACA,oCAAO,CACN,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,EACV,CACA,0CAAY,MAAM,CAClB,sBAAO,CAAC,2BAAa,CACpB,YAAY,CAAE,IAAI,iBAAiB,CAAC,CACpC,YAAY,CAAE,GAAG,CACjB,aAAa,CAAE,IAAI,cAAc,CAClC,CACA,OAAO,6BAAc,CAAC,2BAAa,CAClC,YAAY,CAAE,IAAI,cAAc,CAAC,CACjC,YAAY,CAAE,GACf,CACA,2BAAY,MAAM,CAAC,mBAAI,CACvB,2BAAY,MAAM,CAAC,sBAAQ,CAC1B,OAAO,CAAE,IACV,CACA,oCAAO,CACN,YAAY,CAAE,IAAI,iBAAiB,CAAC,CACpC,YAAY,CAAE,GAAG,CACjB,gBAAgB,CAAE,IAAI,kCAAkC,CAAC,CACzD,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,WAAW,CAAE,IACd,CACA,kCAAI,CACJ,qCAAQ,CACP,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QACX,CACA,qCAAQ,CACP,aAAa,CAAE,IAAI,CACnB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CACA,kCAAK,CACJ,aAAa,CAAE,IAAI,CACnB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CACA,uCAAU,CACT,aAAa,CAAE,IAAI,CACnB,OAAO,CAAE,GAAG,CAAC,GAAG,CAChB,SAAS,CAAE,IACZ,CACA,SAAS,uCAAU,CAClB,KAAK,CAAE,KACR,CACA,MAAM,uCAAU,CACf,gBAAgB,CAAE,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAC5C,YAAY,CAAE,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,CACxC,CACA,OAAO,uCAAU,CAChB,gBAAgB,CAAE,MAAM,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAC3C,YAAY,CAAE,MAAM,KAAK,CAAC,KAAK,CAAC,MAAM,CACvC,CACA,MAAM,uCAAU,CACf,gBAAgB,CAAE,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAC5C,YAAY,CAAE,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,CACxC,CACA,oCAAM,MAAO,CACZ,gBAAgB,CAAE,IAAI,wCAAwC,CAC/D,CACA,iCAAI,CACH,GAAG,CAAE,KAAK,CACV,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAC7B,KAAK,CAAE,GACR,CACA,mCAAM,CACL,GAAG,CAAE,GAAG,CACR,IAAI,CAAE,KAAK,CACX,SAAS,CAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAC7B,MAAM,CAAE,GACT,CACA,oCAAO,CACN,GAAG,CAAE,GAAG,CACR,KAAK,CAAE,KAAK,CACZ,SAAS,CAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAC7B,MAAM,CAAE,GACT,CACA,mCAAM,CACL,MAAM,CAAE,KAAK,CACb,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAC7B,KAAK,CAAE,GACR,CACA,qCAAQ,CACP,GAAG,CAAE,GAAG,CACR,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAC7C,CACA,qCAAQ,CACP,GAAG,CAAE,GAAG,CACR,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,UAAU,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAC7C,CACA,yCAAY,CACX,OAAO,CAAE,IAAI,CACb,GAAG,CAAE,GAAG,CACR,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,IAAI,CACZ,SAAS,CAAE,IAAI,CACf,OAAO,CAAE,CAAC,CAAC,IACZ,CACA,6CAAgB,CACf,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,MAAM,CAAE,IAAI,oBAAoB,CAAC,CAAC,IAAI,oBAAoB,CAAC,CAAC,KAAK,CACjE,aAAa,CAAE,IAAI,cAAc,CAAC,CAClC,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAClB,CACA,8BAAe,CAAC,mBAAK,CACpB,KAAK,CAAE,IAAI,yBAAyB,CAAC,CACrC,UAAU,CAAE,MACb"}'
};
const Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {
  let is_function;
  let { row } = $$props;
  let { is_container } = $$props;
  let { component_type } = $$props;
  let { var_name } = $$props;
  let { active = false } = $$props;
  let { function_mode = false } = $$props;
  let { event_list } = $$props;
  let { is_input = false } = $$props;
  let { is_output = false } = $$props;
  let { triggers = [] } = $$props;
  let { gradio } = $$props;
  const invisible_components = ["state", "browserstate", "function"];
  if ($$props.row === void 0 && $$bindings.row && row !== void 0)
    $$bindings.row(row);
  if ($$props.is_container === void 0 && $$bindings.is_container && is_container !== void 0)
    $$bindings.is_container(is_container);
  if ($$props.component_type === void 0 && $$bindings.component_type && component_type !== void 0)
    $$bindings.component_type(component_type);
  if ($$props.var_name === void 0 && $$bindings.var_name && var_name !== void 0)
    $$bindings.var_name(var_name);
  if ($$props.active === void 0 && $$bindings.active && active !== void 0)
    $$bindings.active(active);
  if ($$props.function_mode === void 0 && $$bindings.function_mode && function_mode !== void 0)
    $$bindings.function_mode(function_mode);
  if ($$props.event_list === void 0 && $$bindings.event_list && event_list !== void 0)
    $$bindings.event_list(event_list);
  if ($$props.is_input === void 0 && $$bindings.is_input && is_input !== void 0)
    $$bindings.is_input(is_input);
  if ($$props.is_output === void 0 && $$bindings.is_output && is_output !== void 0)
    $$bindings.is_output(is_output);
  if ($$props.triggers === void 0 && $$bindings.triggers && triggers !== void 0)
    $$bindings.triggers(triggers);
  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)
    $$bindings.gradio(gradio);
  $$result.css.add(css);
  is_function = component_type === "function";
  return `<div class="${[
    "sketchbox svelte-1ccw3kh",
    (function_mode ? "function_mode" : "") + " " + (row ? "row" : "") + " " + (active ? "active" : "")
  ].join(" ").trim()}"><div class="cover svelte-1ccw3kh"></div>   <div class="interaction svelte-1ccw3kh">${invisible_components.includes(component_type) ? `<div class="component-name svelte-1ccw3kh"><span class="svelte-1ccw3kh">${escape(component_type)}:</span> ${escape(var_name)}</div>` : ``} ${function_mode ? `${!is_function && !is_container ? `<div class="button-set svelte-1ccw3kh"><button class="${["function input svelte-1ccw3kh", is_input ? "selected" : ""].join(" ").trim()}" data-svelte-h="svelte-g0b3ia">input</button> <button class="${["function output svelte-1ccw3kh", is_output ? "selected" : ""].join(" ").trim()}" data-svelte-h="svelte-zz2f36">output</button>
					| ${each(event_list, (event) => {
    return `<button class="${[
      "function event svelte-1ccw3kh",
      triggers.includes(event) ? "selected" : ""
    ].join(" ").trim()}">on:${escape(event)}</button>`;
  })}</div>` : ``}` : `<button class="add up svelte-1ccw3kh" data-svelte-h="svelte-9vgc4e">+</button> <button class="add left svelte-1ccw3kh" data-svelte-h="svelte-pewocm">+</button> <button class="add right svelte-1ccw3kh" data-svelte-h="svelte-4vsur8">+</button> <button class="add down svelte-1ccw3kh" data-svelte-h="svelte-c945mq">+</button> ${!is_container ? `<button class="action modify svelte-1ccw3kh" data-svelte-h="svelte-xeup1i">✎</button> <button class="action delete svelte-1ccw3kh" data-svelte-h="svelte-ko6iaj">✗</button>` : ``}`}</div> ${slots.default ? slots.default({}) : ``} </div>`;
});

export { Index as default };
//# sourceMappingURL=Index31-CdirwJiT.js.map
