{"version": 3, "file": "Component-BlohB9Ds.js", "sources": ["../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/scheduler.js", "../../../../../../node_modules/.pnpm/svelte@4.2.15/node_modules/svelte/src/runtime/internal/Component.js"], "sourcesContent": ["import { run_all } from './utils.js';\nimport { current_component, set_current_component } from './lifecycle.js';\n\nexport const dirty_components = [];\nexport const intros = { enabled: false };\nexport const binding_callbacks = [];\n\nlet render_callbacks = [];\n\nconst flush_callbacks = [];\n\nconst resolved_promise = /* @__PURE__ */ Promise.resolve();\n\nlet update_scheduled = false;\n\n/** @returns {void} */\nexport function schedule_update() {\n\tif (!update_scheduled) {\n\t\tupdate_scheduled = true;\n\t\tresolved_promise.then(flush);\n\t}\n}\n\n/** @returns {Promise<void>} */\nexport function tick() {\n\tschedule_update();\n\treturn resolved_promise;\n}\n\n/** @returns {void} */\nexport function add_render_callback(fn) {\n\trender_callbacks.push(fn);\n}\n\n/** @returns {void} */\nexport function add_flush_callback(fn) {\n\tflush_callbacks.push(fn);\n}\n\n// flush() calls callbacks in this order:\n// 1. All beforeUpdate callbacks, in order: parents before children\n// 2. All bind:this callbacks, in reverse order: children before parents.\n// 3. All afterUpdate callbacks, in order: parents before children. EXCEPT\n//    for afterUpdates called during the initial onMount, which are called in\n//    reverse order: children before parents.\n// Since callbacks might update component values, which could trigger another\n// call to flush(), the following steps guard against this:\n// 1. During beforeUpdate, any updated components will be added to the\n//    dirty_components array and will cause a reentrant call to flush(). Because\n//    the flush index is kept outside the function, the reentrant call will pick\n//    up where the earlier call left off and go through all dirty components. The\n//    current_component value is saved and restored so that the reentrant call will\n//    not interfere with the \"parent\" flush() call.\n// 2. bind:this callbacks cannot trigger new flush() calls.\n// 3. During afterUpdate, any updated components will NOT have their afterUpdate\n//    callback called a second time; the seen_callbacks set, outside the flush()\n//    function, guarantees this behavior.\nconst seen_callbacks = new Set();\n\nlet flushidx = 0; // Do *not* move this inside the flush() function\n\n/** @returns {void} */\nexport function flush() {\n\t// Do not reenter flush while dirty components are updated, as this can\n\t// result in an infinite loop. Instead, let the inner flush handle it.\n\t// Reentrancy is ok afterwards for bindings etc.\n\tif (flushidx !== 0) {\n\t\treturn;\n\t}\n\tconst saved_component = current_component;\n\tdo {\n\t\t// first, call beforeUpdate functions\n\t\t// and update components\n\t\ttry {\n\t\t\twhile (flushidx < dirty_components.length) {\n\t\t\t\tconst component = dirty_components[flushidx];\n\t\t\t\tflushidx++;\n\t\t\t\tset_current_component(component);\n\t\t\t\tupdate(component.$$);\n\t\t\t}\n\t\t} catch (e) {\n\t\t\t// reset dirty state to not end up in a deadlocked state and then rethrow\n\t\t\tdirty_components.length = 0;\n\t\t\tflushidx = 0;\n\t\t\tthrow e;\n\t\t}\n\t\tset_current_component(null);\n\t\tdirty_components.length = 0;\n\t\tflushidx = 0;\n\t\twhile (binding_callbacks.length) binding_callbacks.pop()();\n\t\t// then, once components are updated, call\n\t\t// afterUpdate functions. This may cause\n\t\t// subsequent updates...\n\t\tfor (let i = 0; i < render_callbacks.length; i += 1) {\n\t\t\tconst callback = render_callbacks[i];\n\t\t\tif (!seen_callbacks.has(callback)) {\n\t\t\t\t// ...so guard against infinite loops\n\t\t\t\tseen_callbacks.add(callback);\n\t\t\t\tcallback();\n\t\t\t}\n\t\t}\n\t\trender_callbacks.length = 0;\n\t} while (dirty_components.length);\n\twhile (flush_callbacks.length) {\n\t\tflush_callbacks.pop()();\n\t}\n\tupdate_scheduled = false;\n\tseen_callbacks.clear();\n\tset_current_component(saved_component);\n}\n\n/** @returns {void} */\nfunction update($$) {\n\tif ($$.fragment !== null) {\n\t\t$$.update();\n\t\trun_all($$.before_update);\n\t\tconst dirty = $$.dirty;\n\t\t$$.dirty = [-1];\n\t\t$$.fragment && $$.fragment.p($$.ctx, dirty);\n\t\t$$.after_update.forEach(add_render_callback);\n\t}\n}\n\n/**\n * Useful for example to execute remaining `afterUpdate` callbacks before executing `destroy`.\n * @param {Function[]} fns\n * @returns {void}\n */\nexport function flush_render_callbacks(fns) {\n\tconst filtered = [];\n\tconst targets = [];\n\trender_callbacks.forEach((c) => (fns.indexOf(c) === -1 ? filtered.push(c) : targets.push(c)));\n\ttargets.forEach((c) => c());\n\trender_callbacks = filtered;\n}\n", "import {\n\tadd_render_callback,\n\tflush,\n\tflush_render_callbacks,\n\tschedule_update,\n\tdirty_components\n} from './scheduler.js';\nimport { current_component, set_current_component } from './lifecycle.js';\nimport { blank_object, is_empty, is_function, run, run_all, noop } from './utils.js';\nimport {\n\tchildren,\n\tdetach,\n\tstart_hydrating,\n\tend_hydrating,\n\tget_custom_elements_slots,\n\tinsert,\n\telement,\n\tattr\n} from './dom.js';\nimport { transition_in } from './transitions.js';\n\n/** @returns {void} */\nexport function bind(component, name, callback) {\n\tconst index = component.$$.props[name];\n\tif (index !== undefined) {\n\t\tcomponent.$$.bound[index] = callback;\n\t\tcallback(component.$$.ctx[index]);\n\t}\n}\n\n/** @returns {void} */\nexport function create_component(block) {\n\tblock && block.c();\n}\n\n/** @returns {void} */\nexport function claim_component(block, parent_nodes) {\n\tblock && block.l(parent_nodes);\n}\n\n/** @returns {void} */\nexport function mount_component(component, target, anchor) {\n\tconst { fragment, after_update } = component.$$;\n\tfragment && fragment.m(target, anchor);\n\t// onMount happens before the initial afterUpdate\n\tadd_render_callback(() => {\n\t\tconst new_on_destroy = component.$$.on_mount.map(run).filter(is_function);\n\t\t// if the component was destroyed immediately\n\t\t// it will update the `$$.on_destroy` reference to `null`.\n\t\t// the destructured on_destroy may still reference to the old array\n\t\tif (component.$$.on_destroy) {\n\t\t\tcomponent.$$.on_destroy.push(...new_on_destroy);\n\t\t} else {\n\t\t\t// Edge case - component was destroyed immediately,\n\t\t\t// most likely as a result of a binding initialising\n\t\t\trun_all(new_on_destroy);\n\t\t}\n\t\tcomponent.$$.on_mount = [];\n\t});\n\tafter_update.forEach(add_render_callback);\n}\n\n/** @returns {void} */\nexport function destroy_component(component, detaching) {\n\tconst $$ = component.$$;\n\tif ($$.fragment !== null) {\n\t\tflush_render_callbacks($$.after_update);\n\t\trun_all($$.on_destroy);\n\t\t$$.fragment && $$.fragment.d(detaching);\n\t\t// TODO null out other refs, including component.$$ (but need to\n\t\t// preserve final state?)\n\t\t$$.on_destroy = $$.fragment = null;\n\t\t$$.ctx = [];\n\t}\n}\n\n/** @returns {void} */\nfunction make_dirty(component, i) {\n\tif (component.$$.dirty[0] === -1) {\n\t\tdirty_components.push(component);\n\t\tschedule_update();\n\t\tcomponent.$$.dirty.fill(0);\n\t}\n\tcomponent.$$.dirty[(i / 31) | 0] |= 1 << i % 31;\n}\n\n// TODO: Document the other params\n/**\n * @param {SvelteComponent} component\n * @param {import('./public.js').ComponentConstructorOptions} options\n *\n * @param {import('./utils.js')['not_equal']} not_equal Used to compare props and state values.\n * @param {(target: Element | ShadowRoot) => void} [append_styles] Function that appends styles to the DOM when the component is first initialised.\n * This will be the `add_css` function from the compiled component.\n *\n * @returns {void}\n */\nexport function init(\n\tcomponent,\n\toptions,\n\tinstance,\n\tcreate_fragment,\n\tnot_equal,\n\tprops,\n\tappend_styles = null,\n\tdirty = [-1]\n) {\n\tconst parent_component = current_component;\n\tset_current_component(component);\n\t/** @type {import('./private.js').T$$} */\n\tconst $$ = (component.$$ = {\n\t\tfragment: null,\n\t\tctx: [],\n\t\t// state\n\t\tprops,\n\t\tupdate: noop,\n\t\tnot_equal,\n\t\tbound: blank_object(),\n\t\t// lifecycle\n\t\ton_mount: [],\n\t\ton_destroy: [],\n\t\ton_disconnect: [],\n\t\tbefore_update: [],\n\t\tafter_update: [],\n\t\tcontext: new Map(options.context || (parent_component ? parent_component.$$.context : [])),\n\t\t// everything else\n\t\tcallbacks: blank_object(),\n\t\tdirty,\n\t\tskip_bound: false,\n\t\troot: options.target || parent_component.$$.root\n\t});\n\tappend_styles && append_styles($$.root);\n\tlet ready = false;\n\t$$.ctx = instance\n\t\t? instance(component, options.props || {}, (i, ret, ...rest) => {\n\t\t\t\tconst value = rest.length ? rest[0] : ret;\n\t\t\t\tif ($$.ctx && not_equal($$.ctx[i], ($$.ctx[i] = value))) {\n\t\t\t\t\tif (!$$.skip_bound && $$.bound[i]) $$.bound[i](value);\n\t\t\t\t\tif (ready) make_dirty(component, i);\n\t\t\t\t}\n\t\t\t\treturn ret;\n\t\t  })\n\t\t: [];\n\t$$.update();\n\tready = true;\n\trun_all($$.before_update);\n\t// `false` as a special case of no DOM component\n\t$$.fragment = create_fragment ? create_fragment($$.ctx) : false;\n\tif (options.target) {\n\t\tif (options.hydrate) {\n\t\t\tstart_hydrating();\n\t\t\t// TODO: what is the correct type here?\n\t\t\t// @ts-expect-error\n\t\t\tconst nodes = children(options.target);\n\t\t\t$$.fragment && $$.fragment.l(nodes);\n\t\t\tnodes.forEach(detach);\n\t\t} else {\n\t\t\t// eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n\t\t\t$$.fragment && $$.fragment.c();\n\t\t}\n\t\tif (options.intro) transition_in(component.$$.fragment);\n\t\tmount_component(component, options.target, options.anchor);\n\t\tend_hydrating();\n\t\tflush();\n\t}\n\tset_current_component(parent_component);\n}\n\nexport let SvelteElement;\n\nif (typeof HTMLElement === 'function') {\n\tSvelteElement = class extends HTMLElement {\n\t\t/** The Svelte component constructor */\n\t\t$$ctor;\n\t\t/** Slots */\n\t\t$$s;\n\t\t/** The Svelte component instance */\n\t\t$$c;\n\t\t/** Whether or not the custom element is connected */\n\t\t$$cn = false;\n\t\t/** Component props data */\n\t\t$$d = {};\n\t\t/** `true` if currently in the process of reflecting component props back to attributes */\n\t\t$$r = false;\n\t\t/** @type {Record<string, CustomElementPropDefinition>} Props definition (name, reflected, type etc) */\n\t\t$$p_d = {};\n\t\t/** @type {Record<string, Function[]>} Event listeners */\n\t\t$$l = {};\n\t\t/** @type {Map<Function, Function>} Event listener unsubscribe functions */\n\t\t$$l_u = new Map();\n\n\t\tconstructor($$componentCtor, $$slots, use_shadow_dom) {\n\t\t\tsuper();\n\t\t\tthis.$$ctor = $$componentCtor;\n\t\t\tthis.$$s = $$slots;\n\t\t\tif (use_shadow_dom) {\n\t\t\t\tthis.attachShadow({ mode: 'open' });\n\t\t\t}\n\t\t}\n\n\t\taddEventListener(type, listener, options) {\n\t\t\t// We can't determine upfront if the event is a custom event or not, so we have to\n\t\t\t// listen to both. If someone uses a custom event with the same name as a regular\n\t\t\t// browser event, this fires twice - we can't avoid that.\n\t\t\tthis.$$l[type] = this.$$l[type] || [];\n\t\t\tthis.$$l[type].push(listener);\n\t\t\tif (this.$$c) {\n\t\t\t\tconst unsub = this.$$c.$on(type, listener);\n\t\t\t\tthis.$$l_u.set(listener, unsub);\n\t\t\t}\n\t\t\tsuper.addEventListener(type, listener, options);\n\t\t}\n\n\t\tremoveEventListener(type, listener, options) {\n\t\t\tsuper.removeEventListener(type, listener, options);\n\t\t\tif (this.$$c) {\n\t\t\t\tconst unsub = this.$$l_u.get(listener);\n\t\t\t\tif (unsub) {\n\t\t\t\t\tunsub();\n\t\t\t\t\tthis.$$l_u.delete(listener);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tasync connectedCallback() {\n\t\t\tthis.$$cn = true;\n\t\t\tif (!this.$$c) {\n\t\t\t\t// We wait one tick to let possible child slot elements be created/mounted\n\t\t\t\tawait Promise.resolve();\n\t\t\t\tif (!this.$$cn || this.$$c) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tfunction create_slot(name) {\n\t\t\t\t\treturn () => {\n\t\t\t\t\t\tlet node;\n\t\t\t\t\t\tconst obj = {\n\t\t\t\t\t\t\tc: function create() {\n\t\t\t\t\t\t\t\tnode = element('slot');\n\t\t\t\t\t\t\t\tif (name !== 'default') {\n\t\t\t\t\t\t\t\t\tattr(node, 'name', name);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t/**\n\t\t\t\t\t\t\t * @param {HTMLElement} target\n\t\t\t\t\t\t\t * @param {HTMLElement} [anchor]\n\t\t\t\t\t\t\t */\n\t\t\t\t\t\t\tm: function mount(target, anchor) {\n\t\t\t\t\t\t\t\tinsert(target, node, anchor);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\td: function destroy(detaching) {\n\t\t\t\t\t\t\t\tif (detaching) {\n\t\t\t\t\t\t\t\t\tdetach(node);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t};\n\t\t\t\t\t\treturn obj;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tconst $$slots = {};\n\t\t\t\tconst existing_slots = get_custom_elements_slots(this);\n\t\t\t\tfor (const name of this.$$s) {\n\t\t\t\t\tif (name in existing_slots) {\n\t\t\t\t\t\t$$slots[name] = [create_slot(name)];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (const attribute of this.attributes) {\n\t\t\t\t\t// this.$$data takes precedence over this.attributes\n\t\t\t\t\tconst name = this.$$g_p(attribute.name);\n\t\t\t\t\tif (!(name in this.$$d)) {\n\t\t\t\t\t\tthis.$$d[name] = get_custom_element_value(name, attribute.value, this.$$p_d, 'toProp');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// Port over props that were set programmatically before ce was initialized\n\t\t\t\tfor (const key in this.$$p_d) {\n\t\t\t\t\tif (!(key in this.$$d) && this[key] !== undefined) {\n\t\t\t\t\t\tthis.$$d[key] = this[key]; // don't transform, these were set through JavaScript\n\t\t\t\t\t\tdelete this[key]; // remove the property that shadows the getter/setter\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$$c = new this.$$ctor({\n\t\t\t\t\ttarget: this.shadowRoot || this,\n\t\t\t\t\tprops: {\n\t\t\t\t\t\t...this.$$d,\n\t\t\t\t\t\t$$slots,\n\t\t\t\t\t\t$$scope: {\n\t\t\t\t\t\t\tctx: []\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\t// Reflect component props as attributes\n\t\t\t\tconst reflect_attributes = () => {\n\t\t\t\t\tthis.$$r = true;\n\t\t\t\t\tfor (const key in this.$$p_d) {\n\t\t\t\t\t\tthis.$$d[key] = this.$$c.$$.ctx[this.$$c.$$.props[key]];\n\t\t\t\t\t\tif (this.$$p_d[key].reflect) {\n\t\t\t\t\t\t\tconst attribute_value = get_custom_element_value(\n\t\t\t\t\t\t\t\tkey,\n\t\t\t\t\t\t\t\tthis.$$d[key],\n\t\t\t\t\t\t\t\tthis.$$p_d,\n\t\t\t\t\t\t\t\t'toAttribute'\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t\tif (attribute_value == null) {\n\t\t\t\t\t\t\t\tthis.removeAttribute(this.$$p_d[key].attribute || key);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.setAttribute(this.$$p_d[key].attribute || key, attribute_value);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.$$r = false;\n\t\t\t\t};\n\t\t\t\tthis.$$c.$$.after_update.push(reflect_attributes);\n\t\t\t\treflect_attributes(); // once initially because after_update is added too late for first render\n\n\t\t\t\tfor (const type in this.$$l) {\n\t\t\t\t\tfor (const listener of this.$$l[type]) {\n\t\t\t\t\t\tconst unsub = this.$$c.$on(type, listener);\n\t\t\t\t\t\tthis.$$l_u.set(listener, unsub);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.$$l = {};\n\t\t\t}\n\t\t}\n\n\t\t// We don't need this when working within Svelte code, but for compatibility of people using this outside of Svelte\n\t\t// and setting attributes through setAttribute etc, this is helpful\n\t\tattributeChangedCallback(attr, _oldValue, newValue) {\n\t\t\tif (this.$$r) return;\n\t\t\tattr = this.$$g_p(attr);\n\t\t\tthis.$$d[attr] = get_custom_element_value(attr, newValue, this.$$p_d, 'toProp');\n\t\t\tthis.$$c?.$set({ [attr]: this.$$d[attr] });\n\t\t}\n\n\t\tdisconnectedCallback() {\n\t\t\tthis.$$cn = false;\n\t\t\t// In a microtask, because this could be a move within the DOM\n\t\t\tPromise.resolve().then(() => {\n\t\t\t\tif (!this.$$cn) {\n\t\t\t\t\tthis.$$c.$destroy();\n\t\t\t\t\tthis.$$c = undefined;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\t$$g_p(attribute_name) {\n\t\t\treturn (\n\t\t\t\tObject.keys(this.$$p_d).find(\n\t\t\t\t\t(key) =>\n\t\t\t\t\t\tthis.$$p_d[key].attribute === attribute_name ||\n\t\t\t\t\t\t(!this.$$p_d[key].attribute && key.toLowerCase() === attribute_name)\n\t\t\t\t) || attribute_name\n\t\t\t);\n\t\t}\n\t};\n}\n\n/**\n * @param {string} prop\n * @param {any} value\n * @param {Record<string, CustomElementPropDefinition>} props_definition\n * @param {'toAttribute' | 'toProp'} [transform]\n */\nfunction get_custom_element_value(prop, value, props_definition, transform) {\n\tconst type = props_definition[prop]?.type;\n\tvalue = type === 'Boolean' && typeof value !== 'boolean' ? value != null : value;\n\tif (!transform || !props_definition[prop]) {\n\t\treturn value;\n\t} else if (transform === 'toAttribute') {\n\t\tswitch (type) {\n\t\t\tcase 'Object':\n\t\t\tcase 'Array':\n\t\t\t\treturn value == null ? null : JSON.stringify(value);\n\t\t\tcase 'Boolean':\n\t\t\t\treturn value ? '' : null;\n\t\t\tcase 'Number':\n\t\t\t\treturn value == null ? null : value;\n\t\t\tdefault:\n\t\t\t\treturn value;\n\t\t}\n\t} else {\n\t\tswitch (type) {\n\t\t\tcase 'Object':\n\t\t\tcase 'Array':\n\t\t\t\treturn value && JSON.parse(value);\n\t\t\tcase 'Boolean':\n\t\t\t\treturn value; // conversion already handled above\n\t\t\tcase 'Number':\n\t\t\t\treturn value != null ? +value : value;\n\t\t\tdefault:\n\t\t\t\treturn value;\n\t\t}\n\t}\n}\n\n/**\n * @internal\n *\n * Turn a Svelte component into a custom element.\n * @param {import('./public.js').ComponentType} Component  A Svelte component constructor\n * @param {Record<string, CustomElementPropDefinition>} props_definition  The props to observe\n * @param {string[]} slots  The slots to create\n * @param {string[]} accessors  Other accessors besides the ones for props the component has\n * @param {boolean} use_shadow_dom  Whether to use shadow DOM\n * @param {(ce: new () => HTMLElement) => new () => HTMLElement} [extend]\n */\nexport function create_custom_element(\n\tComponent,\n\tprops_definition,\n\tslots,\n\taccessors,\n\tuse_shadow_dom,\n\textend\n) {\n\tlet Class = class extends SvelteElement {\n\t\tconstructor() {\n\t\t\tsuper(Component, slots, use_shadow_dom);\n\t\t\tthis.$$p_d = props_definition;\n\t\t}\n\t\tstatic get observedAttributes() {\n\t\t\treturn Object.keys(props_definition).map((key) =>\n\t\t\t\t(props_definition[key].attribute || key).toLowerCase()\n\t\t\t);\n\t\t}\n\t};\n\tObject.keys(props_definition).forEach((prop) => {\n\t\tObject.defineProperty(Class.prototype, prop, {\n\t\t\tget() {\n\t\t\t\treturn this.$$c && prop in this.$$c ? this.$$c[prop] : this.$$d[prop];\n\t\t\t},\n\t\t\tset(value) {\n\t\t\t\tvalue = get_custom_element_value(prop, value, props_definition);\n\t\t\t\tthis.$$d[prop] = value;\n\t\t\t\tthis.$$c?.$set({ [prop]: value });\n\t\t\t}\n\t\t});\n\t});\n\taccessors.forEach((accessor) => {\n\t\tObject.defineProperty(Class.prototype, accessor, {\n\t\t\tget() {\n\t\t\t\treturn this.$$c?.[accessor];\n\t\t\t}\n\t\t});\n\t});\n\tif (extend) {\n\t\t// @ts-expect-error - assigning here is fine\n\t\tClass = extend(Class);\n\t}\n\tComponent.element = /** @type {any} */ (Class);\n\treturn Class;\n}\n\n/**\n * Base class for Svelte components. Used when dev=false.\n *\n * @template {Record<string, any>} [Props=any]\n * @template {Record<string, any>} [Events=any]\n */\nexport class SvelteComponent {\n\t/**\n\t * ### PRIVATE API\n\t *\n\t * Do not use, may change at any time\n\t *\n\t * @type {any}\n\t */\n\t$$ = undefined;\n\t/**\n\t * ### PRIVATE API\n\t *\n\t * Do not use, may change at any time\n\t *\n\t * @type {any}\n\t */\n\t$$set = undefined;\n\n\t/** @returns {void} */\n\t$destroy() {\n\t\tdestroy_component(this, 1);\n\t\tthis.$destroy = noop;\n\t}\n\n\t/**\n\t * @template {Extract<keyof Events, string>} K\n\t * @param {K} type\n\t * @param {((e: Events[K]) => void) | null | undefined} callback\n\t * @returns {() => void}\n\t */\n\t$on(type, callback) {\n\t\tif (!is_function(callback)) {\n\t\t\treturn noop;\n\t\t}\n\t\tconst callbacks = this.$$.callbacks[type] || (this.$$.callbacks[type] = []);\n\t\tcallbacks.push(callback);\n\t\treturn () => {\n\t\t\tconst index = callbacks.indexOf(callback);\n\t\t\tif (index !== -1) callbacks.splice(index, 1);\n\t\t};\n\t}\n\n\t/**\n\t * @param {Partial<Props>} props\n\t * @returns {void}\n\t */\n\t$set(props) {\n\t\tif (this.$$set && !is_empty(props)) {\n\t\t\tthis.$$.skip_bound = true;\n\t\t\tthis.$$set(props);\n\t\t\tthis.$$.skip_bound = false;\n\t\t}\n\t}\n}\n\n/**\n * @typedef {Object} CustomElementPropDefinition\n * @property {string} [attribute]\n * @property {boolean} [reflect]\n * @property {'String'|'Boolean'|'Number'|'Array'|'Object'} [type]\n */\n"], "names": [], "mappings": ";;AAGO,MAAM,gBAAgB,GAAG,EAAE,CAAC;AAEvB,MAAC,iBAAiB,GAAG,GAAG;AACpC;AACA,IAAI,gBAAgB,GAAG,EAAE,CAAC;AAC1B;AACA,MAAM,eAAe,GAAG,EAAE,CAAC;AAC3B;AACA,MAAM,gBAAgB,mBAAmB,OAAO,CAAC,OAAO,EAAE,CAAC;AAC3D;AACA,IAAI,gBAAgB,GAAG,KAAK,CAAC;AAC7B;AACA;AACO,SAAS,eAAe,GAAG;AAClC,CAAC,IAAI,CAAC,gBAAgB,EAAE;AACxB,EAAE,gBAAgB,GAAG,IAAI,CAAC;AAC1B,EAAE,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE;AACF,CAAC;AACD;AACA;AACO,SAAS,IAAI,GAAG;AACvB,CAAC,eAAe,EAAE,CAAC;AACnB,CAAC,OAAO,gBAAgB,CAAC;AACzB,CAAC;AACD;AACA;AACO,SAAS,mBAAmB,CAAC,EAAE,EAAE;AACxC,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC;AAMD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;AACjC;AACA,IAAI,QAAQ,GAAG,CAAC,CAAC;AACjB;AACA;AACO,SAAS,KAAK,GAAG;AACxB;AACA;AACA;AACA,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE;AACrB,EAAE,OAAO;AACT,EAAE;AACF,CAAC,MAAM,eAAe,GAAG,iBAAiB,CAAC;AAC3C,CAAC,GAAG;AACJ;AACA;AACA,EAAE,IAAI;AACN,GAAG,OAAO,QAAQ,GAAG,gBAAgB,CAAC,MAAM,EAAE;AAC9C,IAAI,MAAM,SAAS,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACjD,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI,qBAAqB,CAAC,SAAS,CAAC,CAAC;AACrC,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACzB,IAAI;AACJ,GAAG,CAAC,OAAO,CAAC,EAAE;AACd;AACA,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/B,GAAG,QAAQ,GAAG,CAAC,CAAC;AAChB,GAAG,MAAM,CAAC,CAAC;AACX,GAAG;AACH,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;AAC9B,EAAE,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9B,EAAE,QAAQ,GAAG,CAAC,CAAC;AACf,EAAE,OAAO,iBAAiB,CAAC,MAAM,EAAE,iBAAiB,CAAC,GAAG,EAAE,EAAE,CAAC;AAC7D;AACA;AACA;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AACvD,GAAG,MAAM,QAAQ,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AACxC,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AACtC;AACA,IAAI,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACjC,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI;AACJ,GAAG;AACH,EAAE,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9B,EAAE,QAAQ,gBAAgB,CAAC,MAAM,EAAE;AACnC,CAAC,OAAO,eAAe,CAAC,MAAM,EAAE;AAChC,EAAE,eAAe,CAAC,GAAG,EAAE,EAAE,CAAC;AAC1B,EAAE;AACF,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAC1B,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;AACxB,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;AACxC,CAAC;AACD;AACA;AACA,SAAS,MAAM,CAAC,EAAE,EAAE;AACpB,CAAC,IAAI,EAAE,CAAC,QAAQ,KAAK,IAAI,EAAE;AAC3B,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC;AACd,EAAE,OAAO,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;AAC5B,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;AACzB,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClB,EAAE,EAAE,CAAC,QAAQ,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9C,EAAE,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAC/C,EAAE;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB,CAAC,GAAG,EAAE;AAC5C,CAAC,MAAM,QAAQ,GAAG,EAAE,CAAC;AACrB,CAAC,MAAM,OAAO,GAAG,EAAE,CAAC;AACpB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/F,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC7B,CAAC,gBAAgB,GAAG,QAAQ,CAAC;AAC7B;;ACjHA;AACO,SAAS,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE;AAChD,CAAC,MAAM,KAAK,GAAG,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC,IAAI,KAAK,KAAK,SAAS,EAAE;AAC1B,EAAE,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;AACvC,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;AACpC,EAAE;AACF,CAAC;AAiCD;AACA;AACO,SAAS,iBAAiB,CAAC,SAAS,EAAE,SAAS,EAAE;AACxD,CAAC,MAAM,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;AACzB,CAAC,IAAI,EAAE,CAAC,QAAQ,KAAK,IAAI,EAAE;AAC3B,EAAE,sBAAsB,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC;AACzB,EAAE,EAAE,CAAC,QAAQ,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;AAC1C;AACA;AACA,EAAE,EAAE,CAAC,UAAU,GAAG,EAAE,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrC,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC;AACd,EAAE;AACF,CAAC;AAwXD;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,eAAe,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,EAAE,GAAG,SAAS,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,KAAK,GAAG,SAAS,CAAC;AACnB;AACA;AACA,CAAC,QAAQ,GAAG;AACZ,EAAE,iBAAiB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACvB,EAAE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE;AACrB,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE;AAC9B,GAAG,OAAO,IAAI,CAAC;AACf,GAAG;AACH,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;AAC9E,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3B,EAAE,OAAO,MAAM;AACf,GAAG,MAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC7C,GAAG,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAChD,GAAG,CAAC;AACJ,EAAE;AACF;AACA;AACA;AACA;AACA;AACA,CAAC,IAAI,CAAC,KAAK,EAAE;AACb,EAAE,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACtC,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,IAAI,CAAC;AAC7B,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACrB,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,GAAG,KAAK,CAAC;AAC9B,GAAG;AACH,EAAE;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;;", "x_google_ignoreList": [0, 1]}