{"version": 3, "file": "module2-BuleNSx4.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/module2.js"], "sourcesContent": ["const createAddUniqueNumber = (generateUniqueNumber2) => {\n  return (set) => {\n    const number = generateUniqueNumber2(set);\n    set.add(number);\n    return number;\n  };\n};\nconst createCache = (lastNumberWeakMap) => {\n  return (collection, nextNumber) => {\n    lastNumberWeakMap.set(collection, nextNumber);\n    return nextNumber;\n  };\n};\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER === void 0 ? 9007199254740991 : Number.MAX_SAFE_INTEGER;\nconst TWO_TO_THE_POWER_OF_TWENTY_NINE = 536870912;\nconst TWO_TO_THE_POWER_OF_THIRTY = TWO_TO_THE_POWER_OF_TWENTY_NINE * 2;\nconst createGenerateUniqueNumber = (cache2, lastNumberWeakMap) => {\n  return (collection) => {\n    const lastNumber = lastNumberWeakMap.get(collection);\n    let nextNumber = lastNumber === void 0 ? collection.size : lastNumber < TWO_TO_THE_POWER_OF_THIRTY ? lastNumber + 1 : 0;\n    if (!collection.has(nextNumber)) {\n      return cache2(collection, nextNumber);\n    }\n    if (collection.size < TWO_TO_THE_POWER_OF_TWENTY_NINE) {\n      while (collection.has(nextNumber)) {\n        nextNumber = Math.floor(Math.random() * TWO_TO_THE_POWER_OF_THIRTY);\n      }\n      return cache2(collection, nextNumber);\n    }\n    if (collection.size > MAX_SAFE_INTEGER) {\n      throw new Error(\"Congratulations, you created a collection of unique numbers which uses all available integers!\");\n    }\n    while (collection.has(nextNumber)) {\n      nextNumber = Math.floor(Math.random() * MAX_SAFE_INTEGER);\n    }\n    return cache2(collection, nextNumber);\n  };\n};\nconst LAST_NUMBER_WEAK_MAP = /* @__PURE__ */ new WeakMap();\nconst cache = createCache(LAST_NUMBER_WEAK_MAP);\nconst generateUniqueNumber = createGenerateUniqueNumber(cache, LAST_NUMBER_WEAK_MAP);\nconst addUniqueNumber = createAddUniqueNumber(generateUniqueNumber);\nconst isMessagePort = (sender) => {\n  return typeof sender.start === \"function\";\n};\nconst PORT_MAP = /* @__PURE__ */ new WeakMap();\nconst extendBrokerImplementation = (partialBrokerImplementation) => ({\n  ...partialBrokerImplementation,\n  connect: ({ call }) => {\n    return async () => {\n      const { port1, port2 } = new MessageChannel();\n      const portId = await call(\"connect\", { port: port1 }, [port1]);\n      PORT_MAP.set(port2, portId);\n      return port2;\n    };\n  },\n  disconnect: ({ call }) => {\n    return async (port) => {\n      const portId = PORT_MAP.get(port);\n      if (portId === void 0) {\n        throw new Error(\"The given port is not connected.\");\n      }\n      await call(\"disconnect\", { portId });\n    };\n  },\n  isSupported: ({ call }) => {\n    return () => call(\"isSupported\");\n  }\n});\nconst ONGOING_REQUESTS = /* @__PURE__ */ new WeakMap();\nconst createOrGetOngoingRequests = (sender) => {\n  if (ONGOING_REQUESTS.has(sender)) {\n    return ONGOING_REQUESTS.get(sender);\n  }\n  const ongoingRequests = /* @__PURE__ */ new Map();\n  ONGOING_REQUESTS.set(sender, ongoingRequests);\n  return ongoingRequests;\n};\nconst createBroker = (brokerImplementation) => {\n  const fullBrokerImplementation = extendBrokerImplementation(brokerImplementation);\n  return (sender) => {\n    const ongoingRequests = createOrGetOngoingRequests(sender);\n    sender.addEventListener(\"message\", ({ data: message }) => {\n      const { id } = message;\n      if (id !== null && ongoingRequests.has(id)) {\n        const { reject, resolve } = ongoingRequests.get(id);\n        ongoingRequests.delete(id);\n        if (message.error === void 0) {\n          resolve(message.result);\n        } else {\n          reject(new Error(message.error.message));\n        }\n      }\n    });\n    if (isMessagePort(sender)) {\n      sender.start();\n    }\n    const call = (method, params = null, transferables = []) => {\n      return new Promise((resolve, reject) => {\n        const id = generateUniqueNumber(ongoingRequests);\n        ongoingRequests.set(id, { reject, resolve });\n        if (params === null) {\n          sender.postMessage({ id, method }, transferables);\n        } else {\n          sender.postMessage({ id, method, params }, transferables);\n        }\n      });\n    };\n    const notify = (method, params, transferables = []) => {\n      sender.postMessage({ id: null, method, params }, transferables);\n    };\n    let functions = {};\n    for (const [key, handler] of Object.entries(fullBrokerImplementation)) {\n      functions = { ...functions, [key]: handler({ call, notify }) };\n    }\n    return { ...functions };\n  };\n};\nexport {\n  addUniqueNumber as a,\n  createBroker as c,\n  generateUniqueNumber as g\n};\n"], "names": [], "mappings": "AAAA,MAAM,qBAAqB,GAAG,CAAC,qBAAqB,KAAK;AACzD,EAAE,OAAO,CAAC,GAAG,KAAK;AAClB,IAAI,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC;AAC9C,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACpB,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,WAAW,GAAG,CAAC,iBAAiB,KAAK;AAC3C,EAAE,OAAO,CAAC,UAAU,EAAE,UAAU,KAAK;AACrC,IAAI,iBAAiB,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAClD,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,KAAK,KAAK,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACzG,MAAM,+BAA+B,GAAG,SAAS,CAAC;AAClD,MAAM,0BAA0B,GAAG,+BAA+B,GAAG,CAAC,CAAC;AACvE,MAAM,0BAA0B,GAAG,CAAC,MAAM,EAAE,iBAAiB,KAAK;AAClE,EAAE,OAAO,CAAC,UAAU,KAAK;AACzB,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACzD,IAAI,IAAI,UAAU,GAAG,UAAU,KAAK,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,GAAG,0BAA0B,GAAG,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5H,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AACrC,MAAM,OAAO,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,IAAI,GAAG,+BAA+B,EAAE;AAC3D,MAAM,OAAO,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AACzC,QAAQ,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,0BAA0B,CAAC,CAAC;AAC5E,OAAO;AACP,MAAM,OAAO,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,IAAI,GAAG,gBAAgB,EAAE;AAC5C,MAAM,MAAM,IAAI,KAAK,CAAC,gGAAgG,CAAC,CAAC;AACxH,KAAK;AACL,IAAI,OAAO,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AACvC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,gBAAgB,CAAC,CAAC;AAChE,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC1C,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,oBAAoB,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC3D,MAAM,KAAK,GAAG,WAAW,CAAC,oBAAoB,CAAC,CAAC;AAC3C,MAAC,oBAAoB,GAAG,0BAA0B,CAAC,KAAK,EAAE,oBAAoB,EAAE;AAChF,MAAC,eAAe,GAAG,qBAAqB,CAAC,oBAAoB,EAAE;AACpE,MAAM,aAAa,GAAG,CAAC,MAAM,KAAK;AAClC,EAAE,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,UAAU,CAAC;AAC5C,CAAC,CAAC;AACF,MAAM,QAAQ,mBAAmB,IAAI,OAAO,EAAE,CAAC;AAC/C,MAAM,0BAA0B,GAAG,CAAC,2BAA2B,MAAM;AACrE,EAAE,GAAG,2BAA2B;AAChC,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK;AACzB,IAAI,OAAO,YAAY;AACvB,MAAM,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,IAAI,cAAc,EAAE,CAAC;AACpD,MAAM,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACrE,MAAM,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAClC,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,UAAU,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK;AAC5B,IAAI,OAAO,OAAO,IAAI,KAAK;AAC3B,MAAM,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACxC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE;AAC7B,QAAQ,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;AAC5D,OAAO;AACP,MAAM,MAAM,IAAI,CAAC,YAAY,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;AAC3C,KAAK,CAAC;AACN,GAAG;AACH,EAAE,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK;AAC7B,IAAI,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC;AACrC,GAAG;AACH,CAAC,CAAC,CAAC;AACH,MAAM,gBAAgB,mBAAmB,IAAI,OAAO,EAAE,CAAC;AACvD,MAAM,0BAA0B,GAAG,CAAC,MAAM,KAAK;AAC/C,EAAE,IAAI,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AACpC,IAAI,OAAO,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,MAAM,eAAe,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACpD,EAAE,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;AAChD,EAAE,OAAO,eAAe,CAAC;AACzB,CAAC,CAAC;AACG,MAAC,YAAY,GAAG,CAAC,oBAAoB,KAAK;AAC/C,EAAE,MAAM,wBAAwB,GAAG,0BAA0B,CAAC,oBAAoB,CAAC,CAAC;AACpF,EAAE,OAAO,CAAC,MAAM,KAAK;AACrB,IAAI,MAAM,eAAe,GAAG,0BAA0B,CAAC,MAAM,CAAC,CAAC;AAC/D,IAAI,MAAM,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK;AAC9D,MAAM,MAAM,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;AAC7B,MAAM,IAAI,EAAE,KAAK,IAAI,IAAI,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAClD,QAAQ,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC5D,QAAQ,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACnC,QAAQ,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE;AACtC,UAAU,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAClC,SAAS,MAAM;AACf,UAAU,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACnD,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE;AAC/B,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;AACrB,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,EAAE,aAAa,GAAG,EAAE,KAAK;AAChE,MAAM,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AAC9C,QAAQ,MAAM,EAAE,GAAG,oBAAoB,CAAC,eAAe,CAAC,CAAC;AACzD,QAAQ,eAAe,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;AACrD,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE;AAC7B,UAAU,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,aAAa,CAAC,CAAC;AAC5D,SAAS,MAAM;AACf,UAAU,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,aAAa,CAAC,CAAC;AACpE,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,GAAG,EAAE,KAAK;AAC3D,MAAM,MAAM,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,aAAa,CAAC,CAAC;AACtE,KAAK,CAAC;AACN,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACvB,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC,EAAE;AAC3E,MAAM,SAAS,GAAG,EAAE,GAAG,SAAS,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AACrE,KAAK;AACL,IAAI,OAAO,EAAE,GAAG,SAAS,EAAE,CAAC;AAC5B,GAAG,CAAC;AACJ;;;;"}