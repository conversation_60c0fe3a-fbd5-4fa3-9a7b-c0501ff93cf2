{"version": 3, "file": "index34-Clri6PTR.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/index34.js"], "sourcesContent": ["import { a as NodeType, b as NodeProp, s as styleTags, t as tags, N as NodeSet, P as Parser, m as Tag, T as Tree, p as parseMixed, d as syntaxTree, E as EditorSelection, n as countColumn, h as LanguageSupport, o as Prec, q as keymap, C as CompletionContext, r as EditorState, u as Language, v as indentUnit, w as defineLanguageFacet, f as foldNodeProp, e as indentNodeProp, x as languageDataProp, y as foldService, z as LanguageDescription, A as ParseContext } from \"./Index16.js\";\nimport { htmlCompletionSource, html } from \"./index35.js\";\nclass CompositeBlock {\n  static create(type, value, from, parentHash, end) {\n    let hash = parentHash + (parentHash << 8) + type + (value << 4) | 0;\n    return new CompositeBlock(type, value, from, hash, end, [], []);\n  }\n  constructor(type, value, from, hash, end, children, positions) {\n    this.type = type;\n    this.value = value;\n    this.from = from;\n    this.hash = hash;\n    this.end = end;\n    this.children = children;\n    this.positions = positions;\n    this.hashProp = [[NodeProp.contextHash, hash]];\n  }\n  addChild(child, pos) {\n    if (child.prop(NodeProp.contextHash) != this.hash)\n      child = new Tree(child.type, child.children, child.positions, child.length, this.hashProp);\n    this.children.push(child);\n    this.positions.push(pos);\n  }\n  toTree(nodeSet, end = this.end) {\n    let last = this.children.length - 1;\n    if (last >= 0)\n      end = Math.max(end, this.positions[last] + this.children[last].length + this.from);\n    return new Tree(nodeSet.types[this.type], this.children, this.positions, end - this.from).balance({\n      makeTree: (children, positions, length) => new Tree(NodeType.none, children, positions, length, this.hashProp)\n    });\n  }\n}\nvar Type;\n(function(Type2) {\n  Type2[Type2[\"Document\"] = 1] = \"Document\";\n  Type2[Type2[\"CodeBlock\"] = 2] = \"CodeBlock\";\n  Type2[Type2[\"FencedCode\"] = 3] = \"FencedCode\";\n  Type2[Type2[\"Blockquote\"] = 4] = \"Blockquote\";\n  Type2[Type2[\"HorizontalRule\"] = 5] = \"HorizontalRule\";\n  Type2[Type2[\"BulletList\"] = 6] = \"BulletList\";\n  Type2[Type2[\"OrderedList\"] = 7] = \"OrderedList\";\n  Type2[Type2[\"ListItem\"] = 8] = \"ListItem\";\n  Type2[Type2[\"ATXHeading1\"] = 9] = \"ATXHeading1\";\n  Type2[Type2[\"ATXHeading2\"] = 10] = \"ATXHeading2\";\n  Type2[Type2[\"ATXHeading3\"] = 11] = \"ATXHeading3\";\n  Type2[Type2[\"ATXHeading4\"] = 12] = \"ATXHeading4\";\n  Type2[Type2[\"ATXHeading5\"] = 13] = \"ATXHeading5\";\n  Type2[Type2[\"ATXHeading6\"] = 14] = \"ATXHeading6\";\n  Type2[Type2[\"SetextHeading1\"] = 15] = \"SetextHeading1\";\n  Type2[Type2[\"SetextHeading2\"] = 16] = \"SetextHeading2\";\n  Type2[Type2[\"HTMLBlock\"] = 17] = \"HTMLBlock\";\n  Type2[Type2[\"LinkReference\"] = 18] = \"LinkReference\";\n  Type2[Type2[\"Paragraph\"] = 19] = \"Paragraph\";\n  Type2[Type2[\"CommentBlock\"] = 20] = \"CommentBlock\";\n  Type2[Type2[\"ProcessingInstructionBlock\"] = 21] = \"ProcessingInstructionBlock\";\n  Type2[Type2[\"Escape\"] = 22] = \"Escape\";\n  Type2[Type2[\"Entity\"] = 23] = \"Entity\";\n  Type2[Type2[\"HardBreak\"] = 24] = \"HardBreak\";\n  Type2[Type2[\"Emphasis\"] = 25] = \"Emphasis\";\n  Type2[Type2[\"StrongEmphasis\"] = 26] = \"StrongEmphasis\";\n  Type2[Type2[\"Link\"] = 27] = \"Link\";\n  Type2[Type2[\"Image\"] = 28] = \"Image\";\n  Type2[Type2[\"InlineCode\"] = 29] = \"InlineCode\";\n  Type2[Type2[\"HTMLTag\"] = 30] = \"HTMLTag\";\n  Type2[Type2[\"Comment\"] = 31] = \"Comment\";\n  Type2[Type2[\"ProcessingInstruction\"] = 32] = \"ProcessingInstruction\";\n  Type2[Type2[\"Autolink\"] = 33] = \"Autolink\";\n  Type2[Type2[\"HeaderMark\"] = 34] = \"HeaderMark\";\n  Type2[Type2[\"QuoteMark\"] = 35] = \"QuoteMark\";\n  Type2[Type2[\"ListMark\"] = 36] = \"ListMark\";\n  Type2[Type2[\"LinkMark\"] = 37] = \"LinkMark\";\n  Type2[Type2[\"EmphasisMark\"] = 38] = \"EmphasisMark\";\n  Type2[Type2[\"CodeMark\"] = 39] = \"CodeMark\";\n  Type2[Type2[\"CodeText\"] = 40] = \"CodeText\";\n  Type2[Type2[\"CodeInfo\"] = 41] = \"CodeInfo\";\n  Type2[Type2[\"LinkTitle\"] = 42] = \"LinkTitle\";\n  Type2[Type2[\"LinkLabel\"] = 43] = \"LinkLabel\";\n  Type2[Type2[\"URL\"] = 44] = \"URL\";\n})(Type || (Type = {}));\nclass LeafBlock {\n  /**\n  @internal\n  */\n  constructor(start, content) {\n    this.start = start;\n    this.content = content;\n    this.marks = [];\n    this.parsers = [];\n  }\n}\nclass Line {\n  constructor() {\n    this.text = \"\";\n    this.baseIndent = 0;\n    this.basePos = 0;\n    this.depth = 0;\n    this.markers = [];\n    this.pos = 0;\n    this.indent = 0;\n    this.next = -1;\n  }\n  /**\n  @internal\n  */\n  forward() {\n    if (this.basePos > this.pos)\n      this.forwardInner();\n  }\n  /**\n  @internal\n  */\n  forwardInner() {\n    let newPos = this.skipSpace(this.basePos);\n    this.indent = this.countIndent(newPos, this.pos, this.indent);\n    this.pos = newPos;\n    this.next = newPos == this.text.length ? -1 : this.text.charCodeAt(newPos);\n  }\n  /**\n  Skip whitespace after the given position, return the position of\n  the next non-space character or the end of the line if there's\n  only space after `from`.\n  */\n  skipSpace(from) {\n    return skipSpace(this.text, from);\n  }\n  /**\n  @internal\n  */\n  reset(text) {\n    this.text = text;\n    this.baseIndent = this.basePos = this.pos = this.indent = 0;\n    this.forwardInner();\n    this.depth = 1;\n    while (this.markers.length)\n      this.markers.pop();\n  }\n  /**\n  Move the line's base position forward to the given position.\n  This should only be called by composite [block\n  parsers](#BlockParser.parse) or [markup skipping\n  functions](#NodeSpec.composite).\n  */\n  moveBase(to) {\n    this.basePos = to;\n    this.baseIndent = this.countIndent(to, this.pos, this.indent);\n  }\n  /**\n  Move the line's base position forward to the given _column_.\n  */\n  moveBaseColumn(indent) {\n    this.baseIndent = indent;\n    this.basePos = this.findColumn(indent);\n  }\n  /**\n  Store a composite-block-level marker. Should be called from\n  [markup skipping functions](#NodeSpec.composite) when they\n  consume any non-whitespace characters.\n  */\n  addMarker(elt2) {\n    this.markers.push(elt2);\n  }\n  /**\n  Find the column position at `to`, optionally starting at a given\n  position and column.\n  */\n  countIndent(to, from = 0, indent = 0) {\n    for (let i = from; i < to; i++)\n      indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n    return indent;\n  }\n  /**\n  Find the position corresponding to the given column.\n  */\n  findColumn(goal) {\n    let i = 0;\n    for (let indent = 0; i < this.text.length && indent < goal; i++)\n      indent += this.text.charCodeAt(i) == 9 ? 4 - indent % 4 : 1;\n    return i;\n  }\n  /**\n  @internal\n  */\n  scrub() {\n    if (!this.baseIndent)\n      return this.text;\n    let result = \"\";\n    for (let i = 0; i < this.basePos; i++)\n      result += \" \";\n    return result + this.text.slice(this.basePos);\n  }\n}\nfunction skipForList(bl, cx, line) {\n  if (line.pos == line.text.length || bl != cx.block && line.indent >= cx.stack[line.depth + 1].value + line.baseIndent)\n    return true;\n  if (line.indent >= line.baseIndent + 4)\n    return false;\n  let size = (bl.type == Type.OrderedList ? isOrderedList : isBulletList)(line, cx, false);\n  return size > 0 && (bl.type != Type.BulletList || isHorizontalRule(line, cx, false) < 0) && line.text.charCodeAt(line.pos + size - 1) == bl.value;\n}\nconst DefaultSkipMarkup = {\n  [Type.Blockquote](bl, cx, line) {\n    if (line.next != 62)\n      return false;\n    line.markers.push(elt(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1));\n    line.moveBase(line.pos + (space(line.text.charCodeAt(line.pos + 1)) ? 2 : 1));\n    bl.end = cx.lineStart + line.text.length;\n    return true;\n  },\n  [Type.ListItem](bl, _cx, line) {\n    if (line.indent < line.baseIndent + bl.value && line.next > -1)\n      return false;\n    line.moveBaseColumn(line.baseIndent + bl.value);\n    return true;\n  },\n  [Type.OrderedList]: skipForList,\n  [Type.BulletList]: skipForList,\n  [Type.Document]() {\n    return true;\n  }\n};\nfunction space(ch) {\n  return ch == 32 || ch == 9 || ch == 10 || ch == 13;\n}\nfunction skipSpace(line, i = 0) {\n  while (i < line.length && space(line.charCodeAt(i)))\n    i++;\n  return i;\n}\nfunction skipSpaceBack(line, i, to) {\n  while (i > to && space(line.charCodeAt(i - 1)))\n    i--;\n  return i;\n}\nfunction isFencedCode(line) {\n  if (line.next != 96 && line.next != 126)\n    return -1;\n  let pos = line.pos + 1;\n  while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n    pos++;\n  if (pos < line.pos + 3)\n    return -1;\n  if (line.next == 96) {\n    for (let i = pos; i < line.text.length; i++)\n      if (line.text.charCodeAt(i) == 96)\n        return -1;\n  }\n  return pos;\n}\nfunction isBlockquote(line) {\n  return line.next != 62 ? -1 : line.text.charCodeAt(line.pos + 1) == 32 ? 2 : 1;\n}\nfunction isHorizontalRule(line, cx, breaking) {\n  if (line.next != 42 && line.next != 45 && line.next != 95)\n    return -1;\n  let count2 = 1;\n  for (let pos = line.pos + 1; pos < line.text.length; pos++) {\n    let ch = line.text.charCodeAt(pos);\n    if (ch == line.next)\n      count2++;\n    else if (!space(ch))\n      return -1;\n  }\n  if (breaking && line.next == 45 && isSetextUnderline(line) > -1 && line.depth == cx.stack.length && cx.parser.leafBlockParsers.indexOf(DefaultLeafBlocks.SetextHeading) > -1)\n    return -1;\n  return count2 < 3 ? -1 : 1;\n}\nfunction inList(cx, type) {\n  for (let i = cx.stack.length - 1; i >= 0; i--)\n    if (cx.stack[i].type == type)\n      return true;\n  return false;\n}\nfunction isBulletList(line, cx, breaking) {\n  return (line.next == 45 || line.next == 43 || line.next == 42) && (line.pos == line.text.length - 1 || space(line.text.charCodeAt(line.pos + 1))) && (!breaking || inList(cx, Type.BulletList) || line.skipSpace(line.pos + 2) < line.text.length) ? 1 : -1;\n}\nfunction isOrderedList(line, cx, breaking) {\n  let pos = line.pos, next = line.next;\n  for (; ; ) {\n    if (next >= 48 && next <= 57)\n      pos++;\n    else\n      break;\n    if (pos == line.text.length)\n      return -1;\n    next = line.text.charCodeAt(pos);\n  }\n  if (pos == line.pos || pos > line.pos + 9 || next != 46 && next != 41 || pos < line.text.length - 1 && !space(line.text.charCodeAt(pos + 1)) || breaking && !inList(cx, Type.OrderedList) && (line.skipSpace(pos + 1) == line.text.length || pos > line.pos + 1 || line.next != 49))\n    return -1;\n  return pos + 1 - line.pos;\n}\nfunction isAtxHeading(line) {\n  if (line.next != 35)\n    return -1;\n  let pos = line.pos + 1;\n  while (pos < line.text.length && line.text.charCodeAt(pos) == 35)\n    pos++;\n  if (pos < line.text.length && line.text.charCodeAt(pos) != 32)\n    return -1;\n  let size = pos - line.pos;\n  return size > 6 ? -1 : size;\n}\nfunction isSetextUnderline(line) {\n  if (line.next != 45 && line.next != 61 || line.indent >= line.baseIndent + 4)\n    return -1;\n  let pos = line.pos + 1;\n  while (pos < line.text.length && line.text.charCodeAt(pos) == line.next)\n    pos++;\n  let end = pos;\n  while (pos < line.text.length && space(line.text.charCodeAt(pos)))\n    pos++;\n  return pos == line.text.length ? end : -1;\n}\nconst EmptyLine = /^[ \\t]*$/, CommentEnd = /-->/, ProcessingEnd = /\\?>/;\nconst HTMLBlockStyle = [\n  [/^<(?:script|pre|style)(?:\\s|>|$)/i, /<\\/(?:script|pre|style)>/i],\n  [/^\\s*<!--/, CommentEnd],\n  [/^\\s*<\\?/, ProcessingEnd],\n  [/^\\s*<![A-Z]/, />/],\n  [/^\\s*<!\\[CDATA\\[/, /\\]\\]>/],\n  [/^\\s*<\\/?(?:address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h1|h2|h3|h4|h5|h6|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul)(?:\\s|\\/?>|$)/i, EmptyLine],\n  [/^\\s*(?:<\\/[a-z][\\w-]*\\s*>|<[a-z][\\w-]*(\\s+[a-z:_][\\w-.]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*>)\\s*$/i, EmptyLine]\n];\nfunction isHTMLBlock(line, _cx, breaking) {\n  if (line.next != 60)\n    return -1;\n  let rest = line.text.slice(line.pos);\n  for (let i = 0, e = HTMLBlockStyle.length - (breaking ? 1 : 0); i < e; i++)\n    if (HTMLBlockStyle[i][0].test(rest))\n      return i;\n  return -1;\n}\nfunction getListIndent(line, pos) {\n  let indentAfter = line.countIndent(pos, line.pos, line.indent);\n  let indented = line.countIndent(line.skipSpace(pos), pos, indentAfter);\n  return indented >= indentAfter + 5 ? indentAfter + 1 : indented;\n}\nfunction addCodeText(marks, from, to) {\n  let last = marks.length - 1;\n  if (last >= 0 && marks[last].to == from && marks[last].type == Type.CodeText)\n    marks[last].to = to;\n  else\n    marks.push(elt(Type.CodeText, from, to));\n}\nconst DefaultBlockParsers = {\n  LinkReference: void 0,\n  IndentedCode(cx, line) {\n    let base = line.baseIndent + 4;\n    if (line.indent < base)\n      return false;\n    let start = line.findColumn(base);\n    let from = cx.lineStart + start, to = cx.lineStart + line.text.length;\n    let marks = [], pendingMarks = [];\n    addCodeText(marks, from, to);\n    while (cx.nextLine() && line.depth >= cx.stack.length) {\n      if (line.pos == line.text.length) {\n        addCodeText(pendingMarks, cx.lineStart - 1, cx.lineStart);\n        for (let m of line.markers)\n          pendingMarks.push(m);\n      } else if (line.indent < base) {\n        break;\n      } else {\n        if (pendingMarks.length) {\n          for (let m of pendingMarks) {\n            if (m.type == Type.CodeText)\n              addCodeText(marks, m.from, m.to);\n            else\n              marks.push(m);\n          }\n          pendingMarks = [];\n        }\n        addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n        for (let m of line.markers)\n          marks.push(m);\n        to = cx.lineStart + line.text.length;\n        let codeStart = cx.lineStart + line.findColumn(line.baseIndent + 4);\n        if (codeStart < to)\n          addCodeText(marks, codeStart, to);\n      }\n    }\n    if (pendingMarks.length) {\n      pendingMarks = pendingMarks.filter((m) => m.type != Type.CodeText);\n      if (pendingMarks.length)\n        line.markers = pendingMarks.concat(line.markers);\n    }\n    cx.addNode(cx.buffer.writeElements(marks, -from).finish(Type.CodeBlock, to - from), from);\n    return true;\n  },\n  FencedCode(cx, line) {\n    let fenceEnd = isFencedCode(line);\n    if (fenceEnd < 0)\n      return false;\n    let from = cx.lineStart + line.pos, ch = line.next, len = fenceEnd - line.pos;\n    let infoFrom = line.skipSpace(fenceEnd), infoTo = skipSpaceBack(line.text, line.text.length, infoFrom);\n    let marks = [elt(Type.CodeMark, from, from + len)];\n    if (infoFrom < infoTo)\n      marks.push(elt(Type.CodeInfo, cx.lineStart + infoFrom, cx.lineStart + infoTo));\n    for (let first = true; cx.nextLine() && line.depth >= cx.stack.length; first = false) {\n      let i = line.pos;\n      if (line.indent - line.baseIndent < 4)\n        while (i < line.text.length && line.text.charCodeAt(i) == ch)\n          i++;\n      if (i - line.pos >= len && line.skipSpace(i) == line.text.length) {\n        for (let m of line.markers)\n          marks.push(m);\n        marks.push(elt(Type.CodeMark, cx.lineStart + line.pos, cx.lineStart + i));\n        cx.nextLine();\n        break;\n      } else {\n        if (!first)\n          addCodeText(marks, cx.lineStart - 1, cx.lineStart);\n        for (let m of line.markers)\n          marks.push(m);\n        let textStart = cx.lineStart + line.basePos, textEnd = cx.lineStart + line.text.length;\n        if (textStart < textEnd)\n          addCodeText(marks, textStart, textEnd);\n      }\n    }\n    cx.addNode(cx.buffer.writeElements(marks, -from).finish(Type.FencedCode, cx.prevLineEnd() - from), from);\n    return true;\n  },\n  Blockquote(cx, line) {\n    let size = isBlockquote(line);\n    if (size < 0)\n      return false;\n    cx.startContext(Type.Blockquote, line.pos);\n    cx.addNode(Type.QuoteMark, cx.lineStart + line.pos, cx.lineStart + line.pos + 1);\n    line.moveBase(line.pos + size);\n    return null;\n  },\n  HorizontalRule(cx, line) {\n    if (isHorizontalRule(line, cx, false) < 0)\n      return false;\n    let from = cx.lineStart + line.pos;\n    cx.nextLine();\n    cx.addNode(Type.HorizontalRule, from);\n    return true;\n  },\n  BulletList(cx, line) {\n    let size = isBulletList(line, cx, false);\n    if (size < 0)\n      return false;\n    if (cx.block.type != Type.BulletList)\n      cx.startContext(Type.BulletList, line.basePos, line.next);\n    let newBase = getListIndent(line, line.pos + 1);\n    cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n    cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n    line.moveBaseColumn(newBase);\n    return null;\n  },\n  OrderedList(cx, line) {\n    let size = isOrderedList(line, cx, false);\n    if (size < 0)\n      return false;\n    if (cx.block.type != Type.OrderedList)\n      cx.startContext(Type.OrderedList, line.basePos, line.text.charCodeAt(line.pos + size - 1));\n    let newBase = getListIndent(line, line.pos + size);\n    cx.startContext(Type.ListItem, line.basePos, newBase - line.baseIndent);\n    cx.addNode(Type.ListMark, cx.lineStart + line.pos, cx.lineStart + line.pos + size);\n    line.moveBaseColumn(newBase);\n    return null;\n  },\n  ATXHeading(cx, line) {\n    let size = isAtxHeading(line);\n    if (size < 0)\n      return false;\n    let off = line.pos, from = cx.lineStart + off;\n    let endOfSpace = skipSpaceBack(line.text, line.text.length, off), after = endOfSpace;\n    while (after > off && line.text.charCodeAt(after - 1) == line.next)\n      after--;\n    if (after == endOfSpace || after == off || !space(line.text.charCodeAt(after - 1)))\n      after = line.text.length;\n    let buf = cx.buffer.write(Type.HeaderMark, 0, size).writeElements(cx.parser.parseInline(line.text.slice(off + size + 1, after), from + size + 1), -from);\n    if (after < line.text.length)\n      buf.write(Type.HeaderMark, after - off, endOfSpace - off);\n    let node = buf.finish(Type.ATXHeading1 - 1 + size, line.text.length - off);\n    cx.nextLine();\n    cx.addNode(node, from);\n    return true;\n  },\n  HTMLBlock(cx, line) {\n    let type = isHTMLBlock(line, cx, false);\n    if (type < 0)\n      return false;\n    let from = cx.lineStart + line.pos, end = HTMLBlockStyle[type][1];\n    let marks = [], trailing = end != EmptyLine;\n    while (!end.test(line.text) && cx.nextLine()) {\n      if (line.depth < cx.stack.length) {\n        trailing = false;\n        break;\n      }\n      for (let m of line.markers)\n        marks.push(m);\n    }\n    if (trailing)\n      cx.nextLine();\n    let nodeType = end == CommentEnd ? Type.CommentBlock : end == ProcessingEnd ? Type.ProcessingInstructionBlock : Type.HTMLBlock;\n    let to = cx.prevLineEnd();\n    cx.addNode(cx.buffer.writeElements(marks, -from).finish(nodeType, to - from), from);\n    return true;\n  },\n  SetextHeading: void 0\n  // Specifies relative precedence for block-continue function\n};\nclass LinkReferenceParser {\n  constructor(leaf) {\n    this.stage = 0;\n    this.elts = [];\n    this.pos = 0;\n    this.start = leaf.start;\n    this.advance(leaf.content);\n  }\n  nextLine(cx, line, leaf) {\n    if (this.stage == -1)\n      return false;\n    let content = leaf.content + \"\\n\" + line.scrub();\n    let finish = this.advance(content);\n    if (finish > -1 && finish < content.length)\n      return this.complete(cx, leaf, finish);\n    return false;\n  }\n  finish(cx, leaf) {\n    if ((this.stage == 2 || this.stage == 3) && skipSpace(leaf.content, this.pos) == leaf.content.length)\n      return this.complete(cx, leaf, leaf.content.length);\n    return false;\n  }\n  complete(cx, leaf, len) {\n    cx.addLeafElement(leaf, elt(Type.LinkReference, this.start, this.start + len, this.elts));\n    return true;\n  }\n  nextStage(elt2) {\n    if (elt2) {\n      this.pos = elt2.to - this.start;\n      this.elts.push(elt2);\n      this.stage++;\n      return true;\n    }\n    if (elt2 === false)\n      this.stage = -1;\n    return false;\n  }\n  advance(content) {\n    for (; ; ) {\n      if (this.stage == -1) {\n        return -1;\n      } else if (this.stage == 0) {\n        if (!this.nextStage(parseLinkLabel(content, this.pos, this.start, true)))\n          return -1;\n        if (content.charCodeAt(this.pos) != 58)\n          return this.stage = -1;\n        this.elts.push(elt(Type.LinkMark, this.pos + this.start, this.pos + this.start + 1));\n        this.pos++;\n      } else if (this.stage == 1) {\n        if (!this.nextStage(parseURL(content, skipSpace(content, this.pos), this.start)))\n          return -1;\n      } else if (this.stage == 2) {\n        let skip = skipSpace(content, this.pos), end = 0;\n        if (skip > this.pos) {\n          let title = parseLinkTitle(content, skip, this.start);\n          if (title) {\n            let titleEnd = lineEnd(content, title.to - this.start);\n            if (titleEnd > 0) {\n              this.nextStage(title);\n              end = titleEnd;\n            }\n          }\n        }\n        if (!end)\n          end = lineEnd(content, this.pos);\n        return end > 0 && end < content.length ? end : -1;\n      } else {\n        return lineEnd(content, this.pos);\n      }\n    }\n  }\n}\nfunction lineEnd(text, pos) {\n  for (; pos < text.length; pos++) {\n    let next = text.charCodeAt(pos);\n    if (next == 10)\n      break;\n    if (!space(next))\n      return -1;\n  }\n  return pos;\n}\nclass SetextHeadingParser {\n  nextLine(cx, line, leaf) {\n    let underline = line.depth < cx.stack.length ? -1 : isSetextUnderline(line);\n    let next = line.next;\n    if (underline < 0)\n      return false;\n    let underlineMark = elt(Type.HeaderMark, cx.lineStart + line.pos, cx.lineStart + underline);\n    cx.nextLine();\n    cx.addLeafElement(leaf, elt(next == 61 ? Type.SetextHeading1 : Type.SetextHeading2, leaf.start, cx.prevLineEnd(), [\n      ...cx.parser.parseInline(leaf.content, leaf.start),\n      underlineMark\n    ]));\n    return true;\n  }\n  finish() {\n    return false;\n  }\n}\nconst DefaultLeafBlocks = {\n  LinkReference(_, leaf) {\n    return leaf.content.charCodeAt(0) == 91 ? new LinkReferenceParser(leaf) : null;\n  },\n  SetextHeading() {\n    return new SetextHeadingParser();\n  }\n};\nconst DefaultEndLeaf = [\n  (_, line) => isAtxHeading(line) >= 0,\n  (_, line) => isFencedCode(line) >= 0,\n  (_, line) => isBlockquote(line) >= 0,\n  (p, line) => isBulletList(line, p, true) >= 0,\n  (p, line) => isOrderedList(line, p, true) >= 0,\n  (p, line) => isHorizontalRule(line, p, true) >= 0,\n  (p, line) => isHTMLBlock(line, p, true) >= 0\n];\nconst scanLineResult = { text: \"\", end: 0 };\nclass BlockContext {\n  /**\n  @internal\n  */\n  constructor(parser2, input, fragments, ranges) {\n    this.parser = parser2;\n    this.input = input;\n    this.ranges = ranges;\n    this.line = new Line();\n    this.atEnd = false;\n    this.reusePlaceholders = /* @__PURE__ */ new Map();\n    this.stoppedAt = null;\n    this.rangeI = 0;\n    this.to = ranges[ranges.length - 1].to;\n    this.lineStart = this.absoluteLineStart = this.absoluteLineEnd = ranges[0].from;\n    this.block = CompositeBlock.create(Type.Document, 0, this.lineStart, 0, 0);\n    this.stack = [this.block];\n    this.fragments = fragments.length ? new FragmentCursor(fragments, input) : null;\n    this.readLine();\n  }\n  get parsedPos() {\n    return this.absoluteLineStart;\n  }\n  advance() {\n    if (this.stoppedAt != null && this.absoluteLineStart > this.stoppedAt)\n      return this.finish();\n    let { line } = this;\n    for (; ; ) {\n      for (let markI = 0; ; ) {\n        let next = line.depth < this.stack.length ? this.stack[this.stack.length - 1] : null;\n        while (markI < line.markers.length && (!next || line.markers[markI].from < next.end)) {\n          let mark = line.markers[markI++];\n          this.addNode(mark.type, mark.from, mark.to);\n        }\n        if (!next)\n          break;\n        this.finishContext();\n      }\n      if (line.pos < line.text.length)\n        break;\n      if (!this.nextLine())\n        return this.finish();\n    }\n    if (this.fragments && this.reuseFragment(line.basePos))\n      return null;\n    start:\n      for (; ; ) {\n        for (let type of this.parser.blockParsers)\n          if (type) {\n            let result = type(this, line);\n            if (result != false) {\n              if (result == true)\n                return null;\n              line.forward();\n              continue start;\n            }\n          }\n        break;\n      }\n    let leaf = new LeafBlock(this.lineStart + line.pos, line.text.slice(line.pos));\n    for (let parse of this.parser.leafBlockParsers)\n      if (parse) {\n        let parser2 = parse(this, leaf);\n        if (parser2)\n          leaf.parsers.push(parser2);\n      }\n    lines:\n      while (this.nextLine()) {\n        if (line.pos == line.text.length)\n          break;\n        if (line.indent < line.baseIndent + 4) {\n          for (let stop of this.parser.endLeafBlock)\n            if (stop(this, line, leaf))\n              break lines;\n        }\n        for (let parser2 of leaf.parsers)\n          if (parser2.nextLine(this, line, leaf))\n            return null;\n        leaf.content += \"\\n\" + line.scrub();\n        for (let m of line.markers)\n          leaf.marks.push(m);\n      }\n    this.finishLeaf(leaf);\n    return null;\n  }\n  stopAt(pos) {\n    if (this.stoppedAt != null && this.stoppedAt < pos)\n      throw new RangeError(\"Can't move stoppedAt forward\");\n    this.stoppedAt = pos;\n  }\n  reuseFragment(start) {\n    if (!this.fragments.moveTo(this.absoluteLineStart + start, this.absoluteLineStart) || !this.fragments.matches(this.block.hash))\n      return false;\n    let taken = this.fragments.takeNodes(this);\n    if (!taken)\n      return false;\n    this.absoluteLineStart += taken;\n    this.lineStart = toRelative(this.absoluteLineStart, this.ranges);\n    this.moveRangeI();\n    if (this.absoluteLineStart < this.to) {\n      this.lineStart++;\n      this.absoluteLineStart++;\n      this.readLine();\n    } else {\n      this.atEnd = true;\n      this.readLine();\n    }\n    return true;\n  }\n  /**\n  The number of parent blocks surrounding the current block.\n  */\n  get depth() {\n    return this.stack.length;\n  }\n  /**\n  Get the type of the parent block at the given depth. When no\n  depth is passed, return the type of the innermost parent.\n  */\n  parentType(depth = this.depth - 1) {\n    return this.parser.nodeSet.types[this.stack[depth].type];\n  }\n  /**\n  Move to the next input line. This should only be called by\n  (non-composite) [block parsers](#BlockParser.parse) that consume\n  the line directly, or leaf block parser\n  [`nextLine`](#LeafBlockParser.nextLine) methods when they\n  consume the current line (and return true).\n  */\n  nextLine() {\n    this.lineStart += this.line.text.length;\n    if (this.absoluteLineEnd >= this.to) {\n      this.absoluteLineStart = this.absoluteLineEnd;\n      this.atEnd = true;\n      this.readLine();\n      return false;\n    } else {\n      this.lineStart++;\n      this.absoluteLineStart = this.absoluteLineEnd + 1;\n      this.moveRangeI();\n      this.readLine();\n      return true;\n    }\n  }\n  /**\n  Retrieve the text of the line after the current one, without\n  actually moving the context's current line forward.\n  */\n  peekLine() {\n    return this.scanLine(this.absoluteLineEnd + 1).text;\n  }\n  moveRangeI() {\n    while (this.rangeI < this.ranges.length - 1 && this.absoluteLineStart >= this.ranges[this.rangeI].to) {\n      this.rangeI++;\n      this.absoluteLineStart = Math.max(this.absoluteLineStart, this.ranges[this.rangeI].from);\n    }\n  }\n  /**\n  @internal\n  Collect the text for the next line.\n  */\n  scanLine(start) {\n    let r = scanLineResult;\n    r.end = start;\n    if (start >= this.to) {\n      r.text = \"\";\n    } else {\n      r.text = this.lineChunkAt(start);\n      r.end += r.text.length;\n      if (this.ranges.length > 1) {\n        let textOffset = this.absoluteLineStart, rangeI = this.rangeI;\n        while (this.ranges[rangeI].to < r.end) {\n          rangeI++;\n          let nextFrom = this.ranges[rangeI].from;\n          let after = this.lineChunkAt(nextFrom);\n          r.end = nextFrom + after.length;\n          r.text = r.text.slice(0, this.ranges[rangeI - 1].to - textOffset) + after;\n          textOffset = r.end - r.text.length;\n        }\n      }\n    }\n    return r;\n  }\n  /**\n  @internal\n  Populate this.line with the content of the next line. Skip\n  leading characters covered by composite blocks.\n  */\n  readLine() {\n    let { line } = this, { text, end } = this.scanLine(this.absoluteLineStart);\n    this.absoluteLineEnd = end;\n    line.reset(text);\n    for (; line.depth < this.stack.length; line.depth++) {\n      let cx = this.stack[line.depth], handler = this.parser.skipContextMarkup[cx.type];\n      if (!handler)\n        throw new Error(\"Unhandled block context \" + Type[cx.type]);\n      if (!handler(cx, this, line))\n        break;\n      line.forward();\n    }\n  }\n  lineChunkAt(pos) {\n    let next = this.input.chunk(pos), text;\n    if (!this.input.lineChunks) {\n      let eol = next.indexOf(\"\\n\");\n      text = eol < 0 ? next : next.slice(0, eol);\n    } else {\n      text = next == \"\\n\" ? \"\" : next;\n    }\n    return pos + text.length > this.to ? text.slice(0, this.to - pos) : text;\n  }\n  /**\n  The end position of the previous line.\n  */\n  prevLineEnd() {\n    return this.atEnd ? this.lineStart : this.lineStart - 1;\n  }\n  /**\n  @internal\n  */\n  startContext(type, start, value = 0) {\n    this.block = CompositeBlock.create(type, value, this.lineStart + start, this.block.hash, this.lineStart + this.line.text.length);\n    this.stack.push(this.block);\n  }\n  /**\n  Start a composite block. Should only be called from [block\n  parser functions](#BlockParser.parse) that return null.\n  */\n  startComposite(type, start, value = 0) {\n    this.startContext(this.parser.getNodeType(type), start, value);\n  }\n  /**\n  @internal\n  */\n  addNode(block, from, to) {\n    if (typeof block == \"number\")\n      block = new Tree(this.parser.nodeSet.types[block], none, none, (to !== null && to !== void 0 ? to : this.prevLineEnd()) - from);\n    this.block.addChild(block, from - this.block.from);\n  }\n  /**\n  Add a block element. Can be called by [block\n  parsers](#BlockParser.parse).\n  */\n  addElement(elt2) {\n    this.block.addChild(elt2.toTree(this.parser.nodeSet), elt2.from - this.block.from);\n  }\n  /**\n  Add a block element from a [leaf parser](#LeafBlockParser). This\n  makes sure any extra composite block markup (such as blockquote\n  markers) inside the block are also added to the syntax tree.\n  */\n  addLeafElement(leaf, elt2) {\n    this.addNode(this.buffer.writeElements(injectMarks(elt2.children, leaf.marks), -elt2.from).finish(elt2.type, elt2.to - elt2.from), elt2.from);\n  }\n  /**\n  @internal\n  */\n  finishContext() {\n    let cx = this.stack.pop();\n    let top = this.stack[this.stack.length - 1];\n    top.addChild(cx.toTree(this.parser.nodeSet), cx.from - top.from);\n    this.block = top;\n  }\n  finish() {\n    while (this.stack.length > 1)\n      this.finishContext();\n    return this.addGaps(this.block.toTree(this.parser.nodeSet, this.lineStart));\n  }\n  addGaps(tree) {\n    return this.ranges.length > 1 ? injectGaps(this.ranges, 0, tree.topNode, this.ranges[0].from, this.reusePlaceholders) : tree;\n  }\n  /**\n  @internal\n  */\n  finishLeaf(leaf) {\n    for (let parser2 of leaf.parsers)\n      if (parser2.finish(this, leaf))\n        return;\n    let inline = injectMarks(this.parser.parseInline(leaf.content, leaf.start), leaf.marks);\n    this.addNode(this.buffer.writeElements(inline, -leaf.start).finish(Type.Paragraph, leaf.content.length), leaf.start);\n  }\n  elt(type, from, to, children) {\n    if (typeof type == \"string\")\n      return elt(this.parser.getNodeType(type), from, to, children);\n    return new TreeElement(type, from);\n  }\n  /**\n  @internal\n  */\n  get buffer() {\n    return new Buffer(this.parser.nodeSet);\n  }\n}\nfunction injectGaps(ranges, rangeI, tree, offset, dummies) {\n  let rangeEnd = ranges[rangeI].to;\n  let children = [], positions = [], start = tree.from + offset;\n  function movePastNext(upto, inclusive) {\n    while (inclusive ? upto >= rangeEnd : upto > rangeEnd) {\n      let size = ranges[rangeI + 1].from - rangeEnd;\n      offset += size;\n      upto += size;\n      rangeI++;\n      rangeEnd = ranges[rangeI].to;\n    }\n  }\n  for (let ch = tree.firstChild; ch; ch = ch.nextSibling) {\n    movePastNext(ch.from + offset, true);\n    let from = ch.from + offset, node, reuse = dummies.get(ch.tree);\n    if (reuse) {\n      node = reuse;\n    } else if (ch.to + offset > rangeEnd) {\n      node = injectGaps(ranges, rangeI, ch, offset, dummies);\n      movePastNext(ch.to + offset, false);\n    } else {\n      node = ch.toTree();\n    }\n    children.push(node);\n    positions.push(from - start);\n  }\n  movePastNext(tree.to + offset, false);\n  return new Tree(tree.type, children, positions, tree.to + offset - start, tree.tree ? tree.tree.propValues : void 0);\n}\nclass MarkdownParser extends Parser {\n  /**\n  @internal\n  */\n  constructor(nodeSet, blockParsers, leafBlockParsers, blockNames, endLeafBlock, skipContextMarkup, inlineParsers, inlineNames, wrappers) {\n    super();\n    this.nodeSet = nodeSet;\n    this.blockParsers = blockParsers;\n    this.leafBlockParsers = leafBlockParsers;\n    this.blockNames = blockNames;\n    this.endLeafBlock = endLeafBlock;\n    this.skipContextMarkup = skipContextMarkup;\n    this.inlineParsers = inlineParsers;\n    this.inlineNames = inlineNames;\n    this.wrappers = wrappers;\n    this.nodeTypes = /* @__PURE__ */ Object.create(null);\n    for (let t of nodeSet.types)\n      this.nodeTypes[t.name] = t.id;\n  }\n  createParse(input, fragments, ranges) {\n    let parse = new BlockContext(this, input, fragments, ranges);\n    for (let w of this.wrappers)\n      parse = w(parse, input, fragments, ranges);\n    return parse;\n  }\n  /**\n  Reconfigure the parser.\n  */\n  configure(spec) {\n    let config = resolveConfig(spec);\n    if (!config)\n      return this;\n    let { nodeSet, skipContextMarkup } = this;\n    let blockParsers = this.blockParsers.slice(), leafBlockParsers = this.leafBlockParsers.slice(), blockNames = this.blockNames.slice(), inlineParsers = this.inlineParsers.slice(), inlineNames = this.inlineNames.slice(), endLeafBlock = this.endLeafBlock.slice(), wrappers = this.wrappers;\n    if (nonEmpty(config.defineNodes)) {\n      skipContextMarkup = Object.assign({}, skipContextMarkup);\n      let nodeTypes2 = nodeSet.types.slice(), styles;\n      for (let s of config.defineNodes) {\n        let { name, block, composite, style } = typeof s == \"string\" ? { name: s } : s;\n        if (nodeTypes2.some((t) => t.name == name))\n          continue;\n        if (composite)\n          skipContextMarkup[nodeTypes2.length] = (bl, cx, line) => composite(cx, line, bl.value);\n        let id = nodeTypes2.length;\n        let group = composite ? [\"Block\", \"BlockContext\"] : !block ? void 0 : id >= Type.ATXHeading1 && id <= Type.SetextHeading2 ? [\"Block\", \"LeafBlock\", \"Heading\"] : [\"Block\", \"LeafBlock\"];\n        nodeTypes2.push(NodeType.define({\n          id,\n          name,\n          props: group && [[NodeProp.group, group]]\n        }));\n        if (style) {\n          if (!styles)\n            styles = {};\n          if (Array.isArray(style) || style instanceof Tag)\n            styles[name] = style;\n          else\n            Object.assign(styles, style);\n        }\n      }\n      nodeSet = new NodeSet(nodeTypes2);\n      if (styles)\n        nodeSet = nodeSet.extend(styleTags(styles));\n    }\n    if (nonEmpty(config.props))\n      nodeSet = nodeSet.extend(...config.props);\n    if (nonEmpty(config.remove)) {\n      for (let rm of config.remove) {\n        let block = this.blockNames.indexOf(rm), inline = this.inlineNames.indexOf(rm);\n        if (block > -1)\n          blockParsers[block] = leafBlockParsers[block] = void 0;\n        if (inline > -1)\n          inlineParsers[inline] = void 0;\n      }\n    }\n    if (nonEmpty(config.parseBlock)) {\n      for (let spec2 of config.parseBlock) {\n        let found = blockNames.indexOf(spec2.name);\n        if (found > -1) {\n          blockParsers[found] = spec2.parse;\n          leafBlockParsers[found] = spec2.leaf;\n        } else {\n          let pos = spec2.before ? findName(blockNames, spec2.before) : spec2.after ? findName(blockNames, spec2.after) + 1 : blockNames.length - 1;\n          blockParsers.splice(pos, 0, spec2.parse);\n          leafBlockParsers.splice(pos, 0, spec2.leaf);\n          blockNames.splice(pos, 0, spec2.name);\n        }\n        if (spec2.endLeaf)\n          endLeafBlock.push(spec2.endLeaf);\n      }\n    }\n    if (nonEmpty(config.parseInline)) {\n      for (let spec2 of config.parseInline) {\n        let found = inlineNames.indexOf(spec2.name);\n        if (found > -1) {\n          inlineParsers[found] = spec2.parse;\n        } else {\n          let pos = spec2.before ? findName(inlineNames, spec2.before) : spec2.after ? findName(inlineNames, spec2.after) + 1 : inlineNames.length - 1;\n          inlineParsers.splice(pos, 0, spec2.parse);\n          inlineNames.splice(pos, 0, spec2.name);\n        }\n      }\n    }\n    if (config.wrap)\n      wrappers = wrappers.concat(config.wrap);\n    return new MarkdownParser(nodeSet, blockParsers, leafBlockParsers, blockNames, endLeafBlock, skipContextMarkup, inlineParsers, inlineNames, wrappers);\n  }\n  /**\n  @internal\n  */\n  getNodeType(name) {\n    let found = this.nodeTypes[name];\n    if (found == null)\n      throw new RangeError(`Unknown node type '${name}'`);\n    return found;\n  }\n  /**\n  Parse the given piece of inline text at the given offset,\n  returning an array of [`Element`](#Element) objects representing\n  the inline content.\n  */\n  parseInline(text, offset) {\n    let cx = new InlineContext(this, text, offset);\n    outer:\n      for (let pos = offset; pos < cx.end; ) {\n        let next = cx.char(pos);\n        for (let token of this.inlineParsers)\n          if (token) {\n            let result = token(cx, next, pos);\n            if (result >= 0) {\n              pos = result;\n              continue outer;\n            }\n          }\n        pos++;\n      }\n    return cx.resolveMarkers(0);\n  }\n}\nfunction nonEmpty(a) {\n  return a != null && a.length > 0;\n}\nfunction resolveConfig(spec) {\n  if (!Array.isArray(spec))\n    return spec;\n  if (spec.length == 0)\n    return null;\n  let conf = resolveConfig(spec[0]);\n  if (spec.length == 1)\n    return conf;\n  let rest = resolveConfig(spec.slice(1));\n  if (!rest || !conf)\n    return conf || rest;\n  let conc = (a, b) => (a || none).concat(b || none);\n  let wrapA = conf.wrap, wrapB = rest.wrap;\n  return {\n    props: conc(conf.props, rest.props),\n    defineNodes: conc(conf.defineNodes, rest.defineNodes),\n    parseBlock: conc(conf.parseBlock, rest.parseBlock),\n    parseInline: conc(conf.parseInline, rest.parseInline),\n    remove: conc(conf.remove, rest.remove),\n    wrap: !wrapA ? wrapB : !wrapB ? wrapA : (inner, input, fragments, ranges) => wrapA(wrapB(inner, input, fragments, ranges), input, fragments, ranges)\n  };\n}\nfunction findName(names, name) {\n  let found = names.indexOf(name);\n  if (found < 0)\n    throw new RangeError(`Position specified relative to unknown parser ${name}`);\n  return found;\n}\nlet nodeTypes = [NodeType.none];\nfor (let i = 1, name; name = Type[i]; i++) {\n  nodeTypes[i] = NodeType.define({\n    id: i,\n    name,\n    props: i >= Type.Escape ? [] : [[NodeProp.group, i in DefaultSkipMarkup ? [\"Block\", \"BlockContext\"] : [\"Block\", \"LeafBlock\"]]],\n    top: name == \"Document\"\n  });\n}\nconst none = [];\nclass Buffer {\n  constructor(nodeSet) {\n    this.nodeSet = nodeSet;\n    this.content = [];\n    this.nodes = [];\n  }\n  write(type, from, to, children = 0) {\n    this.content.push(type, from, to, 4 + children * 4);\n    return this;\n  }\n  writeElements(elts, offset = 0) {\n    for (let e of elts)\n      e.writeTo(this, offset);\n    return this;\n  }\n  finish(type, length) {\n    return Tree.build({\n      buffer: this.content,\n      nodeSet: this.nodeSet,\n      reused: this.nodes,\n      topID: type,\n      length\n    });\n  }\n}\nclass Element {\n  /**\n  @internal\n  */\n  constructor(type, from, to, children = none) {\n    this.type = type;\n    this.from = from;\n    this.to = to;\n    this.children = children;\n  }\n  /**\n  @internal\n  */\n  writeTo(buf, offset) {\n    let startOff = buf.content.length;\n    buf.writeElements(this.children, offset);\n    buf.content.push(this.type, this.from + offset, this.to + offset, buf.content.length + 4 - startOff);\n  }\n  /**\n  @internal\n  */\n  toTree(nodeSet) {\n    return new Buffer(nodeSet).writeElements(this.children, -this.from).finish(this.type, this.to - this.from);\n  }\n}\nclass TreeElement {\n  constructor(tree, from) {\n    this.tree = tree;\n    this.from = from;\n  }\n  get to() {\n    return this.from + this.tree.length;\n  }\n  get type() {\n    return this.tree.type.id;\n  }\n  get children() {\n    return none;\n  }\n  writeTo(buf, offset) {\n    buf.nodes.push(this.tree);\n    buf.content.push(buf.nodes.length - 1, this.from + offset, this.to + offset, -1);\n  }\n  toTree() {\n    return this.tree;\n  }\n}\nfunction elt(type, from, to, children) {\n  return new Element(type, from, to, children);\n}\nconst EmphasisUnderscore = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst EmphasisAsterisk = { resolve: \"Emphasis\", mark: \"EmphasisMark\" };\nconst LinkStart = {}, ImageStart = {};\nclass InlineDelimiter {\n  constructor(type, from, to, side) {\n    this.type = type;\n    this.from = from;\n    this.to = to;\n    this.side = side;\n  }\n}\nconst Escapable = \"!\\\"#$%&'()*+,-./:;<=>?@[\\\\]^_`{|}~\";\nlet Punctuation = /[!\"#$%&'()*+,\\-.\\/:;<=>?@\\[\\\\\\]^_`{|}~\\xA1\\u2010-\\u2027]/;\ntry {\n  Punctuation = new RegExp(\"[\\\\p{S}|\\\\p{P}]\", \"u\");\n} catch (_) {\n}\nconst DefaultInline = {\n  Escape(cx, next, start) {\n    if (next != 92 || start == cx.end - 1)\n      return -1;\n    let escaped = cx.char(start + 1);\n    for (let i = 0; i < Escapable.length; i++)\n      if (Escapable.charCodeAt(i) == escaped)\n        return cx.append(elt(Type.Escape, start, start + 2));\n    return -1;\n  },\n  Entity(cx, next, start) {\n    if (next != 38)\n      return -1;\n    let m = /^(?:#\\d+|#x[a-f\\d]+|\\w+);/i.exec(cx.slice(start + 1, start + 31));\n    return m ? cx.append(elt(Type.Entity, start, start + 1 + m[0].length)) : -1;\n  },\n  InlineCode(cx, next, start) {\n    if (next != 96 || start && cx.char(start - 1) == 96)\n      return -1;\n    let pos = start + 1;\n    while (pos < cx.end && cx.char(pos) == 96)\n      pos++;\n    let size = pos - start, curSize = 0;\n    for (; pos < cx.end; pos++) {\n      if (cx.char(pos) == 96) {\n        curSize++;\n        if (curSize == size && cx.char(pos + 1) != 96)\n          return cx.append(elt(Type.InlineCode, start, pos + 1, [\n            elt(Type.CodeMark, start, start + size),\n            elt(Type.CodeMark, pos + 1 - size, pos + 1)\n          ]));\n      } else {\n        curSize = 0;\n      }\n    }\n    return -1;\n  },\n  HTMLTag(cx, next, start) {\n    if (next != 60 || start == cx.end - 1)\n      return -1;\n    let after = cx.slice(start + 1, cx.end);\n    let url = /^(?:[a-z][-\\w+.]+:[^\\s>]+|[a-z\\d.!#$%&'*+/=?^_`{|}~-]+@[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?(?:\\.[a-z\\d](?:[a-z\\d-]{0,61}[a-z\\d])?)*)>/i.exec(after);\n    if (url) {\n      return cx.append(elt(Type.Autolink, start, start + 1 + url[0].length, [\n        elt(Type.LinkMark, start, start + 1),\n        // url[0] includes the closing bracket, so exclude it from this slice\n        elt(Type.URL, start + 1, start + url[0].length),\n        elt(Type.LinkMark, start + url[0].length, start + 1 + url[0].length)\n      ]));\n    }\n    let comment = /^!--[^>](?:-[^-]|[^-])*?-->/i.exec(after);\n    if (comment)\n      return cx.append(elt(Type.Comment, start, start + 1 + comment[0].length));\n    let procInst = /^\\?[^]*?\\?>/.exec(after);\n    if (procInst)\n      return cx.append(elt(Type.ProcessingInstruction, start, start + 1 + procInst[0].length));\n    let m = /^(?:![A-Z][^]*?>|!\\[CDATA\\[[^]*?\\]\\]>|\\/\\s*[a-zA-Z][\\w-]*\\s*>|\\s*[a-zA-Z][\\w-]*(\\s+[a-zA-Z:_][\\w-.:]*(?:\\s*=\\s*(?:[^\\s\"'=<>`]+|'[^']*'|\"[^\"]*\"))?)*\\s*(\\/\\s*)?>)/.exec(after);\n    if (!m)\n      return -1;\n    return cx.append(elt(Type.HTMLTag, start, start + 1 + m[0].length));\n  },\n  Emphasis(cx, next, start) {\n    if (next != 95 && next != 42)\n      return -1;\n    let pos = start + 1;\n    while (cx.char(pos) == next)\n      pos++;\n    let before = cx.slice(start - 1, start), after = cx.slice(pos, pos + 1);\n    let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n    let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n    let leftFlanking = !sAfter && (!pAfter || sBefore || pBefore);\n    let rightFlanking = !sBefore && (!pBefore || sAfter || pAfter);\n    let canOpen = leftFlanking && (next == 42 || !rightFlanking || pBefore);\n    let canClose = rightFlanking && (next == 42 || !leftFlanking || pAfter);\n    return cx.append(new InlineDelimiter(next == 95 ? EmphasisUnderscore : EmphasisAsterisk, start, pos, (canOpen ? 1 : 0) | (canClose ? 2 : 0)));\n  },\n  HardBreak(cx, next, start) {\n    if (next == 92 && cx.char(start + 1) == 10)\n      return cx.append(elt(Type.HardBreak, start, start + 2));\n    if (next == 32) {\n      let pos = start + 1;\n      while (cx.char(pos) == 32)\n        pos++;\n      if (cx.char(pos) == 10 && pos >= start + 2)\n        return cx.append(elt(Type.HardBreak, start, pos + 1));\n    }\n    return -1;\n  },\n  Link(cx, next, start) {\n    return next == 91 ? cx.append(new InlineDelimiter(\n      LinkStart,\n      start,\n      start + 1,\n      1\n      /* Mark.Open */\n    )) : -1;\n  },\n  Image(cx, next, start) {\n    return next == 33 && cx.char(start + 1) == 91 ? cx.append(new InlineDelimiter(\n      ImageStart,\n      start,\n      start + 2,\n      1\n      /* Mark.Open */\n    )) : -1;\n  },\n  LinkEnd(cx, next, start) {\n    if (next != 93)\n      return -1;\n    for (let i = cx.parts.length - 1; i >= 0; i--) {\n      let part = cx.parts[i];\n      if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart)) {\n        if (!part.side || cx.skipSpace(part.to) == start && !/[(\\[]/.test(cx.slice(start + 1, start + 2))) {\n          cx.parts[i] = null;\n          return -1;\n        }\n        let content = cx.takeContent(i);\n        let link = cx.parts[i] = finishLink(cx, content, part.type == LinkStart ? Type.Link : Type.Image, part.from, start + 1);\n        if (part.type == LinkStart)\n          for (let j = 0; j < i; j++) {\n            let p = cx.parts[j];\n            if (p instanceof InlineDelimiter && p.type == LinkStart)\n              p.side = 0;\n          }\n        return link.to;\n      }\n    }\n    return -1;\n  }\n};\nfunction finishLink(cx, content, type, start, startPos) {\n  let { text } = cx, next = cx.char(startPos), endPos = startPos;\n  content.unshift(elt(Type.LinkMark, start, start + (type == Type.Image ? 2 : 1)));\n  content.push(elt(Type.LinkMark, startPos - 1, startPos));\n  if (next == 40) {\n    let pos = cx.skipSpace(startPos + 1);\n    let dest = parseURL(text, pos - cx.offset, cx.offset), title;\n    if (dest) {\n      pos = cx.skipSpace(dest.to);\n      if (pos != dest.to) {\n        title = parseLinkTitle(text, pos - cx.offset, cx.offset);\n        if (title)\n          pos = cx.skipSpace(title.to);\n      }\n    }\n    if (cx.char(pos) == 41) {\n      content.push(elt(Type.LinkMark, startPos, startPos + 1));\n      endPos = pos + 1;\n      if (dest)\n        content.push(dest);\n      if (title)\n        content.push(title);\n      content.push(elt(Type.LinkMark, pos, endPos));\n    }\n  } else if (next == 91) {\n    let label = parseLinkLabel(text, startPos - cx.offset, cx.offset, false);\n    if (label) {\n      content.push(label);\n      endPos = label.to;\n    }\n  }\n  return elt(type, start, endPos, content);\n}\nfunction parseURL(text, start, offset) {\n  let next = text.charCodeAt(start);\n  if (next == 60) {\n    for (let pos = start + 1; pos < text.length; pos++) {\n      let ch = text.charCodeAt(pos);\n      if (ch == 62)\n        return elt(Type.URL, start + offset, pos + 1 + offset);\n      if (ch == 60 || ch == 10)\n        return false;\n    }\n    return null;\n  } else {\n    let depth = 0, pos = start;\n    for (let escaped = false; pos < text.length; pos++) {\n      let ch = text.charCodeAt(pos);\n      if (space(ch)) {\n        break;\n      } else if (escaped) {\n        escaped = false;\n      } else if (ch == 40) {\n        depth++;\n      } else if (ch == 41) {\n        if (!depth)\n          break;\n        depth--;\n      } else if (ch == 92) {\n        escaped = true;\n      }\n    }\n    return pos > start ? elt(Type.URL, start + offset, pos + offset) : pos == text.length ? null : false;\n  }\n}\nfunction parseLinkTitle(text, start, offset) {\n  let next = text.charCodeAt(start);\n  if (next != 39 && next != 34 && next != 40)\n    return false;\n  let end = next == 40 ? 41 : next;\n  for (let pos = start + 1, escaped = false; pos < text.length; pos++) {\n    let ch = text.charCodeAt(pos);\n    if (escaped)\n      escaped = false;\n    else if (ch == end)\n      return elt(Type.LinkTitle, start + offset, pos + 1 + offset);\n    else if (ch == 92)\n      escaped = true;\n  }\n  return null;\n}\nfunction parseLinkLabel(text, start, offset, requireNonWS) {\n  for (let escaped = false, pos = start + 1, end = Math.min(text.length, pos + 999); pos < end; pos++) {\n    let ch = text.charCodeAt(pos);\n    if (escaped)\n      escaped = false;\n    else if (ch == 93)\n      return requireNonWS ? false : elt(Type.LinkLabel, start + offset, pos + 1 + offset);\n    else {\n      if (requireNonWS && !space(ch))\n        requireNonWS = false;\n      if (ch == 91)\n        return false;\n      else if (ch == 92)\n        escaped = true;\n    }\n  }\n  return null;\n}\nclass InlineContext {\n  /**\n  @internal\n  */\n  constructor(parser2, text, offset) {\n    this.parser = parser2;\n    this.text = text;\n    this.offset = offset;\n    this.parts = [];\n  }\n  /**\n  Get the character code at the given (document-relative)\n  position.\n  */\n  char(pos) {\n    return pos >= this.end ? -1 : this.text.charCodeAt(pos - this.offset);\n  }\n  /**\n  The position of the end of this inline section.\n  */\n  get end() {\n    return this.offset + this.text.length;\n  }\n  /**\n  Get a substring of this inline section. Again uses\n  document-relative positions.\n  */\n  slice(from, to) {\n    return this.text.slice(from - this.offset, to - this.offset);\n  }\n  /**\n  @internal\n  */\n  append(elt2) {\n    this.parts.push(elt2);\n    return elt2.to;\n  }\n  /**\n  Add a [delimiter](#DelimiterType) at this given position. `open`\n  and `close` indicate whether this delimiter is opening, closing,\n  or both. Returns the end of the delimiter, for convenient\n  returning from [parse functions](#InlineParser.parse).\n  */\n  addDelimiter(type, from, to, open, close) {\n    return this.append(new InlineDelimiter(type, from, to, (open ? 1 : 0) | (close ? 2 : 0)));\n  }\n  /**\n  Returns true when there is an unmatched link or image opening\n  token before the current position.\n  */\n  get hasOpenLink() {\n    for (let i = this.parts.length - 1; i >= 0; i--) {\n      let part = this.parts[i];\n      if (part instanceof InlineDelimiter && (part.type == LinkStart || part.type == ImageStart))\n        return true;\n    }\n    return false;\n  }\n  /**\n  Add an inline element. Returns the end of the element.\n  */\n  addElement(elt2) {\n    return this.append(elt2);\n  }\n  /**\n  Resolve markers between this.parts.length and from, wrapping matched markers in the\n  appropriate node and updating the content of this.parts. @internal\n  */\n  resolveMarkers(from) {\n    for (let i = from; i < this.parts.length; i++) {\n      let close = this.parts[i];\n      if (!(close instanceof InlineDelimiter && close.type.resolve && close.side & 2))\n        continue;\n      let emp = close.type == EmphasisUnderscore || close.type == EmphasisAsterisk;\n      let closeSize = close.to - close.from;\n      let open, j = i - 1;\n      for (; j >= from; j--) {\n        let part = this.parts[j];\n        if (part instanceof InlineDelimiter && part.side & 1 && part.type == close.type && // Ignore emphasis delimiters where the character count doesn't match\n        !(emp && (close.side & 1 || part.side & 2) && (part.to - part.from + closeSize) % 3 == 0 && ((part.to - part.from) % 3 || closeSize % 3))) {\n          open = part;\n          break;\n        }\n      }\n      if (!open)\n        continue;\n      let type = close.type.resolve, content = [];\n      let start = open.from, end = close.to;\n      if (emp) {\n        let size = Math.min(2, open.to - open.from, closeSize);\n        start = open.to - size;\n        end = close.from + size;\n        type = size == 1 ? \"Emphasis\" : \"StrongEmphasis\";\n      }\n      if (open.type.mark)\n        content.push(this.elt(open.type.mark, start, open.to));\n      for (let k = j + 1; k < i; k++) {\n        if (this.parts[k] instanceof Element)\n          content.push(this.parts[k]);\n        this.parts[k] = null;\n      }\n      if (close.type.mark)\n        content.push(this.elt(close.type.mark, close.from, end));\n      let element = this.elt(type, start, end, content);\n      this.parts[j] = emp && open.from != start ? new InlineDelimiter(open.type, open.from, start, open.side) : null;\n      let keep = this.parts[i] = emp && close.to != end ? new InlineDelimiter(close.type, end, close.to, close.side) : null;\n      if (keep)\n        this.parts.splice(i, 0, element);\n      else\n        this.parts[i] = element;\n    }\n    let result = [];\n    for (let i = from; i < this.parts.length; i++) {\n      let part = this.parts[i];\n      if (part instanceof Element)\n        result.push(part);\n    }\n    return result;\n  }\n  /**\n  Find an opening delimiter of the given type. Returns `null` if\n  no delimiter is found, or an index that can be passed to\n  [`takeContent`](#InlineContext.takeContent) otherwise.\n  */\n  findOpeningDelimiter(type) {\n    for (let i = this.parts.length - 1; i >= 0; i--) {\n      let part = this.parts[i];\n      if (part instanceof InlineDelimiter && part.type == type)\n        return i;\n    }\n    return null;\n  }\n  /**\n  Remove all inline elements and delimiters starting from the\n  given index (which you should get from\n  [`findOpeningDelimiter`](#InlineContext.findOpeningDelimiter),\n  resolve delimiters inside of them, and return them as an array\n  of elements.\n  */\n  takeContent(startIndex) {\n    let content = this.resolveMarkers(startIndex);\n    this.parts.length = startIndex;\n    return content;\n  }\n  /**\n  Skip space after the given (document) position, returning either\n  the position of the next non-space character or the end of the\n  section.\n  */\n  skipSpace(from) {\n    return skipSpace(this.text, from - this.offset) + this.offset;\n  }\n  elt(type, from, to, children) {\n    if (typeof type == \"string\")\n      return elt(this.parser.getNodeType(type), from, to, children);\n    return new TreeElement(type, from);\n  }\n}\nfunction injectMarks(elements, marks) {\n  if (!marks.length)\n    return elements;\n  if (!elements.length)\n    return marks;\n  let elts = elements.slice(), eI = 0;\n  for (let mark of marks) {\n    while (eI < elts.length && elts[eI].to < mark.to)\n      eI++;\n    if (eI < elts.length && elts[eI].from < mark.from) {\n      let e = elts[eI];\n      if (e instanceof Element)\n        elts[eI] = new Element(e.type, e.from, e.to, injectMarks(e.children, [mark]));\n    } else {\n      elts.splice(eI++, 0, mark);\n    }\n  }\n  return elts;\n}\nconst NotLast = [Type.CodeBlock, Type.ListItem, Type.OrderedList, Type.BulletList];\nclass FragmentCursor {\n  constructor(fragments, input) {\n    this.fragments = fragments;\n    this.input = input;\n    this.i = 0;\n    this.fragment = null;\n    this.fragmentEnd = -1;\n    this.cursor = null;\n    if (fragments.length)\n      this.fragment = fragments[this.i++];\n  }\n  nextFragment() {\n    this.fragment = this.i < this.fragments.length ? this.fragments[this.i++] : null;\n    this.cursor = null;\n    this.fragmentEnd = -1;\n  }\n  moveTo(pos, lineStart) {\n    while (this.fragment && this.fragment.to <= pos)\n      this.nextFragment();\n    if (!this.fragment || this.fragment.from > (pos ? pos - 1 : 0))\n      return false;\n    if (this.fragmentEnd < 0) {\n      let end = this.fragment.to;\n      while (end > 0 && this.input.read(end - 1, end) != \"\\n\")\n        end--;\n      this.fragmentEnd = end ? end - 1 : 0;\n    }\n    let c = this.cursor;\n    if (!c) {\n      c = this.cursor = this.fragment.tree.cursor();\n      c.firstChild();\n    }\n    let rPos = pos + this.fragment.offset;\n    while (c.to <= rPos)\n      if (!c.parent())\n        return false;\n    for (; ; ) {\n      if (c.from >= rPos)\n        return this.fragment.from <= lineStart;\n      if (!c.childAfter(rPos))\n        return false;\n    }\n  }\n  matches(hash) {\n    let tree = this.cursor.tree;\n    return tree && tree.prop(NodeProp.contextHash) == hash;\n  }\n  takeNodes(cx) {\n    let cur = this.cursor, off = this.fragment.offset, fragEnd = this.fragmentEnd - (this.fragment.openEnd ? 1 : 0);\n    let start = cx.absoluteLineStart, end = start, blockI = cx.block.children.length;\n    let prevEnd = end, prevI = blockI;\n    for (; ; ) {\n      if (cur.to - off > fragEnd) {\n        if (cur.type.isAnonymous && cur.firstChild())\n          continue;\n        break;\n      }\n      let pos = toRelative(cur.from - off, cx.ranges);\n      if (cur.to - off <= cx.ranges[cx.rangeI].to) {\n        cx.addNode(cur.tree, pos);\n      } else {\n        let dummy = new Tree(cx.parser.nodeSet.types[Type.Paragraph], [], [], 0, cx.block.hashProp);\n        cx.reusePlaceholders.set(dummy, cur.tree);\n        cx.addNode(dummy, pos);\n      }\n      if (cur.type.is(\"Block\")) {\n        if (NotLast.indexOf(cur.type.id) < 0) {\n          end = cur.to - off;\n          blockI = cx.block.children.length;\n        } else {\n          end = prevEnd;\n          blockI = prevI;\n          prevEnd = cur.to - off;\n          prevI = cx.block.children.length;\n        }\n      }\n      if (!cur.nextSibling())\n        break;\n    }\n    while (cx.block.children.length > blockI) {\n      cx.block.children.pop();\n      cx.block.positions.pop();\n    }\n    return end - start;\n  }\n}\nfunction toRelative(abs, ranges) {\n  let pos = abs;\n  for (let i = 1; i < ranges.length; i++) {\n    let gapFrom = ranges[i - 1].to, gapTo = ranges[i].from;\n    if (gapFrom < abs)\n      pos -= gapTo - gapFrom;\n  }\n  return pos;\n}\nconst markdownHighlighting = styleTags({\n  \"Blockquote/...\": tags.quote,\n  HorizontalRule: tags.contentSeparator,\n  \"ATXHeading1/... SetextHeading1/...\": tags.heading1,\n  \"ATXHeading2/... SetextHeading2/...\": tags.heading2,\n  \"ATXHeading3/...\": tags.heading3,\n  \"ATXHeading4/...\": tags.heading4,\n  \"ATXHeading5/...\": tags.heading5,\n  \"ATXHeading6/...\": tags.heading6,\n  \"Comment CommentBlock\": tags.comment,\n  Escape: tags.escape,\n  Entity: tags.character,\n  \"Emphasis/...\": tags.emphasis,\n  \"StrongEmphasis/...\": tags.strong,\n  \"Link/... Image/...\": tags.link,\n  \"OrderedList/... BulletList/...\": tags.list,\n  \"BlockQuote/...\": tags.quote,\n  \"InlineCode CodeText\": tags.monospace,\n  \"URL Autolink\": tags.url,\n  \"HeaderMark HardBreak QuoteMark ListMark LinkMark EmphasisMark CodeMark\": tags.processingInstruction,\n  \"CodeInfo LinkLabel\": tags.labelName,\n  LinkTitle: tags.string,\n  Paragraph: tags.content\n});\nconst parser = new MarkdownParser(new NodeSet(nodeTypes).extend(markdownHighlighting), Object.keys(DefaultBlockParsers).map((n) => DefaultBlockParsers[n]), Object.keys(DefaultBlockParsers).map((n) => DefaultLeafBlocks[n]), Object.keys(DefaultBlockParsers), DefaultEndLeaf, DefaultSkipMarkup, Object.keys(DefaultInline).map((n) => DefaultInline[n]), Object.keys(DefaultInline), []);\nfunction leftOverSpace(node, from, to) {\n  let ranges = [];\n  for (let n = node.firstChild, pos = from; ; n = n.nextSibling) {\n    let nextPos = n ? n.from : to;\n    if (nextPos > pos)\n      ranges.push({ from: pos, to: nextPos });\n    if (!n)\n      break;\n    pos = n.to;\n  }\n  return ranges;\n}\nfunction parseCode(config) {\n  let { codeParser, htmlParser } = config;\n  let wrap = parseMixed((node, input) => {\n    let id = node.type.id;\n    if (codeParser && (id == Type.CodeBlock || id == Type.FencedCode)) {\n      let info = \"\";\n      if (id == Type.FencedCode) {\n        let infoNode = node.node.getChild(Type.CodeInfo);\n        if (infoNode)\n          info = input.read(infoNode.from, infoNode.to);\n      }\n      let parser2 = codeParser(info);\n      if (parser2)\n        return { parser: parser2, overlay: (node2) => node2.type.id == Type.CodeText };\n    } else if (htmlParser && (id == Type.HTMLBlock || id == Type.HTMLTag)) {\n      return { parser: htmlParser, overlay: leftOverSpace(node.node, node.from, node.to) };\n    }\n    return null;\n  });\n  return { wrap };\n}\nconst StrikethroughDelim = { resolve: \"Strikethrough\", mark: \"StrikethroughMark\" };\nconst Strikethrough = {\n  defineNodes: [{\n    name: \"Strikethrough\",\n    style: { \"Strikethrough/...\": tags.strikethrough }\n  }, {\n    name: \"StrikethroughMark\",\n    style: tags.processingInstruction\n  }],\n  parseInline: [{\n    name: \"Strikethrough\",\n    parse(cx, next, pos) {\n      if (next != 126 || cx.char(pos + 1) != 126 || cx.char(pos + 2) == 126)\n        return -1;\n      let before = cx.slice(pos - 1, pos), after = cx.slice(pos + 2, pos + 3);\n      let sBefore = /\\s|^$/.test(before), sAfter = /\\s|^$/.test(after);\n      let pBefore = Punctuation.test(before), pAfter = Punctuation.test(after);\n      return cx.addDelimiter(StrikethroughDelim, pos, pos + 2, !sAfter && (!pAfter || sBefore || pBefore), !sBefore && (!pBefore || sAfter || pAfter));\n    },\n    after: \"Emphasis\"\n  }]\n};\nfunction parseRow(cx, line, startI = 0, elts, offset = 0) {\n  let count2 = 0, first = true, cellStart = -1, cellEnd = -1, esc = false;\n  let parseCell = () => {\n    elts.push(cx.elt(\"TableCell\", offset + cellStart, offset + cellEnd, cx.parser.parseInline(line.slice(cellStart, cellEnd), offset + cellStart)));\n  };\n  for (let i = startI; i < line.length; i++) {\n    let next = line.charCodeAt(i);\n    if (next == 124 && !esc) {\n      if (!first || cellStart > -1)\n        count2++;\n      first = false;\n      if (elts) {\n        if (cellStart > -1)\n          parseCell();\n        elts.push(cx.elt(\"TableDelimiter\", i + offset, i + offset + 1));\n      }\n      cellStart = cellEnd = -1;\n    } else if (esc || next != 32 && next != 9) {\n      if (cellStart < 0)\n        cellStart = i;\n      cellEnd = i + 1;\n    }\n    esc = !esc && next == 92;\n  }\n  if (cellStart > -1) {\n    count2++;\n    if (elts)\n      parseCell();\n  }\n  return count2;\n}\nfunction hasPipe(str, start) {\n  for (let i = start; i < str.length; i++) {\n    let next = str.charCodeAt(i);\n    if (next == 124)\n      return true;\n    if (next == 92)\n      i++;\n  }\n  return false;\n}\nconst delimiterLine = /^\\|?(\\s*:?-+:?\\s*\\|)+(\\s*:?-+:?\\s*)?$/;\nclass TableParser {\n  constructor() {\n    this.rows = null;\n  }\n  nextLine(cx, line, leaf) {\n    if (this.rows == null) {\n      this.rows = false;\n      let lineText;\n      if ((line.next == 45 || line.next == 58 || line.next == 124) && delimiterLine.test(lineText = line.text.slice(line.pos))) {\n        let firstRow = [], firstCount = parseRow(cx, leaf.content, 0, firstRow, leaf.start);\n        if (firstCount == parseRow(cx, lineText, line.pos))\n          this.rows = [\n            cx.elt(\"TableHeader\", leaf.start, leaf.start + leaf.content.length, firstRow),\n            cx.elt(\"TableDelimiter\", cx.lineStart + line.pos, cx.lineStart + line.text.length)\n          ];\n      }\n    } else if (this.rows) {\n      let content = [];\n      parseRow(cx, line.text, line.pos, content, cx.lineStart);\n      this.rows.push(cx.elt(\"TableRow\", cx.lineStart + line.pos, cx.lineStart + line.text.length, content));\n    }\n    return false;\n  }\n  finish(cx, leaf) {\n    if (!this.rows)\n      return false;\n    cx.addLeafElement(leaf, cx.elt(\"Table\", leaf.start, leaf.start + leaf.content.length, this.rows));\n    return true;\n  }\n}\nconst Table = {\n  defineNodes: [\n    { name: \"Table\", block: true },\n    { name: \"TableHeader\", style: { \"TableHeader/...\": tags.heading } },\n    \"TableRow\",\n    { name: \"TableCell\", style: tags.content },\n    { name: \"TableDelimiter\", style: tags.processingInstruction }\n  ],\n  parseBlock: [{\n    name: \"Table\",\n    leaf(_, leaf) {\n      return hasPipe(leaf.content, 0) ? new TableParser() : null;\n    },\n    endLeaf(cx, line, leaf) {\n      if (leaf.parsers.some((p) => p instanceof TableParser) || !hasPipe(line.text, line.basePos))\n        return false;\n      let next = cx.peekLine();\n      return delimiterLine.test(next) && parseRow(cx, line.text, line.basePos) == parseRow(cx, next, line.basePos);\n    },\n    before: \"SetextHeading\"\n  }]\n};\nclass TaskParser {\n  nextLine() {\n    return false;\n  }\n  finish(cx, leaf) {\n    cx.addLeafElement(leaf, cx.elt(\"Task\", leaf.start, leaf.start + leaf.content.length, [\n      cx.elt(\"TaskMarker\", leaf.start, leaf.start + 3),\n      ...cx.parser.parseInline(leaf.content.slice(3), leaf.start + 3)\n    ]));\n    return true;\n  }\n}\nconst TaskList = {\n  defineNodes: [\n    { name: \"Task\", block: true, style: tags.list },\n    { name: \"TaskMarker\", style: tags.atom }\n  ],\n  parseBlock: [{\n    name: \"TaskList\",\n    leaf(cx, leaf) {\n      return /^\\[[ xX]\\][ \\t]/.test(leaf.content) && cx.parentType().name == \"ListItem\" ? new TaskParser() : null;\n    },\n    after: \"SetextHeading\"\n  }]\n};\nconst autolinkRE = /(www\\.)|(https?:\\/\\/)|([\\w.+-]{1,100}@)|(mailto:|xmpp:)/gy;\nconst urlRE = /[\\w-]+(\\.[\\w-]+)+(\\/[^\\s<]*)?/gy;\nconst lastTwoDomainWords = /[\\w-]+\\.[\\w-]+($|\\/)/;\nconst emailRE = /[\\w.+-]+@[\\w-]+(\\.[\\w.-]+)+/gy;\nconst xmppResourceRE = /\\/[a-zA-Z\\d@.]+/gy;\nfunction count(str, from, to, ch) {\n  let result = 0;\n  for (let i = from; i < to; i++)\n    if (str[i] == ch)\n      result++;\n  return result;\n}\nfunction autolinkURLEnd(text, from) {\n  urlRE.lastIndex = from;\n  let m = urlRE.exec(text);\n  if (!m || lastTwoDomainWords.exec(m[0])[0].indexOf(\"_\") > -1)\n    return -1;\n  let end = from + m[0].length;\n  for (; ; ) {\n    let last = text[end - 1], m2;\n    if (/[?!.,:*_~]/.test(last) || last == \")\" && count(text, from, end, \")\") > count(text, from, end, \"(\"))\n      end--;\n    else if (last == \";\" && (m2 = /&(?:#\\d+|#x[a-f\\d]+|\\w+);$/.exec(text.slice(from, end))))\n      end = from + m2.index;\n    else\n      break;\n  }\n  return end;\n}\nfunction autolinkEmailEnd(text, from) {\n  emailRE.lastIndex = from;\n  let m = emailRE.exec(text);\n  if (!m)\n    return -1;\n  let last = m[0][m[0].length - 1];\n  return last == \"_\" || last == \"-\" ? -1 : from + m[0].length - (last == \".\" ? 1 : 0);\n}\nconst Autolink = {\n  parseInline: [{\n    name: \"Autolink\",\n    parse(cx, next, absPos) {\n      let pos = absPos - cx.offset;\n      if (pos && /\\w/.test(cx.text[pos - 1]))\n        return -1;\n      autolinkRE.lastIndex = pos;\n      let m = autolinkRE.exec(cx.text), end = -1;\n      if (!m)\n        return -1;\n      if (m[1] || m[2]) {\n        end = autolinkURLEnd(cx.text, pos + m[0].length);\n        if (end > -1 && cx.hasOpenLink) {\n          let noBracket = /([^\\[\\]]|\\[[^\\]]*\\])*/.exec(cx.text.slice(pos, end));\n          end = pos + noBracket[0].length;\n        }\n      } else if (m[3]) {\n        end = autolinkEmailEnd(cx.text, pos);\n      } else {\n        end = autolinkEmailEnd(cx.text, pos + m[0].length);\n        if (end > -1 && m[0] == \"xmpp:\") {\n          xmppResourceRE.lastIndex = end;\n          m = xmppResourceRE.exec(cx.text);\n          if (m)\n            end = m.index + m[0].length;\n        }\n      }\n      if (end < 0)\n        return -1;\n      cx.addElement(cx.elt(\"URL\", absPos, end + cx.offset));\n      return end + cx.offset;\n    }\n  }]\n};\nconst GFM = [Table, TaskList, Strikethrough, Autolink];\nfunction parseSubSuper(ch, node, mark) {\n  return (cx, next, pos) => {\n    if (next != ch || cx.char(pos + 1) == ch)\n      return -1;\n    let elts = [cx.elt(mark, pos, pos + 1)];\n    for (let i = pos + 1; i < cx.end; i++) {\n      let next2 = cx.char(i);\n      if (next2 == ch)\n        return cx.addElement(cx.elt(node, pos, i + 1, elts.concat(cx.elt(mark, i, i + 1))));\n      if (next2 == 92)\n        elts.push(cx.elt(\"Escape\", i, i++ + 2));\n      if (space(next2))\n        break;\n    }\n    return -1;\n  };\n}\nconst Superscript = {\n  defineNodes: [\n    { name: \"Superscript\", style: tags.special(tags.content) },\n    { name: \"SuperscriptMark\", style: tags.processingInstruction }\n  ],\n  parseInline: [{\n    name: \"Superscript\",\n    parse: parseSubSuper(94, \"Superscript\", \"SuperscriptMark\")\n  }]\n};\nconst Subscript = {\n  defineNodes: [\n    { name: \"Subscript\", style: tags.special(tags.content) },\n    { name: \"SubscriptMark\", style: tags.processingInstruction }\n  ],\n  parseInline: [{\n    name: \"Subscript\",\n    parse: parseSubSuper(126, \"Subscript\", \"SubscriptMark\")\n  }]\n};\nconst Emoji = {\n  defineNodes: [{ name: \"Emoji\", style: tags.character }],\n  parseInline: [{\n    name: \"Emoji\",\n    parse(cx, next, pos) {\n      let match;\n      if (next != 58 || !(match = /^[a-zA-Z_0-9]+:/.exec(cx.slice(pos + 1, cx.end))))\n        return -1;\n      return cx.addElement(cx.elt(\"Emoji\", pos, pos + 1 + match[0].length));\n    }\n  }]\n};\nconst data = /* @__PURE__ */ defineLanguageFacet({ commentTokens: { block: { open: \"<!--\", close: \"-->\" } } });\nconst headingProp = /* @__PURE__ */ new NodeProp();\nconst commonmark = /* @__PURE__ */ parser.configure({\n  props: [\n    /* @__PURE__ */ foldNodeProp.add((type) => {\n      return !type.is(\"Block\") || type.is(\"Document\") || isHeading(type) != null || isList(type) ? void 0 : (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to });\n    }),\n    /* @__PURE__ */ headingProp.add(isHeading),\n    /* @__PURE__ */ indentNodeProp.add({\n      Document: () => null\n    }),\n    /* @__PURE__ */ languageDataProp.add({\n      Document: data\n    })\n  ]\n});\nfunction isHeading(type) {\n  let match = /^(?:ATX|Setext)Heading(\\d)$/.exec(type.name);\n  return match ? +match[1] : void 0;\n}\nfunction isList(type) {\n  return type.name == \"OrderedList\" || type.name == \"BulletList\";\n}\nfunction findSectionEnd(headerNode, level) {\n  let last = headerNode;\n  for (; ; ) {\n    let next = last.nextSibling, heading;\n    if (!next || (heading = isHeading(next.type)) != null && heading <= level)\n      break;\n    last = next;\n  }\n  return last.to;\n}\nconst headerIndent = /* @__PURE__ */ foldService.of((state, start, end) => {\n  for (let node = syntaxTree(state).resolveInner(end, -1); node; node = node.parent) {\n    if (node.from < start)\n      break;\n    let heading = node.type.prop(headingProp);\n    if (heading == null)\n      continue;\n    let upto = findSectionEnd(node, heading);\n    if (upto > end)\n      return { from: end, to: upto };\n  }\n  return null;\n});\nfunction mkLang(parser2) {\n  return new Language(data, parser2, [headerIndent], \"markdown\");\n}\nconst commonmarkLanguage = /* @__PURE__ */ mkLang(commonmark);\nconst extended = /* @__PURE__ */ commonmark.configure([GFM, Subscript, Superscript, Emoji, {\n  props: [\n    /* @__PURE__ */ foldNodeProp.add({\n      Table: (tree, state) => ({ from: state.doc.lineAt(tree.from).to, to: tree.to })\n    })\n  ]\n}]);\nconst markdownLanguage = /* @__PURE__ */ mkLang(extended);\nfunction getCodeParser(languages, defaultLanguage) {\n  return (info) => {\n    if (info && languages) {\n      let found = null;\n      info = /\\S*/.exec(info)[0];\n      if (typeof languages == \"function\")\n        found = languages(info);\n      else\n        found = LanguageDescription.matchLanguageName(languages, info, true);\n      if (found instanceof LanguageDescription)\n        return found.support ? found.support.language.parser : ParseContext.getSkippingParser(found.load());\n      else if (found)\n        return found.parser;\n    }\n    return defaultLanguage ? defaultLanguage.parser : null;\n  };\n}\nclass Context {\n  constructor(node, from, to, spaceBefore, spaceAfter, type, item) {\n    this.node = node;\n    this.from = from;\n    this.to = to;\n    this.spaceBefore = spaceBefore;\n    this.spaceAfter = spaceAfter;\n    this.type = type;\n    this.item = item;\n  }\n  blank(maxWidth, trailing = true) {\n    let result = this.spaceBefore + (this.node.name == \"Blockquote\" ? \">\" : \"\");\n    if (maxWidth != null) {\n      while (result.length < maxWidth)\n        result += \" \";\n      return result;\n    } else {\n      for (let i = this.to - this.from - result.length - this.spaceAfter.length; i > 0; i--)\n        result += \" \";\n      return result + (trailing ? this.spaceAfter : \"\");\n    }\n  }\n  marker(doc, add) {\n    let number = this.node.name == \"OrderedList\" ? String(+itemNumber(this.item, doc)[2] + add) : \"\";\n    return this.spaceBefore + number + this.type + this.spaceAfter;\n  }\n}\nfunction getContext(node, doc) {\n  let nodes = [], context = [];\n  for (let cur = node; cur; cur = cur.parent) {\n    if (cur.name == \"FencedCode\")\n      return context;\n    if (cur.name == \"ListItem\" || cur.name == \"Blockquote\")\n      nodes.push(cur);\n  }\n  for (let i = nodes.length - 1; i >= 0; i--) {\n    let node2 = nodes[i], match;\n    let line = doc.lineAt(node2.from), startPos = node2.from - line.from;\n    if (node2.name == \"Blockquote\" && (match = /^ *>( ?)/.exec(line.text.slice(startPos)))) {\n      context.push(new Context(node2, startPos, startPos + match[0].length, \"\", match[1], \">\", null));\n    } else if (node2.name == \"ListItem\" && node2.parent.name == \"OrderedList\" && (match = /^( *)\\d+([.)])( *)/.exec(line.text.slice(startPos)))) {\n      let after = match[3], len = match[0].length;\n      if (after.length >= 4) {\n        after = after.slice(0, after.length - 4);\n        len -= 4;\n      }\n      context.push(new Context(node2.parent, startPos, startPos + len, match[1], after, match[2], node2));\n    } else if (node2.name == \"ListItem\" && node2.parent.name == \"BulletList\" && (match = /^( *)([-+*])( {1,4}\\[[ xX]\\])?( +)/.exec(line.text.slice(startPos)))) {\n      let after = match[4], len = match[0].length;\n      if (after.length > 4) {\n        after = after.slice(0, after.length - 4);\n        len -= 4;\n      }\n      let type = match[2];\n      if (match[3])\n        type += match[3].replace(/[xX]/, \" \");\n      context.push(new Context(node2.parent, startPos, startPos + len, match[1], after, type, node2));\n    }\n  }\n  return context;\n}\nfunction itemNumber(item, doc) {\n  return /^(\\s*)(\\d+)(?=[.)])/.exec(doc.sliceString(item.from, item.from + 10));\n}\nfunction renumberList(after, doc, changes, offset = 0) {\n  for (let prev = -1, node = after; ; ) {\n    if (node.name == \"ListItem\") {\n      let m = itemNumber(node, doc);\n      let number = +m[2];\n      if (prev >= 0) {\n        if (number != prev + 1)\n          return;\n        changes.push({ from: node.from + m[1].length, to: node.from + m[0].length, insert: String(prev + 2 + offset) });\n      }\n      prev = number;\n    }\n    let next = node.nextSibling;\n    if (!next)\n      break;\n    node = next;\n  }\n}\nfunction normalizeIndent(content, state) {\n  let blank = /^[ \\t]*/.exec(content)[0].length;\n  if (!blank || state.facet(indentUnit) != \"\t\")\n    return content;\n  let col = countColumn(content, 4, blank);\n  let space2 = \"\";\n  for (let i = col; i > 0; ) {\n    if (i >= 4) {\n      space2 += \"\t\";\n      i -= 4;\n    } else {\n      space2 += \" \";\n      i--;\n    }\n  }\n  return space2 + content.slice(blank);\n}\nconst insertNewlineContinueMarkup = ({ state, dispatch }) => {\n  let tree = syntaxTree(state), { doc } = state;\n  let dont = null, changes = state.changeByRange((range) => {\n    if (!range.empty || !markdownLanguage.isActiveAt(state, range.from, 0))\n      return dont = { range };\n    let pos = range.from, line = doc.lineAt(pos);\n    let context = getContext(tree.resolveInner(pos, -1), doc);\n    while (context.length && context[context.length - 1].from > pos - line.from)\n      context.pop();\n    if (!context.length)\n      return dont = { range };\n    let inner = context[context.length - 1];\n    if (inner.to - inner.spaceAfter.length > pos - line.from)\n      return dont = { range };\n    let emptyLine = pos >= inner.to - inner.spaceAfter.length && !/\\S/.test(line.text.slice(inner.to));\n    if (inner.item && emptyLine) {\n      let first = inner.node.firstChild, second = inner.node.getChild(\"ListItem\", \"ListItem\");\n      if (first.to >= pos || second && second.to < pos || line.from > 0 && !/[^\\s>]/.test(doc.lineAt(line.from - 1).text)) {\n        let next = context.length > 1 ? context[context.length - 2] : null;\n        let delTo, insert2 = \"\";\n        if (next && next.item) {\n          delTo = line.from + next.from;\n          insert2 = next.marker(doc, 1);\n        } else {\n          delTo = line.from + (next ? next.to : 0);\n        }\n        let changes3 = [{ from: delTo, to: pos, insert: insert2 }];\n        if (inner.node.name == \"OrderedList\")\n          renumberList(inner.item, doc, changes3, -2);\n        if (next && next.node.name == \"OrderedList\")\n          renumberList(next.item, doc, changes3);\n        return { range: EditorSelection.cursor(delTo + insert2.length), changes: changes3 };\n      } else {\n        let insert2 = blankLine(context, state, line);\n        return {\n          range: EditorSelection.cursor(pos + insert2.length + 1),\n          changes: { from: line.from, insert: insert2 + state.lineBreak }\n        };\n      }\n    }\n    if (inner.node.name == \"Blockquote\" && emptyLine && line.from) {\n      let prevLine = doc.lineAt(line.from - 1), quoted = />\\s*$/.exec(prevLine.text);\n      if (quoted && quoted.index == inner.from) {\n        let changes3 = state.changes([\n          { from: prevLine.from + quoted.index, to: prevLine.to },\n          { from: line.from + inner.from, to: line.to }\n        ]);\n        return { range: range.map(changes3), changes: changes3 };\n      }\n    }\n    let changes2 = [];\n    if (inner.node.name == \"OrderedList\")\n      renumberList(inner.item, doc, changes2);\n    let continued = inner.item && inner.item.from < line.from;\n    let insert = \"\";\n    if (!continued || /^[\\s\\d.)\\-+*>]*/.exec(line.text)[0].length >= inner.to) {\n      for (let i = 0, e = context.length - 1; i <= e; i++) {\n        insert += i == e && !continued ? context[i].marker(doc, 1) : context[i].blank(i < e ? countColumn(line.text, 4, context[i + 1].from) - insert.length : null);\n      }\n    }\n    let from = pos;\n    while (from > line.from && /\\s/.test(line.text.charAt(from - line.from - 1)))\n      from--;\n    insert = normalizeIndent(insert, state);\n    if (nonTightList(inner.node, state.doc))\n      insert = blankLine(context, state, line) + state.lineBreak + insert;\n    changes2.push({ from, to: pos, insert: state.lineBreak + insert });\n    return { range: EditorSelection.cursor(from + insert.length + 1), changes: changes2 };\n  });\n  if (dont)\n    return false;\n  dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"input\" }));\n  return true;\n};\nfunction isMark(node) {\n  return node.name == \"QuoteMark\" || node.name == \"ListMark\";\n}\nfunction nonTightList(node, doc) {\n  if (node.name != \"OrderedList\" && node.name != \"BulletList\")\n    return false;\n  let first = node.firstChild, second = node.getChild(\"ListItem\", \"ListItem\");\n  if (!second)\n    return false;\n  let line1 = doc.lineAt(first.to), line2 = doc.lineAt(second.from);\n  let empty = /^[\\s>]*$/.test(line1.text);\n  return line1.number + (empty ? 0 : 1) < line2.number;\n}\nfunction blankLine(context, state, line) {\n  let insert = \"\";\n  for (let i = 0, e = context.length - 2; i <= e; i++) {\n    insert += context[i].blank(i < e ? countColumn(line.text, 4, Math.min(line.text.length, context[i + 1].from)) - insert.length : null, i < e);\n  }\n  return normalizeIndent(insert, state);\n}\nfunction contextNodeForDelete(tree, pos) {\n  let node = tree.resolveInner(pos, -1), scan = pos;\n  if (isMark(node)) {\n    scan = node.from;\n    node = node.parent;\n  }\n  for (let prev; prev = node.childBefore(scan); ) {\n    if (isMark(prev)) {\n      scan = prev.from;\n    } else if (prev.name == \"OrderedList\" || prev.name == \"BulletList\") {\n      node = prev.lastChild;\n      scan = node.to;\n    } else {\n      break;\n    }\n  }\n  return node;\n}\nconst deleteMarkupBackward = ({ state, dispatch }) => {\n  let tree = syntaxTree(state);\n  let dont = null, changes = state.changeByRange((range) => {\n    let pos = range.from, { doc } = state;\n    if (range.empty && markdownLanguage.isActiveAt(state, range.from)) {\n      let line = doc.lineAt(pos);\n      let context = getContext(contextNodeForDelete(tree, pos), doc);\n      if (context.length) {\n        let inner = context[context.length - 1];\n        let spaceEnd = inner.to - inner.spaceAfter.length + (inner.spaceAfter ? 1 : 0);\n        if (pos - line.from > spaceEnd && !/\\S/.test(line.text.slice(spaceEnd, pos - line.from)))\n          return {\n            range: EditorSelection.cursor(line.from + spaceEnd),\n            changes: { from: line.from + spaceEnd, to: pos }\n          };\n        if (pos - line.from == spaceEnd && // Only apply this if we're on the line that has the\n        // construct's syntax, or there's only indentation in the\n        // target range\n        (!inner.item || line.from <= inner.item.from || !/\\S/.test(line.text.slice(0, inner.to)))) {\n          let start = line.from + inner.from;\n          if (inner.item && inner.node.from < inner.item.from && /\\S/.test(line.text.slice(inner.from, inner.to))) {\n            let insert = inner.blank(countColumn(line.text, 4, inner.to) - countColumn(line.text, 4, inner.from));\n            if (start == line.from)\n              insert = normalizeIndent(insert, state);\n            return {\n              range: EditorSelection.cursor(start + insert.length),\n              changes: { from: start, to: line.from + inner.to, insert }\n            };\n          }\n          if (start < pos)\n            return { range: EditorSelection.cursor(start), changes: { from: start, to: pos } };\n        }\n      }\n    }\n    return dont = { range };\n  });\n  if (dont)\n    return false;\n  dispatch(state.update(changes, { scrollIntoView: true, userEvent: \"delete\" }));\n  return true;\n};\nconst markdownKeymap = [\n  { key: \"Enter\", run: insertNewlineContinueMarkup },\n  { key: \"Backspace\", run: deleteMarkupBackward }\n];\nconst htmlNoMatch = /* @__PURE__ */ html({ matchClosingTags: false });\nfunction markdown(config = {}) {\n  let { codeLanguages, defaultCodeLanguage, addKeymap = true, base: { parser: parser2 } = commonmarkLanguage, completeHTMLTags = true, htmlTagLanguage = htmlNoMatch } = config;\n  if (!(parser2 instanceof MarkdownParser))\n    throw new RangeError(\"Base parser provided to `markdown` should be a Markdown parser\");\n  let extensions = config.extensions ? [config.extensions] : [];\n  let support = [htmlTagLanguage.support], defaultCode;\n  if (defaultCodeLanguage instanceof LanguageSupport) {\n    support.push(defaultCodeLanguage.support);\n    defaultCode = defaultCodeLanguage.language;\n  } else if (defaultCodeLanguage) {\n    defaultCode = defaultCodeLanguage;\n  }\n  let codeParser = codeLanguages || defaultCode ? getCodeParser(codeLanguages, defaultCode) : void 0;\n  extensions.push(parseCode({ codeParser, htmlParser: htmlTagLanguage.language.parser }));\n  if (addKeymap)\n    support.push(Prec.high(keymap.of(markdownKeymap)));\n  let lang = mkLang(parser2.configure(extensions));\n  if (completeHTMLTags)\n    support.push(lang.data.of({ autocomplete: htmlTagCompletion }));\n  return new LanguageSupport(lang, support);\n}\nfunction htmlTagCompletion(context) {\n  let { state, pos } = context, m = /<[:\\-\\.\\w\\u00b7-\\uffff]*$/.exec(state.sliceDoc(pos - 25, pos));\n  if (!m)\n    return null;\n  let tree = syntaxTree(state).resolveInner(pos, -1);\n  while (tree && !tree.type.isTop) {\n    if (tree.name == \"CodeBlock\" || tree.name == \"FencedCode\" || tree.name == \"ProcessingInstructionBlock\" || tree.name == \"CommentBlock\" || tree.name == \"Link\" || tree.name == \"Image\")\n      return null;\n    tree = tree.parent;\n  }\n  return {\n    from: pos - m[0].length,\n    to: pos,\n    options: htmlTagCompletions(),\n    validFor: /^<[:\\-\\.\\w\\u00b7-\\uffff]*$/\n  };\n}\nlet _tagCompletions = null;\nfunction htmlTagCompletions() {\n  if (_tagCompletions)\n    return _tagCompletions;\n  let result = htmlCompletionSource(new CompletionContext(EditorState.create({ extensions: htmlNoMatch }), 0, true));\n  return _tagCompletions = result ? result.options : [];\n}\nexport {\n  commonmarkLanguage,\n  deleteMarkupBackward,\n  insertNewlineContinueMarkup,\n  markdown,\n  markdownKeymap,\n  markdownLanguage\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA,MAAM,cAAc,CAAC;AACrB,EAAE,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE;AACpD,IAAI,IAAI,IAAI,GAAG,UAAU,IAAI,UAAU,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACxE,IAAI,OAAO,IAAI,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS,EAAE;AACjE,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACnB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;AACnD,GAAG;AACH,EAAE,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;AACvB,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI;AACrD,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjG,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,MAAM,CAAC,OAAO,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AAClC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AACxC,IAAI,IAAI,IAAI,IAAI,CAAC;AACjB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AACzF,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;AACtG,MAAM,QAAQ,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC;AACpH,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACD,IAAI,IAAI,CAAC;AACT,CAAC,SAAS,KAAK,EAAE;AACjB,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC;AAC5C,EAAE,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW,CAAC;AAC9C,EAAE,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC;AAChD,EAAE,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC;AAChD,EAAE,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB,CAAC;AACxD,EAAE,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC;AAChD,EAAE,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;AAClD,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC;AAC5C,EAAE,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC;AAClD,EAAE,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,GAAG,aAAa,CAAC;AACnD,EAAE,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,GAAG,aAAa,CAAC;AACnD,EAAE,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,GAAG,aAAa,CAAC;AACnD,EAAE,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,GAAG,aAAa,CAAC;AACnD,EAAE,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,GAAG,aAAa,CAAC;AACnD,EAAE,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,GAAG,gBAAgB,CAAC;AACzD,EAAE,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,GAAG,gBAAgB,CAAC;AACzD,EAAE,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW,CAAC;AAC/C,EAAE,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,GAAG,eAAe,CAAC;AACvD,EAAE,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW,CAAC;AAC/C,EAAE,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,cAAc,CAAC;AACrD,EAAE,KAAK,CAAC,KAAK,CAAC,4BAA4B,CAAC,GAAG,EAAE,CAAC,GAAG,4BAA4B,CAAC;AACjF,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC;AACzC,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ,CAAC;AACzC,EAAE,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW,CAAC;AAC/C,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AAC7C,EAAE,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC,GAAG,gBAAgB,CAAC;AACzD,EAAE,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM,CAAC;AACrC,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC;AACvC,EAAE,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY,CAAC;AACjD,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC;AAC3C,EAAE,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS,CAAC;AAC3C,EAAE,KAAK,CAAC,KAAK,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC,GAAG,uBAAuB,CAAC;AACvE,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AAC7C,EAAE,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY,CAAC;AACjD,EAAE,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW,CAAC;AAC/C,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AAC7C,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AAC7C,EAAE,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,cAAc,CAAC;AACrD,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AAC7C,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AAC7C,EAAE,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,GAAG,UAAU,CAAC;AAC7C,EAAE,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW,CAAC;AAC/C,EAAE,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW,CAAC;AAC/C,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,CAAC;AACnC,CAAC,EAAE,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;AACxB,MAAM,SAAS,CAAC;AAChB;AACA;AACA;AACA,EAAE,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;AAC9B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,GAAG;AACH,CAAC;AACD,MAAM,IAAI,CAAC;AACX,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACjB,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG;AAC/B,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAC1B,GAAG;AACH;AACA;AACA;AACA,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;AACtB,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAC/E,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtC,GAAG;AACH;AACA;AACA;AACA,EAAE,KAAK,CAAC,IAAI,EAAE;AACd,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;AAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,EAAE,EAAE;AACf,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AAClE,GAAG;AACH;AACA;AACA;AACA,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;AAC7B,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE;AACxC,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;AAClC,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAClE,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA,EAAE,UAAU,CAAC,IAAI,EAAE;AACnB,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC,EAAE;AACnE,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;AAClE,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU;AACxB,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC;AACvB,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE;AACzC,MAAM,MAAM,IAAI,GAAG,CAAC;AACpB,IAAI,OAAO,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClD,GAAG;AACH,CAAC;AACD,SAAS,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;AACnC,EAAE,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU;AACvH,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC;AACxC,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,GAAG,aAAa,GAAG,YAAY,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;AAC3F,EAAE,OAAO,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,IAAI,gBAAgB,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC;AACpJ,CAAC;AACD,MAAM,iBAAiB,GAAG;AAC1B,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;AAClC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;AACvB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AACjG,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAClF,IAAI,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7C,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE;AACjC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAClE,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AACpD,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW;AACjC,EAAE,CAAC,IAAI,CAAC,UAAU,GAAG,WAAW;AAChC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC,CAAC;AACF,SAAS,KAAK,CAAC,EAAE,EAAE;AACnB,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACrD,CAAC;AACD,SAAS,SAAS,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE;AAChC,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACrD,IAAI,CAAC,EAAE,CAAC;AACR,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD,SAAS,aAAa,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;AACpC,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD,IAAI,CAAC,EAAE,CAAC;AACR,EAAE,OAAO,CAAC,CAAC;AACX,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG;AACzC,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACzB,EAAE,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI;AACzE,IAAI,GAAG,EAAE,CAAC;AACV,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC;AACxB,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,EAAE;AACvB,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE;AAC/C,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE;AACvC,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,EAAE,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACjF,CAAC;AACD,SAAS,gBAAgB,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC9C,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;AAC3D,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;AAC9D,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACvC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI;AACvB,MAAM,MAAM,EAAE,CAAC;AACf,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;AACvB,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC9K,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,OAAO,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7B,CAAC;AACD,SAAS,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE;AAC1B,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AAC/C,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI;AAChC,MAAM,OAAO,IAAI,CAAC;AAClB,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC1C,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9P,CAAC;AACD,SAAS,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;AAC3C,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvC,EAAE,WAAW;AACb,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE;AAChC,MAAM,GAAG,EAAE,CAAC;AACZ;AACA,MAAM,MAAM;AACZ,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;AAC/B,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;AACrR,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,OAAO,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AAC5B,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;AACrB,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACzB,EAAE,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE;AAClE,IAAI,GAAG,EAAE,CAAC;AACV,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE;AAC/D,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AAC5B,EAAE,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC9B,CAAC;AACD,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACjC,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC;AAC9E,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACzB,EAAE,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI;AACzE,IAAI,GAAG,EAAE,CAAC;AACV,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC;AAChB,EAAE,OAAO,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACnE,IAAI,GAAG,EAAE,CAAC;AACV,EAAE,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAC5C,CAAC;AACD,MAAM,SAAS,GAAG,UAAU,EAAE,UAAU,GAAG,KAAK,EAAE,aAAa,GAAG,KAAK,CAAC;AACxE,MAAM,cAAc,GAAG;AACvB,EAAE,CAAC,mCAAmC,EAAE,2BAA2B,CAAC;AACpE,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;AAC1B,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;AAC5B,EAAE,CAAC,aAAa,EAAE,GAAG,CAAC;AACtB,EAAE,CAAC,iBAAiB,EAAE,OAAO,CAAC;AAC9B,EAAE,CAAC,+XAA+X,EAAE,SAAS,CAAC;AAC9Y,EAAE,CAAC,kHAAkH,EAAE,SAAS,CAAC;AACjI,CAAC,CAAC;AACF,SAAS,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE;AAC1C,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE;AACrB,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACvC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,IAAI,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAC5E,IAAI,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AACvC,MAAM,OAAO,CAAC,CAAC;AACf,EAAE,OAAO,CAAC,CAAC,CAAC;AACZ,CAAC;AACD,SAAS,aAAa,CAAC,IAAI,EAAE,GAAG,EAAE;AAClC,EAAE,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACjE,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,WAAW,CAAC,CAAC;AACzE,EAAE,OAAO,QAAQ,IAAI,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,QAAQ,CAAC;AAClE,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE;AACtC,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9B,EAAE,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ;AAC9E,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;AACxB;AACA,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7C,CAAC;AACD,MAAM,mBAAmB,GAAG;AAC5B,EAAE,aAAa,EAAE,KAAK,CAAC;AACvB,EAAE,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE;AACzB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACnC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI;AAC1B,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACtC,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,SAAS,GAAG,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAC1E,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,YAAY,GAAG,EAAE,CAAC;AACtC,IAAI,WAAW,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;AACjC,IAAI,OAAO,EAAE,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;AAC3D,MAAM,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACxC,QAAQ,WAAW,CAAC,YAAY,EAAE,EAAE,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;AAClE,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO;AAClC,UAAU,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/B,OAAO,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,EAAE;AACrC,QAAQ,MAAM;AACd,OAAO,MAAM;AACb,QAAQ,IAAI,YAAY,CAAC,MAAM,EAAE;AACjC,UAAU,KAAK,IAAI,CAAC,IAAI,YAAY,EAAE;AACtC,YAAY,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ;AACvC,cAAc,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/C;AACA,cAAc,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,WAAW;AACX,UAAU,YAAY,GAAG,EAAE,CAAC;AAC5B,SAAS;AACT,QAAQ,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;AAC3D,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO;AAClC,UAAU,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,QAAQ,EAAE,GAAG,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7C,QAAQ,IAAI,SAAS,GAAG,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAC5E,QAAQ,IAAI,SAAS,GAAG,EAAE;AAC1B,UAAU,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;AAC5C,OAAO;AACP,KAAK;AACL,IAAI,IAAI,YAAY,CAAC,MAAM,EAAE;AAC7B,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzE,MAAM,IAAI,YAAY,CAAC,MAAM;AAC7B,QAAQ,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzD,KAAK;AACL,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9F,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE;AACvB,IAAI,IAAI,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AACtC,IAAI,IAAI,QAAQ,GAAG,CAAC;AACpB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;AAClF,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC3G,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC;AACvD,IAAI,IAAI,QAAQ,GAAG,MAAM;AACzB,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,GAAG,QAAQ,EAAE,EAAE,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC;AACrF,IAAI,KAAK,IAAI,KAAK,GAAG,IAAI,EAAE,EAAE,CAAC,QAAQ,EAAE,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE;AAC1F,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;AACvB,MAAM,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC;AAC3C,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE;AACpE,UAAU,CAAC,EAAE,CAAC;AACd,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACxE,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO;AAClC,UAAU,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,QAAQ,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AAClF,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC;AACtB,QAAQ,MAAM;AACd,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,KAAK;AAClB,UAAU,WAAW,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;AAC7D,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO;AAClC,UAAU,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,QAAQ,IAAI,SAAS,GAAG,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/F,QAAQ,IAAI,SAAS,GAAG,OAAO;AAC/B,UAAU,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;AACjD,OAAO;AACP,KAAK;AACL,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAC7G,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE;AACvB,IAAI,IAAI,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AAClC,IAAI,IAAI,IAAI,GAAG,CAAC;AAChB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/C,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACrF,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AACnC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE;AAC3B,IAAI,IAAI,gBAAgB,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC;AAC7C,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC;AACvC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;AAClB,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC1C,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE;AACvB,IAAI,IAAI,IAAI,GAAG,YAAY,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;AAC7C,IAAI,IAAI,IAAI,GAAG,CAAC;AAChB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU;AACxC,MAAM,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAChE,IAAI,IAAI,OAAO,GAAG,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AACpD,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5E,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AACvF,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACjC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE;AACxB,IAAI,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;AAC9C,IAAI,IAAI,IAAI,GAAG,CAAC;AAChB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW;AACzC,MAAM,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACjG,IAAI,IAAI,OAAO,GAAG,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AACvD,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5E,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;AACvF,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AACjC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE;AACvB,IAAI,IAAI,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;AAClC,IAAI,IAAI,IAAI,GAAG,CAAC;AAChB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,SAAS,GAAG,GAAG,CAAC;AAClD,IAAI,IAAI,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC;AACzF,IAAI,OAAO,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI;AACtE,MAAM,KAAK,EAAE,CAAC;AACd,IAAI,IAAI,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACtF,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,aAAa,CAAC,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAC7J,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;AAChC,MAAM,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,GAAG,EAAE,UAAU,GAAG,GAAG,CAAC,CAAC;AAChE,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;AAC/E,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;AAClB,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC3B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE;AACtB,IAAI,IAAI,IAAI,GAAG,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;AAC5C,IAAI,IAAI,IAAI,GAAG,CAAC;AAChB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,IAAI,IAAI,KAAK,GAAG,EAAE,EAAE,QAAQ,GAAG,GAAG,IAAI,SAAS,CAAC;AAChD,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE,EAAE;AAClD,MAAM,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE;AACxC,QAAQ,QAAQ,GAAG,KAAK,CAAC;AACzB,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO;AAChC,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,QAAQ;AAChB,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC;AACpB,IAAI,IAAI,QAAQ,GAAG,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,GAAG,GAAG,IAAI,aAAa,GAAG,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,SAAS,CAAC;AACnI,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;AAC9B,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AACxF,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,aAAa,EAAE,KAAK,CAAC;AACvB;AACA,CAAC,CAAC;AACF,MAAM,mBAAmB,CAAC;AAC1B,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AACjB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AAC3B,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;AACxB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AACrD,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvC,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM;AAC9C,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAC7C,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE;AACnB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM;AACxG,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC1D,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAC1B,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC9F,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC;AACtC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,IAAI,KAAK,KAAK;AACtB,MAAM,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACtB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,OAAO,CAAC,OAAO,EAAE;AACnB,IAAI,WAAW;AACf,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,EAAE;AAC5B,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,OAAO,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE;AAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAChF,UAAU,OAAO,CAAC,CAAC,CAAC;AACpB,QAAQ,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;AAC9C,UAAU,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7F,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;AACnB,OAAO,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE;AAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,EAAE,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACxF,UAAU,OAAO,CAAC,CAAC,CAAC;AACpB,OAAO,MAAM,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,EAAE;AAClC,QAAQ,IAAI,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;AACzD,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAC7B,UAAU,IAAI,KAAK,GAAG,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAChE,UAAU,IAAI,KAAK,EAAE;AACrB,YAAY,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AACnE,YAAY,IAAI,QAAQ,GAAG,CAAC,EAAE;AAC9B,cAAc,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACpC,cAAc,GAAG,GAAG,QAAQ,CAAC;AAC7B,aAAa;AACb,WAAW;AACX,SAAS;AACT,QAAQ,IAAI,CAAC,GAAG;AAChB,UAAU,GAAG,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3C,QAAQ,OAAO,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AAC1D,OAAO,MAAM;AACb,QAAQ,OAAO,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1C,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC;AACD,SAAS,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;AAC5B,EAAE,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;AACnC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACpC,IAAI,IAAI,IAAI,IAAI,EAAE;AAClB,MAAM,MAAM;AACZ,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AACpB,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,MAAM,mBAAmB,CAAC;AAC1B,EAAE,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AAC3B,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAChF,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzB,IAAI,IAAI,SAAS,GAAG,CAAC;AACrB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,aAAa,GAAG,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC,CAAC;AAChG,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;AAClB,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,WAAW,EAAE,EAAE;AACtH,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC;AACxD,MAAM,aAAa;AACnB,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,CAAC;AACD,MAAM,iBAAiB,GAAG;AAC1B,EAAE,aAAa,CAAC,CAAC,EAAE,IAAI,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACnF,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,OAAO,IAAI,mBAAmB,EAAE,CAAC;AACrC,GAAG;AACH,CAAC,CAAC;AACF,MAAM,cAAc,GAAG;AACvB,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;AAC/C,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,aAAa,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;AAChD,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,gBAAgB,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;AACnD,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC;AAC9C,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;AAC5C,MAAM,YAAY,CAAC;AACnB;AACA;AACA;AACA,EAAE,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;AACjD,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;AAC1B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,iBAAiB,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACvD,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACpB,IAAI,IAAI,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AAC3C,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACpF,IAAI,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/E,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,IAAI,cAAc,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC;AACpF,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;AACpB,GAAG;AACH,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS;AACzE,MAAM,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AAC3B,IAAI,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;AACxB,IAAI,WAAW;AACf,MAAM,KAAK,IAAI,KAAK,GAAG,CAAC,MAAM;AAC9B,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC7F,QAAQ,OAAO,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE;AAC9F,UAAU,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC;AAC3C,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AACtD,SAAS;AACT,QAAQ,IAAI,CAAC,IAAI;AACjB,UAAU,MAAM;AAChB,QAAQ,IAAI,CAAC,aAAa,EAAE,CAAC;AAC7B,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM;AACrC,QAAQ,MAAM;AACd,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC1B,QAAQ,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;AAC1D,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,KAAK;AACT,MAAM,WAAW;AACjB,QAAQ,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;AACjD,UAAU,IAAI,IAAI,EAAE;AACpB,YAAY,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC1C,YAAY,IAAI,MAAM,IAAI,KAAK,EAAE;AACjC,cAAc,IAAI,MAAM,IAAI,IAAI;AAChC,gBAAgB,OAAO,IAAI,CAAC;AAC5B,cAAc,IAAI,CAAC,OAAO,EAAE,CAAC;AAC7B,cAAc,SAAS,KAAK,CAAC;AAC7B,aAAa;AACb,WAAW;AACX,QAAQ,MAAM;AACd,OAAO;AACP,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACnF,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB;AAClD,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,IAAI,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACxC,QAAQ,IAAI,OAAO;AACnB,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACrC,OAAO;AACP,IAAI,KAAK;AACT,MAAM,OAAO,IAAI,CAAC,QAAQ,EAAE,EAAE;AAC9B,QAAQ,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;AACxC,UAAU,MAAM;AAChB,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;AAC/C,UAAU,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;AACnD,YAAY,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACtC,cAAc,MAAM,KAAK,CAAC;AAC1B,SAAS;AACT,QAAQ,KAAK,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO;AACxC,UAAU,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAChD,YAAY,OAAO,IAAI,CAAC;AACxB,QAAQ,IAAI,CAAC,OAAO,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;AAC5C,QAAQ,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO;AAClC,UAAU,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7B,OAAO;AACP,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,MAAM,CAAC,GAAG,EAAE;AACd,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG;AACtD,MAAM,MAAM,IAAI,UAAU,CAAC,8BAA8B,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AACzB,GAAG;AACH,EAAE,aAAa,CAAC,KAAK,EAAE;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,GAAG,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;AAClI,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,KAAK;AACd,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,CAAC,iBAAiB,IAAI,KAAK,CAAC;AACpC,IAAI,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AACtB,IAAI,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,EAAE,EAAE;AAC1C,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AACvB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC/B,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACxB,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;AACrC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC;AAC7D,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAC5C,IAAI,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,EAAE,EAAE;AACzC,MAAM,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,CAAC;AACpD,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACxB,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC;AACvB,MAAM,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AACxD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;AACxB,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtB,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC;AACxD,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE;AAC1G,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,MAAM,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;AAC/F,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE;AAClB,IAAI,IAAI,CAAC,GAAG,cAAc,CAAC;AAC3B,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;AAClB,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,EAAE,EAAE;AAC1B,MAAM,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;AAClB,KAAK,MAAM;AACX,MAAM,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACvC,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7B,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,iBAAiB,EAAE,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACtE,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,EAAE;AAC/C,UAAU,MAAM,EAAE,CAAC;AACnB,UAAU,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;AAClD,UAAU,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACjD,UAAU,CAAC,CAAC,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC;AAC1C,UAAU,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,GAAG,KAAK,CAAC;AACpF,UAAU,UAAU,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7C,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAC/E,IAAI,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;AAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACrB,IAAI,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE;AACzD,MAAM,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACxF,MAAM,IAAI,CAAC,OAAO;AAClB,QAAQ,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AACpE,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;AAClC,QAAQ,MAAM;AACd,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AACrB,KAAK;AACL,GAAG;AACH,EAAE,WAAW,CAAC,GAAG,EAAE;AACnB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;AAC3C,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;AAChC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACnC,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AACjD,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC;AACtC,KAAK;AACL,IAAI,OAAO,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC;AAC7E,GAAG;AACH;AACA;AACA;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AAC5D,GAAG;AACH;AACA;AACA;AACA,EAAE,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE;AACvC,IAAI,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,GAAG,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE;AACzC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACnE,GAAG;AACH;AACA;AACA;AACA,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE;AAC3B,IAAI,IAAI,OAAO,KAAK,IAAI,QAAQ;AAChC,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,CAAC;AACtI,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACvD,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,IAAI,EAAE;AACnB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACvF,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE;AAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AAClJ,GAAG;AACH;AACA;AACA;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAChD,IAAI,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;AACrB,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;AAChC,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC3B,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAChF,GAAG;AACH,EAAE,OAAO,CAAC,IAAI,EAAE;AAChB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;AACjI,GAAG;AACH;AACA;AACA;AACA,EAAE,UAAU,CAAC,IAAI,EAAE;AACnB,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,CAAC,OAAO;AACpC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;AACpC,QAAQ,OAAO;AACf,IAAI,IAAI,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5F,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AACzH,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;AAChC,IAAI,IAAI,OAAO,IAAI,IAAI,QAAQ;AAC/B,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;AACpE,IAAI,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACvC,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC3C,GAAG;AACH,CAAC;AACD,SAAS,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;AAC3D,EAAE,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;AACnC,EAAE,IAAI,QAAQ,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;AAChE,EAAE,SAAS,YAAY,CAAC,IAAI,EAAE,SAAS,EAAE;AACzC,IAAI,OAAO,SAAS,GAAG,IAAI,IAAI,QAAQ,GAAG,IAAI,GAAG,QAAQ,EAAE;AAC3D,MAAM,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,QAAQ,CAAC;AACpD,MAAM,MAAM,IAAI,IAAI,CAAC;AACrB,MAAM,IAAI,IAAI,IAAI,CAAC;AACnB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;AACnC,KAAK;AACL,GAAG;AACH,EAAE,KAAK,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,WAAW,EAAE;AAC1D,IAAI,YAAY,CAAC,EAAE,CAAC,IAAI,GAAG,MAAM,EAAE,IAAI,CAAC,CAAC;AACzC,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,GAAG,MAAM,EAAE,IAAI,EAAE,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACpE,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI,GAAG,KAAK,CAAC;AACnB,KAAK,MAAM,IAAI,EAAE,CAAC,EAAE,GAAG,MAAM,GAAG,QAAQ,EAAE;AAC1C,MAAM,IAAI,GAAG,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC7D,MAAM,YAAY,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,EAAE,KAAK,CAAC,CAAC;AAC1C,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AACzB,KAAK;AACL,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxB,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;AACjC,GAAG;AACH,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,KAAK,CAAC,CAAC;AACxC,EAAE,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,KAAK,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC;AACvH,CAAC;AACD,MAAM,cAAc,SAAS,MAAM,CAAC;AACpC;AACA;AACA;AACA,EAAE,WAAW,CAAC,OAAO,EAAE,YAAY,EAAE,gBAAgB,EAAE,UAAU,EAAE,YAAY,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE;AAC1I,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACrC,IAAI,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AAC7C,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACrC,IAAI,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC/C,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,IAAI,IAAI,CAAC,SAAS,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzD,IAAI,KAAK,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK;AAC/B,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;AACpC,GAAG;AACH,EAAE,WAAW,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE;AACxC,IAAI,IAAI,KAAK,GAAG,IAAI,YAAY,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AACjE,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ;AAC/B,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,IAAI,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,MAAM;AACf,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,GAAG,IAAI,CAAC;AAC9C,IAAI,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,EAAE,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,EAAE,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjS,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AACtC,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,iBAAiB,CAAC,CAAC;AAC/D,MAAM,IAAI,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,MAAM,CAAC;AACrD,MAAM,KAAK,IAAI,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE;AACxC,QAAQ,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,IAAI,QAAQ,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;AACvF,QAAQ,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC;AAClD,UAAU,SAAS;AACnB,QAAQ,IAAI,SAAS;AACrB,UAAU,iBAAiB,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,KAAK,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;AACjG,QAAQ,IAAI,EAAE,GAAG,UAAU,CAAC,MAAM,CAAC;AACnC,QAAQ,IAAI,KAAK,GAAG,SAAS,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,WAAW,IAAI,EAAE,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;AAC/L,QAAQ,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AACxC,UAAU,EAAE;AACZ,UAAU,IAAI;AACd,UAAU,KAAK,EAAE,KAAK,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACnD,SAAS,CAAC,CAAC,CAAC;AACZ,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,IAAI,CAAC,MAAM;AACrB,YAAY,MAAM,GAAG,EAAE,CAAC;AACxB,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,GAAG;AAC1D,YAAY,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AACjC;AACA,YAAY,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,SAAS;AACT,OAAO;AACP,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC;AACxC,MAAM,IAAI,MAAM;AAChB,QAAQ,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;AAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAChD,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;AACjC,MAAM,KAAK,IAAI,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE;AACpC,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACvF,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC;AACtB,UAAU,YAAY,CAAC,KAAK,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AACjE,QAAQ,IAAI,MAAM,GAAG,CAAC,CAAC;AACvB,UAAU,aAAa,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;AACzC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;AACrC,MAAM,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,UAAU,EAAE;AAC3C,QAAQ,IAAI,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACnD,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACxB,UAAU,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;AAC5C,UAAU,gBAAgB,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC;AAC/C,SAAS,MAAM;AACf,UAAU,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,UAAU,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AACpJ,UAAU,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACnD,UAAU,gBAAgB,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;AACtD,UAAU,UAAU,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;AAChD,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,OAAO;AACzB,UAAU,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC3C,OAAO;AACP,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AACtC,MAAM,KAAK,IAAI,KAAK,IAAI,MAAM,CAAC,WAAW,EAAE;AAC5C,QAAQ,IAAI,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACpD,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACxB,UAAU,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;AAC7C,SAAS,MAAM;AACf,UAAU,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;AACvJ,UAAU,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACpD,UAAU,WAAW,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;AACjD,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,IAAI;AACnB,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,OAAO,IAAI,cAAc,CAAC,OAAO,EAAE,YAAY,EAAE,gBAAgB,EAAE,UAAU,EAAE,YAAY,EAAE,iBAAiB,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;AAC1J,GAAG;AACH;AACA;AACA;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACrC,IAAI,IAAI,KAAK,IAAI,IAAI;AACrB,MAAM,MAAM,IAAI,UAAU,CAAC,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE;AAC5B,IAAI,IAAI,EAAE,GAAG,IAAI,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AACnD,IAAI,KAAK;AACT,MAAM,KAAK,IAAI,GAAG,GAAG,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI;AAC7C,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChC,QAAQ,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,aAAa;AAC5C,UAAU,IAAI,KAAK,EAAE;AACrB,YAAY,IAAI,MAAM,GAAG,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;AAC9C,YAAY,IAAI,MAAM,IAAI,CAAC,EAAE;AAC7B,cAAc,GAAG,GAAG,MAAM,CAAC;AAC3B,cAAc,SAAS,KAAK,CAAC;AAC7B,aAAa;AACb,WAAW;AACX,QAAQ,GAAG,EAAE,CAAC;AACd,OAAO;AACP,IAAI,OAAO,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AAChC,GAAG;AACH,CAAC;AACD,SAAS,QAAQ,CAAC,CAAC,EAAE;AACrB,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AACnC,CAAC;AACD,SAAS,aAAa,CAAC,IAAI,EAAE;AAC7B,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC1B,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AACtB,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,EAAE,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AACtB,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,IAAI,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1C,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;AACpB,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC;AACxB,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;AACrD,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;AAC3C,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;AACvC,IAAI,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC;AACzD,IAAI,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC;AACtD,IAAI,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC;AACzD,IAAI,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC;AAC1C,IAAI,IAAI,EAAE,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,KAAK,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;AACxJ,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE;AAC/B,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAClC,EAAE,IAAI,KAAK,GAAG,CAAC;AACf,IAAI,MAAM,IAAI,UAAU,CAAC,CAAC,8CAA8C,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAClF,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD,IAAI,SAAS,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;AAC3C,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;AACjC,IAAI,EAAE,EAAE,CAAC;AACT,IAAI,IAAI;AACR,IAAI,KAAK,EAAE,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,iBAAiB,GAAG,CAAC,OAAO,EAAE,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC;AAClI,IAAI,GAAG,EAAE,IAAI,IAAI,UAAU;AAC3B,GAAG,CAAC,CAAC;AACL,CAAC;AACD,MAAM,IAAI,GAAG,EAAE,CAAC;AAChB,MAAM,MAAM,CAAC;AACb,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,GAAG;AACH,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,GAAG,CAAC,EAAE;AACtC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC;AACxD,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE;AAClC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI;AACtB,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC9B,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,MAAM,MAAM,EAAE,IAAI,CAAC,OAAO;AAC1B,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,MAAM,EAAE,IAAI,CAAC,KAAK;AACxB,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,MAAM;AACZ,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACD,MAAM,OAAO,CAAC;AACd;AACA;AACA;AACA,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,GAAG,IAAI,EAAE;AAC/C,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA,EAAE,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE;AACvB,IAAI,IAAI,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC;AACtC,IAAI,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC7C,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC;AACzG,GAAG;AACH;AACA;AACA;AACA,EAAE,MAAM,CAAC,OAAO,EAAE;AAClB,IAAI,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/G,GAAG;AACH,CAAC;AACD,MAAM,WAAW,CAAC;AAClB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG;AACH,EAAE,IAAI,EAAE,GAAG;AACX,IAAI,OAAO,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AACxC,GAAG;AACH,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AAC7B,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE;AACvB,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,GAAG,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AACrF,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;AACvC,EAAE,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;AAC/C,CAAC;AACD,MAAM,kBAAkB,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;AACzE,MAAM,gBAAgB,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;AACvE,MAAM,SAAS,GAAG,EAAE,EAAE,UAAU,GAAG,EAAE,CAAC;AACtC,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE;AACpC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG;AACH,CAAC;AACD,MAAM,SAAS,GAAG,oCAAoC,CAAC;AACvD,IAAI,WAAW,GAAG,0DAA0D,CAAC;AAC7E,IAAI;AACJ,EAAE,WAAW,GAAG,IAAI,MAAM,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;AACnD,CAAC,CAAC,OAAO,CAAC,EAAE;AACZ,CAAC;AACD,MAAM,aAAa,GAAG;AACtB,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAC1B,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC;AACzC,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACrC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE;AAC7C,MAAM,IAAI,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,OAAO;AAC5C,QAAQ,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7D,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,GAAG;AACH,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAC1B,IAAI,IAAI,IAAI,IAAI,EAAE;AAClB,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,CAAC,GAAG,4BAA4B,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;AAC/E,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAChF,GAAG;AACH,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAC9B,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE;AACvD,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC;AACxB,IAAI,OAAO,GAAG,GAAG,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;AAC7C,MAAM,GAAG,EAAE,CAAC;AACZ,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,KAAK,EAAE,OAAO,GAAG,CAAC,CAAC;AACxC,IAAI,OAAO,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;AAChC,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE;AAC9B,QAAQ,OAAO,EAAE,CAAC;AAClB,QAAQ,IAAI,OAAO,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE;AACrD,UAAU,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,EAAE;AAChE,YAAY,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI,CAAC;AACnD,YAAY,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,GAAG,CAAC,GAAG,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC;AACvD,WAAW,CAAC,CAAC,CAAC;AACd,OAAO,MAAM;AACb,QAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,GAAG;AACH,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAC3B,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,EAAE,CAAC,GAAG,GAAG,CAAC;AACzC,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;AAC5C,IAAI,IAAI,GAAG,GAAG,qIAAqI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChK,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;AAC5E,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC;AAC5C;AACA,QAAQ,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACvD,QAAQ,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC5E,OAAO,CAAC,CAAC,CAAC;AACV,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,8BAA8B,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7D,IAAI,IAAI,OAAO;AACf,MAAM,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAChF,IAAI,IAAI,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7C,IAAI,IAAI,QAAQ;AAChB,MAAM,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC/F,IAAI,IAAI,CAAC,GAAG,kKAAkK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3L,IAAI,IAAI,CAAC,CAAC;AACV,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AACxE,GAAG;AACH,EAAE,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAC5B,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE;AAChC,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC;AACxB,IAAI,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI;AAC/B,MAAM,GAAG,EAAE,CAAC;AACZ,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC5E,IAAI,IAAI,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7E,IAAI,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrE,IAAI,IAAI,YAAY,GAAG,CAAC,MAAM,KAAK,CAAC,MAAM,IAAI,OAAO,IAAI,OAAO,CAAC,CAAC;AAClE,IAAI,IAAI,aAAa,GAAG,CAAC,OAAO,KAAK,CAAC,OAAO,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC;AACnE,IAAI,IAAI,OAAO,GAAG,YAAY,KAAK,IAAI,IAAI,EAAE,IAAI,CAAC,aAAa,IAAI,OAAO,CAAC,CAAC;AAC5E,IAAI,IAAI,QAAQ,GAAG,aAAa,KAAK,IAAI,IAAI,EAAE,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,CAAC;AAC5E,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,IAAI,IAAI,EAAE,GAAG,kBAAkB,GAAG,gBAAgB,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAClJ,GAAG;AACH,EAAE,SAAS,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAC7B,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE;AAC9C,MAAM,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9D,IAAI,IAAI,IAAI,IAAI,EAAE,EAAE;AACpB,MAAM,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC,CAAC;AAC1B,MAAM,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;AAC/B,QAAQ,GAAG,EAAE,CAAC;AACd,MAAM,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC;AAChD,QAAQ,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,GAAG;AACH,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AACxB,IAAI,OAAO,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,eAAe;AACrD,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,CAAC;AACP;AACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACZ,GAAG;AACH,EAAE,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AACzB,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,eAAe;AACjF,MAAM,UAAU;AAChB,MAAM,KAAK;AACX,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,CAAC;AACP;AACA,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACZ,GAAG;AACH,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAC3B,IAAI,IAAI,IAAI,IAAI,EAAE;AAClB,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,IAAI,YAAY,eAAe,KAAK,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC,EAAE;AAClG,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;AAC3G,UAAU,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC7B,UAAU,OAAO,CAAC,CAAC,CAAC;AACpB,SAAS;AACT,QAAQ,IAAI,OAAO,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACxC,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAChI,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,SAAS;AAClC,UAAU,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACtC,YAAY,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChC,YAAY,IAAI,CAAC,YAAY,eAAe,IAAI,CAAC,CAAC,IAAI,IAAI,SAAS;AACnE,cAAc,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AACzB,WAAW;AACX,QAAQ,OAAO,IAAI,CAAC,EAAE,CAAC;AACvB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,GAAG;AACH,CAAC,CAAC;AACF,SAAS,UAAU,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE;AACxD,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,QAAQ,CAAC;AACjE,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnF,EAAE,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC3D,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE;AAClB,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AACzC,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC;AACjE,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,EAAE;AAC1B,QAAQ,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,GAAG,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;AACjE,QAAQ,IAAI,KAAK;AACjB,UAAU,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACvC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE;AAC5B,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D,MAAM,MAAM,GAAG,GAAG,GAAG,CAAC,CAAC;AACvB,MAAM,IAAI,IAAI;AACd,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,MAAM,IAAI,KAAK;AACf,QAAQ,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5B,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC;AACpD,KAAK;AACL,GAAG,MAAM,IAAI,IAAI,IAAI,EAAE,EAAE;AACzB,IAAI,IAAI,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,QAAQ,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC7E,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1B,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC;AACxB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC3C,CAAC;AACD,SAAS,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE;AACvC,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACpC,EAAE,IAAI,IAAI,IAAI,EAAE,EAAE;AAClB,IAAI,KAAK,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;AACxD,MAAM,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACpC,MAAM,IAAI,EAAE,IAAI,EAAE;AAClB,QAAQ,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AAC/D,MAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAC9B,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,MAAM;AACT,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC;AAC/B,IAAI,KAAK,IAAI,OAAO,GAAG,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;AACxD,MAAM,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACpC,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE;AACrB,QAAQ,MAAM;AACd,OAAO,MAAM,IAAI,OAAO,EAAE;AAC1B,QAAQ,OAAO,GAAG,KAAK,CAAC;AACxB,OAAO,MAAM,IAAI,EAAE,IAAI,EAAE,EAAE;AAC3B,QAAQ,KAAK,EAAE,CAAC;AAChB,OAAO,MAAM,IAAI,EAAE,IAAI,EAAE,EAAE;AAC3B,QAAQ,IAAI,CAAC,KAAK;AAClB,UAAU,MAAM;AAChB,QAAQ,KAAK,EAAE,CAAC;AAChB,OAAO,MAAM,IAAI,EAAE,IAAI,EAAE,EAAE;AAC3B,QAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,MAAM,EAAE,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;AACzG,GAAG;AACH,CAAC;AACD,SAAS,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE;AAC7C,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACpC,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE;AAC5C,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,GAAG,GAAG,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AACnC,EAAE,KAAK,IAAI,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE;AACvE,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,OAAO;AACf,MAAM,OAAO,GAAG,KAAK,CAAC;AACtB,SAAS,IAAI,EAAE,IAAI,GAAG;AACtB,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AACnE,SAAS,IAAI,EAAE,IAAI,EAAE;AACrB,MAAM,OAAO,GAAG,IAAI,CAAC;AACrB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,SAAS,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE;AAC3D,EAAE,KAAK,IAAI,OAAO,GAAG,KAAK,EAAE,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;AACvG,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAClC,IAAI,IAAI,OAAO;AACf,MAAM,OAAO,GAAG,KAAK,CAAC;AACtB,SAAS,IAAI,EAAE,IAAI,EAAE;AACrB,MAAM,OAAO,YAAY,GAAG,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC;AAC1F,SAAS;AACT,MAAM,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;AACpC,QAAQ,YAAY,GAAG,KAAK,CAAC;AAC7B,MAAM,IAAI,EAAE,IAAI,EAAE;AAClB,QAAQ,OAAO,KAAK,CAAC;AACrB,WAAW,IAAI,EAAE,IAAI,EAAE;AACvB,QAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,MAAM,aAAa,CAAC;AACpB;AACA;AACA;AACA,EAAE,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE;AACrC,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,GAAG,EAAE;AACZ,IAAI,OAAO,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1E,GAAG;AACH;AACA;AACA;AACA,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE;AAClB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AACjE,GAAG;AACH;AACA;AACA;AACA,EAAE,MAAM,CAAC,IAAI,EAAE;AACf,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAC5C,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9F,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,IAAI,IAAI,YAAY,eAAe,KAAK,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC;AAChG,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA,EAAE,UAAU,CAAC,IAAI,EAAE;AACnB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,IAAI,EAAE;AACvB,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI,EAAE,KAAK,YAAY,eAAe,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;AACrF,QAAQ,SAAS;AACjB,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,kBAAkB,IAAI,KAAK,CAAC,IAAI,IAAI,gBAAgB,CAAC;AACnF,MAAM,IAAI,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;AAC5C,MAAM,IAAI,IAAI,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC1B,MAAM,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE;AAC7B,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACjC,QAAQ,IAAI,IAAI,YAAY,eAAe,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI;AACvF,QAAQ,EAAE,GAAG,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE;AACnJ,UAAU,IAAI,GAAG,IAAI,CAAC;AACtB,UAAU,MAAM;AAChB,SAAS;AACT,OAAO;AACP,MAAM,IAAI,CAAC,IAAI;AACf,QAAQ,SAAS;AACjB,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,CAAC;AAClD,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC;AAC5C,MAAM,IAAI,GAAG,EAAE;AACf,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC/D,QAAQ,KAAK,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;AAC/B,QAAQ,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AAChC,QAAQ,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,UAAU,GAAG,gBAAgB,CAAC;AACzD,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;AACxB,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC/D,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACtC,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,OAAO;AAC5C,UAAU,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACtC,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAC7B,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI;AACzB,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AACjE,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC;AACxD,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AACrH,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,EAAE,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAC5H,MAAM,IAAI,IAAI;AACd,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AACzC;AACA,QAAQ,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,IAAI,IAAI,YAAY,OAAO;AACjC,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,oBAAoB,CAAC,IAAI,EAAE;AAC7B,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,IAAI,IAAI,YAAY,eAAe,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI;AAC9D,QAAQ,OAAO,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,UAAU,EAAE;AAC1B,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC;AACnC,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,IAAI,EAAE;AAClB,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAClE,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE;AAChC,IAAI,IAAI,OAAO,IAAI,IAAI,QAAQ;AAC/B,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;AACpE,IAAI,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACvC,GAAG;AACH,CAAC;AACD,SAAS,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE;AACtC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM;AACnB,IAAI,OAAO,QAAQ,CAAC;AACpB,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;AACtB,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;AACtC,EAAE,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;AAC1B,IAAI,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;AACpD,MAAM,EAAE,EAAE,CAAC;AACX,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;AACvD,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,CAAC;AACvB,MAAM,IAAI,CAAC,YAAY,OAAO;AAC9B,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtF,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACjC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACnF,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE;AAChC,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;AACf,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,SAAS,CAAC,MAAM;AACxB,MAAM,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;AACrF,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AAC1B,GAAG;AACH,EAAE,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,GAAG;AACnD,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAClE,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;AAC9B,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;AACjC,MAAM,OAAO,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI;AAC7D,QAAQ,GAAG,EAAE,CAAC;AACd,MAAM,IAAI,CAAC,WAAW,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,IAAI,IAAI,CAAC,CAAC,EAAE;AACZ,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACpD,MAAM,CAAC,CAAC,UAAU,EAAE,CAAC;AACrB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1C,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,IAAI;AACvB,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;AACrB,QAAQ,OAAO,KAAK,CAAC;AACrB,IAAI,WAAW;AACf,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI,IAAI;AACxB,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,SAAS,CAAC;AAC/C,MAAM,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC;AAC7B,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,IAAI,EAAE;AAChB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AAChC,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;AAC3D,GAAG;AACH,EAAE,SAAS,CAAC,EAAE,EAAE;AAChB,IAAI,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACpH,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,iBAAiB,EAAE,GAAG,GAAG,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;AACrF,IAAI,IAAI,OAAO,GAAG,GAAG,EAAE,KAAK,GAAG,MAAM,CAAC;AACtC,IAAI,WAAW;AACf,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,GAAG,GAAG,OAAO,EAAE;AAClC,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,IAAI,GAAG,CAAC,UAAU,EAAE;AACpD,UAAU,SAAS;AACnB,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;AACtD,MAAM,IAAI,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE;AACnD,QAAQ,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAClC,OAAO,MAAM;AACb,QAAQ,IAAI,KAAK,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpG,QAAQ,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;AAClD,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC/B,OAAO;AACP,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;AAChC,QAAQ,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;AAC9C,UAAU,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;AAC7B,UAAU,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC5C,SAAS,MAAM;AACf,UAAU,GAAG,GAAG,OAAO,CAAC;AACxB,UAAU,MAAM,GAAG,KAAK,CAAC;AACzB,UAAU,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC;AACjC,UAAU,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3C,SAAS;AACT,OAAO;AACP,MAAM,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE;AAC5B,QAAQ,MAAM;AACd,KAAK;AACL,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,EAAE;AAC9C,MAAM,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;AAC9B,MAAM,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,GAAG,GAAG,KAAK,CAAC;AACvB,GAAG;AACH,CAAC;AACD,SAAS,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE;AACjC,EAAE,IAAI,GAAG,GAAG,GAAG,CAAC;AAChB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,IAAI,IAAI,OAAO,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAC3D,IAAI,IAAI,OAAO,GAAG,GAAG;AACrB,MAAM,GAAG,IAAI,KAAK,GAAG,OAAO,CAAC;AAC7B,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,MAAM,oBAAoB,GAAG,SAAS,CAAC;AACvC,EAAE,gBAAgB,EAAE,IAAI,CAAC,KAAK;AAC9B,EAAE,cAAc,EAAE,IAAI,CAAC,gBAAgB;AACvC,EAAE,oCAAoC,EAAE,IAAI,CAAC,QAAQ;AACrD,EAAE,oCAAoC,EAAE,IAAI,CAAC,QAAQ;AACrD,EAAE,iBAAiB,EAAE,IAAI,CAAC,QAAQ;AAClC,EAAE,iBAAiB,EAAE,IAAI,CAAC,QAAQ;AAClC,EAAE,iBAAiB,EAAE,IAAI,CAAC,QAAQ;AAClC,EAAE,iBAAiB,EAAE,IAAI,CAAC,QAAQ;AAClC,EAAE,sBAAsB,EAAE,IAAI,CAAC,OAAO;AACtC,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM;AACrB,EAAE,MAAM,EAAE,IAAI,CAAC,SAAS;AACxB,EAAE,cAAc,EAAE,IAAI,CAAC,QAAQ;AAC/B,EAAE,oBAAoB,EAAE,IAAI,CAAC,MAAM;AACnC,EAAE,oBAAoB,EAAE,IAAI,CAAC,IAAI;AACjC,EAAE,gCAAgC,EAAE,IAAI,CAAC,IAAI;AAC7C,EAAE,gBAAgB,EAAE,IAAI,CAAC,KAAK;AAC9B,EAAE,qBAAqB,EAAE,IAAI,CAAC,SAAS;AACvC,EAAE,cAAc,EAAE,IAAI,CAAC,GAAG;AAC1B,EAAE,wEAAwE,EAAE,IAAI,CAAC,qBAAqB;AACtG,EAAE,oBAAoB,EAAE,IAAI,CAAC,SAAS;AACtC,EAAE,SAAS,EAAE,IAAI,CAAC,MAAM;AACxB,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO;AACzB,CAAC,CAAC,CAAC;AACH,MAAM,MAAM,GAAG,IAAI,cAAc,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,cAAc,EAAE,iBAAiB,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC;AAC7X,SAAS,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;AACvC,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE;AACjE,IAAI,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;AAClC,IAAI,IAAI,OAAO,GAAG,GAAG;AACrB,MAAM,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,CAAC;AACV,MAAM,MAAM;AACZ,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC;AACf,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,SAAS,SAAS,CAAC,MAAM,EAAE;AAC3B,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,MAAM,CAAC;AAC1C,EAAE,IAAI,IAAI,GAAG,UAAU,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;AACzC,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AAC1B,IAAI,IAAI,UAAU,KAAK,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE;AACvE,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC;AACpB,MAAM,IAAI,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE;AACjC,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzD,QAAQ,IAAI,QAAQ;AACpB,UAAU,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;AACxD,OAAO;AACP,MAAM,IAAI,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;AACrC,MAAM,IAAI,OAAO;AACjB,QAAQ,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;AACvF,KAAK,MAAM,IAAI,UAAU,KAAK,EAAE,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;AAC3E,MAAM,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3F,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAClB,CAAC;AACD,MAAM,kBAAkB,GAAG,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC;AACnF,MAAM,aAAa,GAAG;AACtB,EAAE,WAAW,EAAE,CAAC;AAChB,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,KAAK,EAAE,EAAE,mBAAmB,EAAE,IAAI,CAAC,aAAa,EAAE;AACtD,GAAG,EAAE;AACL,IAAI,IAAI,EAAE,mBAAmB;AAC7B,IAAI,KAAK,EAAE,IAAI,CAAC,qBAAqB;AACrC,GAAG,CAAC;AACJ,EAAE,WAAW,EAAE,CAAC;AAChB,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AACzB,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,GAAG;AAC3E,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,MAAM,IAAI,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AAC9E,MAAM,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACvE,MAAM,IAAI,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/E,MAAM,OAAO,EAAE,CAAC,YAAY,CAAC,kBAAkB,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,MAAM,IAAI,OAAO,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO,KAAK,CAAC,OAAO,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC;AACvJ,KAAK;AACL,IAAI,KAAK,EAAE,UAAU;AACrB,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,SAAS,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE;AAC1D,EAAE,IAAI,MAAM,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,EAAE,SAAS,GAAG,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC;AAC1E,EAAE,IAAI,SAAS,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,MAAM,GAAG,SAAS,EAAE,MAAM,GAAG,OAAO,EAAE,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACpJ,GAAG,CAAC;AACJ,EAAE,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;AAC7B,MAAM,IAAI,CAAC,KAAK,IAAI,SAAS,GAAG,CAAC,CAAC;AAClC,QAAQ,MAAM,EAAE,CAAC;AACjB,MAAM,KAAK,GAAG,KAAK,CAAC;AACpB,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,IAAI,SAAS,GAAG,CAAC,CAAC;AAC1B,UAAU,SAAS,EAAE,CAAC;AACtB,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AACxE,OAAO;AACP,MAAM,SAAS,GAAG,OAAO,GAAG,CAAC,CAAC,CAAC;AAC/B,KAAK,MAAM,IAAI,GAAG,IAAI,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE;AAC/C,MAAM,IAAI,SAAS,GAAG,CAAC;AACvB,QAAQ,SAAS,GAAG,CAAC,CAAC;AACtB,MAAM,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;AACtB,KAAK;AACL,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,IAAI,IAAI,EAAE,CAAC;AAC7B,GAAG;AACH,EAAE,IAAI,SAAS,GAAG,CAAC,CAAC,EAAE;AACtB,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,IAAI,IAAI;AACZ,MAAM,SAAS,EAAE,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,SAAS,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE;AAC7B,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,IAAI,IAAI,IAAI,GAAG;AACnB,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,IAAI,IAAI,IAAI,EAAE;AAClB,MAAM,CAAC,EAAE,CAAC;AACV,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,CAAC;AACD,MAAM,aAAa,GAAG,uCAAuC,CAAC;AAC9D,MAAM,WAAW,CAAC;AAClB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG;AACH,EAAE,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AAC3B,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE;AAC3B,MAAM,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AACxB,MAAM,IAAI,QAAQ,CAAC;AACnB,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,KAAK,aAAa,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;AAChI,QAAQ,IAAI,QAAQ,GAAG,EAAE,EAAE,UAAU,GAAG,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5F,QAAQ,IAAI,UAAU,IAAI,QAAQ,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC;AAC1D,UAAU,IAAI,CAAC,IAAI,GAAG;AACtB,YAAY,EAAE,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC;AACzF,YAAY,EAAE,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;AAC9F,WAAW,CAAC;AACZ,OAAO;AACP,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;AAC1B,MAAM,IAAI,OAAO,GAAG,EAAE,CAAC;AACvB,MAAM,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC;AAC/D,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC;AAC5G,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE;AACnB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;AAClB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACtG,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AACD,MAAM,KAAK,GAAG;AACd,EAAE,WAAW,EAAE;AACf,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;AAClC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE,iBAAiB,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE;AACvE,IAAI,UAAU;AACd,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE;AAC9C,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC,qBAAqB,EAAE;AACjE,GAAG;AACH,EAAE,UAAU,EAAE,CAAC;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE;AAClB,MAAM,OAAO,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,IAAI,WAAW,EAAE,GAAG,IAAI,CAAC;AACjE,KAAK;AACL,IAAI,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AAC5B,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC;AACjG,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;AAC/B,MAAM,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AACnH,KAAK;AACL,IAAI,MAAM,EAAE,eAAe;AAC3B,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,UAAU,CAAC;AACjB,EAAE,QAAQ,GAAG;AACb,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE;AACnB,IAAI,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AACzF,MAAM,EAAE,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACtD,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACrE,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AACD,MAAM,QAAQ,GAAG;AACjB,EAAE,WAAW,EAAE;AACf,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AACnD,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAC5C,GAAG;AACH,EAAE,UAAU,EAAE,CAAC;AACf,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE;AACnB,MAAM,OAAO,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,UAAU,EAAE,CAAC,IAAI,IAAI,UAAU,GAAG,IAAI,UAAU,EAAE,GAAG,IAAI,CAAC;AAClH,KAAK;AACL,IAAI,KAAK,EAAE,eAAe;AAC1B,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,UAAU,GAAG,2DAA2D,CAAC;AAC/E,MAAM,KAAK,GAAG,iCAAiC,CAAC;AAChD,MAAM,kBAAkB,GAAG,sBAAsB,CAAC;AAClD,MAAM,OAAO,GAAG,+BAA+B,CAAC;AAChD,MAAM,cAAc,GAAG,mBAAmB,CAAC;AAC3C,SAAS,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE;AAClC,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE;AAChC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;AACpB,MAAM,MAAM,EAAE,CAAC;AACf,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,SAAS,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE;AACpC,EAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;AACzB,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,EAAE,IAAI,CAAC,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9D,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC/B,EAAE,WAAW;AACb,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;AACjC,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC;AAC3G,MAAM,GAAG,EAAE,CAAC;AACZ,SAAS,IAAI,IAAI,IAAI,GAAG,KAAK,EAAE,GAAG,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3F,MAAM,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC;AAC5B;AACA,MAAM,MAAM;AACZ,GAAG;AACH,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,SAAS,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE;AACtC,EAAE,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,EAAE,IAAI,CAAC,CAAC;AACR,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACnC,EAAE,OAAO,IAAI,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACtF,CAAC;AACD,MAAM,QAAQ,GAAG;AACjB,EAAE,WAAW,EAAE,CAAC;AAChB,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;AAC5B,MAAM,IAAI,GAAG,GAAG,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC;AACnC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;AAC5C,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,MAAM,UAAU,CAAC,SAAS,GAAG,GAAG,CAAC;AACjC,MAAM,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AACjD,MAAM,IAAI,CAAC,CAAC;AACZ,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AACxB,QAAQ,GAAG,GAAG,cAAc,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACzD,QAAQ,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE;AACxC,UAAU,IAAI,SAAS,GAAG,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;AAChF,UAAU,GAAG,GAAG,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC1C,SAAS;AACT,OAAO,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;AACvB,QAAQ,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAC7C,OAAO,MAAM;AACb,QAAQ,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAC3D,QAAQ,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,OAAO,EAAE;AACzC,UAAU,cAAc,CAAC,SAAS,GAAG,GAAG,CAAC;AACzC,UAAU,CAAC,GAAG,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAC3C,UAAU,IAAI,CAAC;AACf,YAAY,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACxC,SAAS;AACT,OAAO;AACP,MAAM,IAAI,GAAG,GAAG,CAAC;AACjB,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5D,MAAM,OAAO,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC;AAC7B,KAAK;AACL,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;AACvD,SAAS,aAAa,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AACvC,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,KAAK;AAC5B,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE;AAC5C,MAAM,OAAO,CAAC,CAAC,CAAC;AAChB,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC5C,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;AAC3C,MAAM,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,KAAK,IAAI,EAAE;AACrB,QAAQ,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5F,MAAM,IAAI,KAAK,IAAI,EAAE;AACrB,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAChD,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC;AACtB,QAAQ,MAAM;AACd,KAAK;AACL,IAAI,OAAO,CAAC,CAAC,CAAC;AACd,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,WAAW,GAAG;AACpB,EAAE,WAAW,EAAE;AACf,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AAC9D,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,IAAI,CAAC,qBAAqB,EAAE;AAClE,GAAG;AACH,EAAE,WAAW,EAAE,CAAC;AAChB,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,KAAK,EAAE,aAAa,CAAC,EAAE,EAAE,aAAa,EAAE,iBAAiB,CAAC;AAC9D,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,SAAS,GAAG;AAClB,EAAE,WAAW,EAAE;AACf,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AAC5D,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC,qBAAqB,EAAE;AAChE,GAAG;AACH,EAAE,WAAW,EAAE,CAAC;AAChB,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,KAAK,EAAE,aAAa,CAAC,GAAG,EAAE,WAAW,EAAE,eAAe,CAAC;AAC3D,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,KAAK,GAAG;AACd,EAAE,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;AACzD,EAAE,WAAW,EAAE,CAAC;AAChB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,KAAK,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AACzB,MAAM,IAAI,KAAK,CAAC;AAChB,MAAM,IAAI,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACpF,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,MAAM,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5E,KAAK;AACL,GAAG,CAAC;AACJ,CAAC,CAAC;AACF,MAAM,IAAI,mBAAmB,mBAAmB,CAAC,EAAE,aAAa,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AAC/G,MAAM,WAAW,mBAAmB,IAAI,QAAQ,EAAE,CAAC;AACnD,MAAM,UAAU,mBAAmB,MAAM,CAAC,SAAS,CAAC;AACpD,EAAE,KAAK,EAAE;AACT,oBAAoB,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AAC/C,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;AACrL,KAAK,CAAC;AACN,oBAAoB,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC;AAC9C,oBAAoB,cAAc,CAAC,GAAG,CAAC;AACvC,MAAM,QAAQ,EAAE,MAAM,IAAI;AAC1B,KAAK,CAAC;AACN,oBAAoB,gBAAgB,CAAC,GAAG,CAAC;AACzC,MAAM,QAAQ,EAAE,IAAI;AACpB,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC,CAAC;AACH,SAAS,SAAS,CAAC,IAAI,EAAE;AACzB,EAAE,IAAI,KAAK,GAAG,6BAA6B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5D,EAAE,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AACpC,CAAC;AACD,SAAS,MAAM,CAAC,IAAI,EAAE;AACtB,EAAE,OAAO,IAAI,CAAC,IAAI,IAAI,aAAa,IAAI,IAAI,CAAC,IAAI,IAAI,YAAY,CAAC;AACjE,CAAC;AACD,SAAS,cAAc,CAAC,UAAU,EAAE,KAAK,EAAE;AAC3C,EAAE,IAAI,IAAI,GAAG,UAAU,CAAC;AACxB,EAAE,WAAW;AACb,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC;AACzC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK;AAC7E,MAAM,MAAM;AACZ,IAAI,IAAI,GAAG,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;AACjB,CAAC;AACD,MAAM,YAAY,mBAAmB,WAAW,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,KAAK;AAC3E,EAAE,KAAK,IAAI,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;AACrF,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK;AACzB,MAAM,MAAM;AACZ,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9C,IAAI,IAAI,OAAO,IAAI,IAAI;AACvB,MAAM,SAAS;AACf,IAAI,IAAI,IAAI,GAAG,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7C,IAAI,IAAI,IAAI,GAAG,GAAG;AAClB,MAAM,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC;AACrC,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC,CAAC;AACH,SAAS,MAAM,CAAC,OAAO,EAAE;AACzB,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC,EAAE,UAAU,CAAC,CAAC;AACjE,CAAC;AACI,MAAC,kBAAkB,mBAAmB,MAAM,CAAC,UAAU,EAAE;AAC9D,MAAM,QAAQ,mBAAmB,UAAU,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE;AAC3F,EAAE,KAAK,EAAE;AACT,oBAAoB,YAAY,CAAC,GAAG,CAAC;AACrC,MAAM,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC;AACrF,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC,CAAC,CAAC;AACC,MAAC,gBAAgB,mBAAmB,MAAM,CAAC,QAAQ,EAAE;AAC1D,SAAS,aAAa,CAAC,SAAS,EAAE,eAAe,EAAE;AACnD,EAAE,OAAO,CAAC,IAAI,KAAK;AACnB,IAAI,IAAI,IAAI,IAAI,SAAS,EAAE;AAC3B,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC;AACvB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,MAAM,IAAI,OAAO,SAAS,IAAI,UAAU;AACxC,QAAQ,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAChC;AACA,QAAQ,KAAK,GAAG,mBAAmB,CAAC,iBAAiB,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7E,MAAM,IAAI,KAAK,YAAY,mBAAmB;AAC9C,QAAQ,OAAO,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,GAAG,YAAY,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5G,WAAW,IAAI,KAAK;AACpB,QAAQ,OAAO,KAAK,CAAC,MAAM,CAAC;AAC5B,KAAK;AACL,IAAI,OAAO,eAAe,GAAG,eAAe,CAAC,MAAM,GAAG,IAAI,CAAC;AAC3D,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,OAAO,CAAC;AACd,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,WAAW,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE;AACnE,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG;AACH,EAAE,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,IAAI,EAAE;AACnC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,YAAY,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;AAChF,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE;AAC1B,MAAM,OAAO,MAAM,CAAC,MAAM,GAAG,QAAQ;AACrC,QAAQ,MAAM,IAAI,GAAG,CAAC;AACtB,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK,MAAM;AACX,MAAM,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;AAC3F,QAAQ,MAAM,IAAI,GAAG,CAAC;AACtB,MAAM,OAAO,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC,CAAC;AACxD,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE;AACnB,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,aAAa,GAAG,MAAM,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;AACrG,IAAI,OAAO,IAAI,CAAC,WAAW,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;AACnE,GAAG;AACH,CAAC;AACD,SAAS,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE;AAC/B,EAAE,IAAI,KAAK,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC;AAC/B,EAAE,KAAK,IAAI,GAAG,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE;AAC9C,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,YAAY;AAChC,MAAM,OAAO,OAAO,CAAC;AACrB,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG,CAAC,IAAI,IAAI,YAAY;AAC1D,MAAM,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACtB,GAAG;AACH,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC9C,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AAChC,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,GAAG,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACzE,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,YAAY,KAAK,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AAC5F,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACtG,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,aAAa,KAAK,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AACjJ,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAClD,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;AAC7B,QAAQ,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACjD,QAAQ,GAAG,IAAI,CAAC,CAAC;AACjB,OAAO;AACP,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC1G,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,IAAI,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,YAAY,KAAK,KAAK,GAAG,oCAAoC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AAChK,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAClD,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,QAAQ,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACjD,QAAQ,GAAG,IAAI,CAAC,CAAC;AACjB,OAAO;AACP,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1B,MAAM,IAAI,KAAK,CAAC,CAAC,CAAC;AAClB,QAAQ,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC9C,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACtG,KAAK;AACL,GAAG;AACH,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD,SAAS,UAAU,CAAC,IAAI,EAAE,GAAG,EAAE;AAC/B,EAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;AAChF,CAAC;AACD,SAAS,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE;AACvD,EAAE,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,KAAK,MAAM;AACxC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,UAAU,EAAE;AACjC,MAAM,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AACpC,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,MAAM,IAAI,IAAI,IAAI,CAAC,EAAE;AACrB,QAAQ,IAAI,MAAM,IAAI,IAAI,GAAG,CAAC;AAC9B,UAAU,OAAO;AACjB,QAAQ,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;AACxH,OAAO;AACP,MAAM,IAAI,GAAG,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;AAChC,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,MAAM;AACZ,IAAI,IAAI,GAAG,IAAI,CAAC;AAChB,GAAG;AACH,CAAC;AACD,SAAS,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE;AACzC,EAAE,IAAI,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAChD,EAAE,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG;AAC9C,IAAI,OAAO,OAAO,CAAC;AACnB,EAAE,IAAI,GAAG,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAC3C,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI;AAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;AAChB,MAAM,MAAM,IAAI,GAAG,CAAC;AACpB,MAAM,CAAC,IAAI,CAAC,CAAC;AACb,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,GAAG,CAAC;AACpB,MAAM,CAAC,EAAE,CAAC;AACV,KAAK;AACL,GAAG;AACH,EAAE,OAAO,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC;AACI,MAAC,2BAA2B,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK;AAC7D,EAAE,IAAI,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC;AAChD,EAAE,IAAI,IAAI,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,KAAK,KAAK;AAC5D,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1E,MAAM,OAAO,IAAI,GAAG,EAAE,KAAK,EAAE,CAAC;AAC9B,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACjD,IAAI,IAAI,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC9D,IAAI,OAAO,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI;AAC/E,MAAM,OAAO,CAAC,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM;AACvB,MAAM,OAAO,IAAI,GAAG,EAAE,KAAK,EAAE,CAAC;AAC9B,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAI,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI;AAC5D,MAAM,OAAO,IAAI,GAAG,EAAE,KAAK,EAAE,CAAC;AAC9B,IAAI,IAAI,SAAS,GAAG,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AACvG,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;AACjC,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC9F,MAAM,IAAI,KAAK,CAAC,EAAE,IAAI,GAAG,IAAI,MAAM,IAAI,MAAM,CAAC,EAAE,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;AAC3H,QAAQ,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC3E,QAAQ,IAAI,KAAK,EAAE,OAAO,GAAG,EAAE,CAAC;AAChC,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;AAC/B,UAAU,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACxC,UAAU,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACxC,SAAS,MAAM;AACf,UAAU,KAAK,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,QAAQ,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;AACnE,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,aAAa;AAC5C,UAAU,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;AACtD,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,aAAa;AACnD,UAAU,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;AACjD,QAAQ,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAC5F,OAAO,MAAM;AACb,QAAQ,IAAI,OAAO,GAAG,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACtD,QAAQ,OAAO;AACf,UAAU,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AACjE,UAAU,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,GAAG,KAAK,CAAC,SAAS,EAAE;AACzE,SAAS,CAAC;AACV,OAAO;AACP,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,YAAY,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI,EAAE;AACnE,MAAM,IAAI,QAAQ,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrF,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE;AAChD,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC;AACrC,UAAU,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AACjE,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AACvD,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AACjE,OAAO;AACP,KAAK;AACL,IAAI,IAAI,QAAQ,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,aAAa;AACxC,MAAM,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC9C,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC9D,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE,EAAE;AAC/E,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC3D,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;AACrK,OAAO;AACP,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,GAAG,CAAC;AACnB,IAAI,OAAO,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAChF,MAAM,IAAI,EAAE,CAAC;AACb,IAAI,MAAM,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC5C,IAAI,IAAI,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC;AAC3C,MAAM,MAAM,GAAG,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;AAC1E,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC,CAAC;AACvE,IAAI,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAC1F,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,IAAI;AACV,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;AAChF,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF,SAAS,MAAM,CAAC,IAAI,EAAE;AACtB,EAAE,OAAO,IAAI,CAAC,IAAI,IAAI,WAAW,IAAI,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC;AAC7D,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE;AACjC,EAAE,IAAI,IAAI,CAAC,IAAI,IAAI,aAAa,IAAI,IAAI,CAAC,IAAI,IAAI,YAAY;AAC7D,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC9E,EAAE,IAAI,CAAC,MAAM;AACb,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpE,EAAE,IAAI,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1C,EAAE,OAAO,KAAK,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC;AACvD,CAAC;AACD,SAAS,SAAS,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;AACzC,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;AAClB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACvD,IAAI,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;AACjJ,GAAG;AACH,EAAE,OAAO,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxC,CAAC;AACD,SAAS,oBAAoB,CAAC,IAAI,EAAE,GAAG,EAAE;AACzC,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC;AACpD,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;AACpB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACrB,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AACvB,GAAG;AACH,EAAE,KAAK,IAAI,IAAI,EAAE,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;AAClD,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;AACtB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACvB,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,aAAa,IAAI,IAAI,CAAC,IAAI,IAAI,YAAY,EAAE;AACxE,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;AAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,MAAM;AACZ,KAAK;AACL,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACI,MAAC,oBAAoB,GAAG,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK;AACtD,EAAE,IAAI,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE,IAAI,IAAI,GAAG,IAAI,EAAE,OAAO,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,KAAK,KAAK;AAC5D,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC;AAC1C,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,gBAAgB,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE;AACvE,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACjC,MAAM,IAAI,OAAO,GAAG,UAAU,CAAC,oBAAoB,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AACrE,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE;AAC1B,QAAQ,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAChD,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACvF,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AAChG,UAAU,OAAO;AACjB,YAAY,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;AAC/D,YAAY,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,EAAE,EAAE,EAAE,GAAG,EAAE;AAC5D,WAAW,CAAC;AACZ,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,QAAQ;AACvC;AACA;AACA,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;AACnG,UAAU,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AAC7C,UAAU,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;AACnH,YAAY,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAClH,YAAY,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI;AAClC,cAAc,MAAM,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACtD,YAAY,OAAO;AACnB,cAAc,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;AAClE,cAAc,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE;AACxE,aAAa,CAAC;AACd,WAAW;AACX,UAAU,IAAI,KAAK,GAAG,GAAG;AACzB,YAAY,OAAO,EAAE,KAAK,EAAE,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;AAC/F,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,OAAO,IAAI,GAAG,EAAE,KAAK,EAAE,CAAC;AAC5B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,IAAI;AACV,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC,CAAC;AACjF,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACG,MAAC,cAAc,GAAG;AACvB,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,2BAA2B,EAAE;AACpD,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,GAAG,EAAE,oBAAoB,EAAE;AACjD,EAAE;AACF,MAAM,WAAW,mBAAmB,IAAI,CAAC,EAAE,gBAAgB,EAAE,KAAK,EAAE,CAAC,CAAC;AACtE,SAAS,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE;AAC/B,EAAE,IAAI,EAAE,aAAa,EAAE,mBAAmB,EAAE,SAAS,GAAG,IAAI,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,kBAAkB,EAAE,gBAAgB,GAAG,IAAI,EAAE,eAAe,GAAG,WAAW,EAAE,GAAG,MAAM,CAAC;AAChL,EAAE,IAAI,EAAE,OAAO,YAAY,cAAc,CAAC;AAC1C,IAAI,MAAM,IAAI,UAAU,CAAC,gEAAgE,CAAC,CAAC;AAC3F,EAAE,IAAI,UAAU,GAAG,MAAM,CAAC,UAAU,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;AAChE,EAAE,IAAI,OAAO,GAAG,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,WAAW,CAAC;AACvD,EAAE,IAAI,mBAAmB,YAAY,eAAe,EAAE;AACtD,IAAI,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;AAC9C,IAAI,WAAW,GAAG,mBAAmB,CAAC,QAAQ,CAAC;AAC/C,GAAG,MAAM,IAAI,mBAAmB,EAAE;AAClC,IAAI,WAAW,GAAG,mBAAmB,CAAC;AACtC,GAAG;AACH,EAAE,IAAI,UAAU,GAAG,aAAa,IAAI,WAAW,GAAG,aAAa,CAAC,aAAa,EAAE,WAAW,CAAC,GAAG,KAAK,CAAC,CAAC;AACrG,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC1F,EAAE,IAAI,SAAS;AACf,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACvD,EAAE,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AACnD,EAAE,IAAI,gBAAgB;AACtB,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC;AACpE,EAAE,OAAO,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AACD,SAAS,iBAAiB,CAAC,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,GAAG,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;AACpG,EAAE,IAAI,CAAC,CAAC;AACR,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACrD,EAAE,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACnC,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,WAAW,IAAI,IAAI,CAAC,IAAI,IAAI,YAAY,IAAI,IAAI,CAAC,IAAI,IAAI,4BAA4B,IAAI,IAAI,CAAC,IAAI,IAAI,cAAc,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO;AACxL,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AACvB,GAAG;AACH,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;AAC3B,IAAI,EAAE,EAAE,GAAG;AACX,IAAI,OAAO,EAAE,kBAAkB,EAAE;AACjC,IAAI,QAAQ,EAAE,4BAA4B;AAC1C,GAAG,CAAC;AACJ,CAAC;AACD,IAAI,eAAe,GAAG,IAAI,CAAC;AAC3B,SAAS,kBAAkB,GAAG;AAC9B,EAAE,IAAI,eAAe;AACrB,IAAI,OAAO,eAAe,CAAC;AAC3B,EAAE,IAAI,MAAM,GAAG,oBAAoB,CAAC,IAAI,iBAAiB,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AACrH,EAAE,OAAO,eAAe,GAAG,MAAM,GAAG,MAAM,CAAC,OAAO,GAAG,EAAE,CAAC;AACxD;;;;"}