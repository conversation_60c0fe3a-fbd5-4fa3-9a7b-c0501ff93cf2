{"version": 3, "file": "frontmatter-D8AJ97p9.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/frontmatter.js"], "sourcesContent": ["import { s as styleTags, t as tags, f as foldNodeProp, c as foldInside, p as parseMixed, S as StreamLanguage } from \"./Index16.js\";\nimport { yaml } from \"./yaml.js\";\nconst frontMatterFence = /^---\\s*$/m;\nconst frontmatter = {\n  defineNodes: [{ name: \"Frontmatter\", block: true }, \"FrontmatterMark\"],\n  props: [\n    styleTags({\n      Frontmatter: [tags.documentMeta, tags.monospace],\n      FrontmatterMark: tags.processingInstruction\n    }),\n    foldNodeProp.add({\n      Frontmatter: foldInside,\n      FrontmatterMark: () => null\n    })\n  ],\n  wrap: parseMixed((node) => {\n    const { parser } = StreamLanguage.define(yaml);\n    if (node.type.name === \"Frontmatter\") {\n      return {\n        parser,\n        overlay: [{ from: node.from + 4, to: node.to - 4 }]\n      };\n    }\n    return null;\n  }),\n  parseBlock: [\n    {\n      name: \"Frontmatter\",\n      before: \"HorizontalRule\",\n      parse: (cx, line) => {\n        let end = void 0;\n        const children = new Array();\n        if (cx.lineStart === 0 && frontMatterFence.test(line.text)) {\n          children.push(cx.elt(\"FrontmatterMark\", 0, 4));\n          while (cx.nextLine()) {\n            if (frontMatterFence.test(line.text)) {\n              end = cx.lineStart + 4;\n              break;\n            }\n          }\n          if (end !== void 0) {\n            children.push(cx.elt(\"FrontmatterMark\", end - 4, end));\n            cx.addElement(cx.elt(\"Frontmatter\", 0, end, children));\n          }\n          return true;\n        }\n        return false;\n      }\n    }\n  ]\n};\nexport {\n  frontmatter\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAEA,MAAM,gBAAgB,GAAG,WAAW,CAAC;AAChC,MAAC,WAAW,GAAG;AACpB,EAAE,WAAW,EAAE,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,iBAAiB,CAAC;AACxE,EAAE,KAAK,EAAE;AACT,IAAI,SAAS,CAAC;AACd,MAAM,WAAW,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC;AACtD,MAAM,eAAe,EAAE,IAAI,CAAC,qBAAqB;AACjD,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,GAAG,CAAC;AACrB,MAAM,WAAW,EAAE,UAAU;AAC7B,MAAM,eAAe,EAAE,MAAM,IAAI;AACjC,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,IAAI,KAAK;AAC7B,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnD,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE;AAC1C,MAAM,OAAO;AACb,QAAQ,MAAM;AACd,QAAQ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;AAC3D,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC;AACJ,EAAE,UAAU,EAAE;AACd,IAAI;AACJ,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,KAAK,EAAE,CAAC,EAAE,EAAE,IAAI,KAAK;AAC3B,QAAQ,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC;AACzB,QAAQ,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;AACrC,QAAQ,IAAI,EAAE,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACpE,UAAU,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACzD,UAAU,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE;AAChC,YAAY,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAClD,cAAc,GAAG,GAAG,EAAE,CAAC,SAAS,GAAG,CAAC,CAAC;AACrC,cAAc,MAAM;AACpB,aAAa;AACb,WAAW;AACX,UAAU,IAAI,GAAG,KAAK,KAAK,CAAC,EAAE;AAC9B,YAAY,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,iBAAiB,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AACnE,YAAY,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC;AACnE,WAAW;AACX,UAAU,OAAO,IAAI,CAAC;AACtB,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,KAAK;AACL,GAAG;AACH;;;;"}