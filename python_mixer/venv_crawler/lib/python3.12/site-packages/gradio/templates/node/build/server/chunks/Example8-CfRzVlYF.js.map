{"version": 3, "file": "Example8-CfRzVlYF.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example8.js"], "sourcesContent": ["import { create_ssr_component, escape, each } from \"svelte/internal\";\nconst css = {\n  code: \"table.svelte-hn96gn.svelte-hn96gn{position:relative;border-collapse:collapse}td.svelte-hn96gn.svelte-hn96gn{border:1px solid var(--table-border-color);padding:var(--size-2);font-size:var(--text-sm);font-family:var(--font-mono)}.selected.svelte-hn96gn td.svelte-hn96gn{border-color:var(--border-color-accent)}.table.svelte-hn96gn.svelte-hn96gn{display:inline-block;margin:0 auto}.gallery.svelte-hn96gn td.svelte-hn96gn:first-child{border-left:none}.gallery.svelte-hn96gn tr:first-child td.svelte-hn96gn{border-top:none}.gallery.svelte-hn96gn td.svelte-hn96gn:last-child{border-right:none}.gallery.svelte-hn96gn tr:last-child td.svelte-hn96gn{border-bottom:none}.overlay.svelte-hn96gn.svelte-hn96gn{--gradient-to:transparent;position:absolute;bottom:0;background:linear-gradient(to bottom, transparent, var(--gradient-to));width:var(--size-full);height:50%}.odd.svelte-hn96gn.svelte-hn96gn{--gradient-to:var(--table-even-background-fill)}.even.svelte-hn96gn.svelte-hn96gn{--gradient-to:var(--table-odd-background-fill)}.button.svelte-hn96gn.svelte-hn96gn{--gradient-to:var(--background-fill-primary)}\",\n  map: `{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">export let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\nexport let index;\\\\nlet hovered = false;\\\\nlet loaded = Array.isArray(value);\\\\nlet is_empty = loaded && (value.length === 0 || value[0].length === 0);\\\\n<\\/script>\\\\n\\\\n{#if loaded}\\\\n\\\\t<!-- TODO: fix-->\\\\n\\\\t<!-- svelte-ignore a11y-no-static-element-interactions-->\\\\n\\\\t<div\\\\n\\\\t\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\t\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\t\\\\tclass:selected\\\\n\\\\t\\\\ton:mouseenter={() => (hovered = true)}\\\\n\\\\t\\\\ton:mouseleave={() => (hovered = false)}\\\\n\\\\t>\\\\n\\\\t\\\\t{#if typeof value === \\\\\"string\\\\\"}\\\\n\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t{:else if is_empty}\\\\n\\\\t\\\\t\\\\t<table class=\\\\\"\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t<tr>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<td>Empty</td>\\\\n\\\\t\\\\t\\\\t\\\\t</tr>\\\\n\\\\t\\\\t\\\\t</table>\\\\n\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t<table class=\\\\\"\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t{#each value.slice(0, 3) as row, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<tr>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#each row.slice(0, 3) as cell, j}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<td>{cell}</td>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if row.length > 3}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<td>…</td>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</tr>\\\\n\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t{#if value.length > 3}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"overlay\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:odd={index % 2 != 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:even={index % 2 == 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:button={type === \\\\\"gallery\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</table>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\ttable {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tborder-collapse: collapse;\\\\n\\\\t}\\\\n\\\\n\\\\ttd {\\\\n\\\\t\\\\tborder: 1px solid var(--table-border-color);\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t\\\\tfont-size: var(--text-sm);\\\\n\\\\t\\\\tfont-family: var(--font-mono);\\\\n\\\\t}\\\\n\\\\n\\\\t.selected td {\\\\n\\\\t\\\\tborder-color: var(--border-color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.table {\\\\n\\\\t\\\\tdisplay: inline-block;\\\\n\\\\t\\\\tmargin: 0 auto;\\\\n\\\\t}\\\\n\\\\n\\\\t.gallery td:first-child {\\\\n\\\\t\\\\tborder-left: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.gallery tr:first-child td {\\\\n\\\\t\\\\tborder-top: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.gallery td:last-child {\\\\n\\\\t\\\\tborder-right: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.gallery tr:last-child td {\\\\n\\\\t\\\\tborder-bottom: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.overlay {\\\\n\\\\t\\\\t--gradient-to: transparent;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tbackground: linear-gradient(to bottom, transparent, var(--gradient-to));\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: 50%;\\\\n\\\\t}\\\\n\\\\n\\\\t/* i dont know what i've done here but it is what it is */\\\\n\\\\t.odd {\\\\n\\\\t\\\\t--gradient-to: var(--table-even-background-fill);\\\\n\\\\t}\\\\n\\\\n\\\\t.even {\\\\n\\\\t\\\\t--gradient-to: var(--table-odd-background-fill);\\\\n\\\\t}\\\\n\\\\n\\\\t.button {\\\\n\\\\t\\\\t--gradient-to: var(--background-fill-primary);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAqDC,iCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,eAAe,CAAE,QAClB,CAEA,8BAAG,CACF,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,oBAAoB,CAAC,CAC3C,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,WAAW,CAC7B,CAEA,uBAAS,CAAC,gBAAG,CACZ,YAAY,CAAE,IAAI,qBAAqB,CACxC,CAEA,kCAAO,CACN,OAAO,CAAE,YAAY,CACrB,MAAM,CAAE,CAAC,CAAC,IACX,CAEA,sBAAQ,CAAC,gBAAE,YAAa,CACvB,WAAW,CAAE,IACd,CAEA,sBAAQ,CAAC,EAAE,YAAY,CAAC,gBAAG,CAC1B,UAAU,CAAE,IACb,CAEA,sBAAQ,CAAC,gBAAE,WAAY,CACtB,YAAY,CAAE,IACf,CAEA,sBAAQ,CAAC,EAAE,WAAW,CAAC,gBAAG,CACzB,aAAa,CAAE,IAChB,CAEA,oCAAS,CACR,aAAa,CAAE,WAAW,CAC1B,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,CAAC,CACT,UAAU,CAAE,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC,WAAW,CAAC,CAAC,IAAI,aAAa,CAAC,CAAC,CACvE,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,GACT,CAGA,gCAAK,CACJ,aAAa,CAAE,iCAChB,CAEA,iCAAM,CACL,aAAa,CAAE,gCAChB,CAEA,mCAAQ,CACP,aAAa,CAAE,8BAChB\"}`\n};\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { type } = $$props;\n  let { selected = false } = $$props;\n  let { index } = $$props;\n  let loaded = Array.isArray(value);\n  let is_empty = loaded && (value.length === 0 || value[0].length === 0);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  if ($$props.index === void 0 && $$bindings.index && index !== void 0)\n    $$bindings.index(index);\n  $$result.css.add(css);\n  return `${loaded ? `  <div class=\"${[\n    \"svelte-hn96gn\",\n    (type === \"table\" ? \"table\" : \"\") + \" \" + (type === \"gallery\" ? \"gallery\" : \"\") + \" \" + (selected ? \"selected\" : \"\")\n  ].join(\" \").trim()}\">${typeof value === \"string\" ? `${escape(value)}` : `${is_empty ? `<table class=\" svelte-hn96gn\" data-svelte-h=\"svelte-1bpl05m\"><tr><td class=\"svelte-hn96gn\">Empty</td></tr></table>` : `<table class=\" svelte-hn96gn\">${each(value.slice(0, 3), (row, i) => {\n    return `<tr>${each(row.slice(0, 3), (cell, j) => {\n      return `<td class=\"svelte-hn96gn\">${escape(cell)}</td>`;\n    })} ${row.length > 3 ? `<td class=\"svelte-hn96gn\" data-svelte-h=\"svelte-1o35md4\">…</td>` : ``} </tr>`;\n  })} ${value.length > 3 ? `<div class=\"${[\n    \"overlay svelte-hn96gn\",\n    (index % 2 != 0 ? \"odd\" : \"\") + \" \" + (index % 2 == 0 ? \"even\" : \"\") + \" \" + (type === \"gallery\" ? \"button\" : \"\")\n  ].join(\" \").trim()}\"></div>` : ``}</table>`}`}</div>` : ``}`;\n});\nexport {\n  Example as default\n};\n"], "names": [], "mappings": ";;AACA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,4kCAA4kC;AACplC,EAAE,GAAG,EAAE,CAAC,mrHAAmrH,CAAC;AAC5rH,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACpC,EAAE,IAAI,QAAQ,GAAG,MAAM,KAAK,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;AACzE,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,CAAC,cAAc,EAAE;AACtC,IAAI,eAAe;AACnB,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC;AACxH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,OAAO,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,CAAC,kHAAkH,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,KAAK;AACpR,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,KAAK;AACrD,MAAM,OAAO,CAAC,0BAA0B,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC;AAC9D,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,+DAA+D,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AAC1G,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,YAAY,EAAE;AAC1C,IAAI,uBAAuB;AAC3B,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,GAAG,EAAE,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,CAAC;AACrH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC;;;;"}