{"version": 3, "file": "BokehPlot-4fJ7BNld.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/BokehPlot.js"], "sourcesContent": ["import { create_ssr_component, add_attribute } from \"svelte/internal\";\nimport { createEventDispatcher, onDestroy } from \"svelte\";\nconst css = {\n  code: \".gradio-bokeh.svelte-1rhu6ax{display:flex;justify-content:center}\",\n  map: '{\"version\":3,\"file\":\"BokehPlot.svelte\",\"sources\":[\"BokehPlot.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { onDestroy, createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nexport let value;\\\\nexport let bokeh_version;\\\\nconst div_id = `bokehDiv-${Math.random().toString(5).substring(2)}`;\\\\nconst dispatch = createEventDispatcher();\\\\n$: plot = value?.plot;\\\\nasync function embed_bokeh(_plot) {\\\\n    if (document) {\\\\n        if (document.getElementById(div_id)) {\\\\n            document.getElementById(div_id).innerHTML = \\\\\"\\\\\";\\\\n        }\\\\n    }\\\\n    if (window.Bokeh) {\\\\n        load_bokeh();\\\\n        let plotObj = JSON.parse(_plot);\\\\n        const y = await window.Bokeh.embed.embed_item(plotObj, div_id);\\\\n        y._roots.forEach(async (p) => {\\\\n            await p.ready;\\\\n            dispatch(\\\\\"load\\\\\");\\\\n        });\\\\n    }\\\\n}\\\\n$: loaded && embed_bokeh(plot);\\\\nconst main_src = `https://cdn.bokeh.org/bokeh/release/bokeh-${bokeh_version}.min.js`;\\\\nconst plugins_src = [\\\\n    `https://cdn.pydata.org/bokeh/release/bokeh-widgets-${bokeh_version}.min.js`,\\\\n    `https://cdn.pydata.org/bokeh/release/bokeh-tables-${bokeh_version}.min.js`,\\\\n    `https://cdn.pydata.org/bokeh/release/bokeh-gl-${bokeh_version}.min.js`,\\\\n    `https://cdn.pydata.org/bokeh/release/bokeh-api-${bokeh_version}.min.js`\\\\n];\\\\nlet loaded = false;\\\\nasync function load_plugins() {\\\\n    await Promise.all(plugins_src.map((src, i) => {\\\\n        return new Promise((resolve) => {\\\\n            const script = document.createElement(\\\\\"script\\\\\");\\\\n            script.onload = resolve;\\\\n            script.src = src;\\\\n            document.head.appendChild(script);\\\\n            return script;\\\\n        });\\\\n    }));\\\\n    loaded = true;\\\\n}\\\\nlet plugin_scripts = [];\\\\nfunction handle_bokeh_loaded() {\\\\n    plugin_scripts = load_plugins();\\\\n}\\\\nfunction load_bokeh() {\\\\n    const script = document.createElement(\\\\\"script\\\\\");\\\\n    script.onload = handle_bokeh_loaded;\\\\n    script.src = main_src;\\\\n    const is_bokeh_script_present = document.head.querySelector(`script[src=\\\\\"${main_src}\\\\\"]`);\\\\n    if (!is_bokeh_script_present) {\\\\n        document.head.appendChild(script);\\\\n    }\\\\n    else {\\\\n        handle_bokeh_loaded();\\\\n    }\\\\n    return script;\\\\n}\\\\nconst main_script = bokeh_version ? load_bokeh() : null;\\\\nonDestroy(() => {\\\\n    if (main_script in document.children) {\\\\n        document.removeChild(main_script);\\\\n        plugin_scripts.forEach((child) => document.removeChild(child));\\\\n    }\\\\n});\\\\n<\\/script>\\\\n\\\\n<div data-testid={\\\\\"bokeh\\\\\"} id={div_id} class=\\\\\"gradio-bokeh\\\\\" />\\\\n\\\\n<style>\\\\n\\\\t.gradio-bokeh {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAwEC,4BAAc,CACb,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAClB\"}'\n};\nconst BokehPlot = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let plot;\n  let { value } = $$props;\n  let { bokeh_version } = $$props;\n  const div_id = `bokehDiv-${Math.random().toString(5).substring(2)}`;\n  const dispatch = createEventDispatcher();\n  async function embed_bokeh(_plot) {\n    if (document) {\n      if (document.getElementById(div_id)) {\n        document.getElementById(div_id).innerHTML = \"\";\n      }\n    }\n    if (window.Bokeh) {\n      load_bokeh();\n      let plotObj = JSON.parse(_plot);\n      const y = await window.Bokeh.embed.embed_item(plotObj, div_id);\n      y._roots.forEach(async (p) => {\n        await p.ready;\n        dispatch(\"load\");\n      });\n    }\n  }\n  const main_src = `https://cdn.bokeh.org/bokeh/release/bokeh-${bokeh_version}.min.js`;\n  const plugins_src = [\n    `https://cdn.pydata.org/bokeh/release/bokeh-widgets-${bokeh_version}.min.js`,\n    `https://cdn.pydata.org/bokeh/release/bokeh-tables-${bokeh_version}.min.js`,\n    `https://cdn.pydata.org/bokeh/release/bokeh-gl-${bokeh_version}.min.js`,\n    `https://cdn.pydata.org/bokeh/release/bokeh-api-${bokeh_version}.min.js`\n  ];\n  let loaded = false;\n  async function load_plugins() {\n    await Promise.all(plugins_src.map((src, i) => {\n      return new Promise((resolve) => {\n        const script = document.createElement(\"script\");\n        script.onload = resolve;\n        script.src = src;\n        document.head.appendChild(script);\n        return script;\n      });\n    }));\n    loaded = true;\n  }\n  let plugin_scripts = [];\n  function handle_bokeh_loaded() {\n    plugin_scripts = load_plugins();\n  }\n  function load_bokeh() {\n    const script = document.createElement(\"script\");\n    script.onload = handle_bokeh_loaded;\n    script.src = main_src;\n    const is_bokeh_script_present = document.head.querySelector(`script[src=\"${main_src}\"]`);\n    if (!is_bokeh_script_present) {\n      document.head.appendChild(script);\n    } else {\n      handle_bokeh_loaded();\n    }\n    return script;\n  }\n  const main_script = bokeh_version ? load_bokeh() : null;\n  onDestroy(() => {\n    if (main_script in document.children) {\n      document.removeChild(main_script);\n      plugin_scripts.forEach((child) => document.removeChild(child));\n    }\n  });\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.bokeh_version === void 0 && $$bindings.bokeh_version && bokeh_version !== void 0)\n    $$bindings.bokeh_version(bokeh_version);\n  $$result.css.add(css);\n  plot = value?.plot;\n  loaded && embed_bokeh(plot);\n  return `<div${add_attribute(\"data-testid\", \"bokeh\", 0)}${add_attribute(\"id\", div_id, 0)} class=\"gradio-bokeh svelte-1rhu6ax\"></div>`;\n});\nexport {\n  BokehPlot as default\n};\n"], "names": [], "mappings": ";;AAEA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,mEAAmE;AAC3E,EAAE,GAAG,EAAE,qwFAAqwF;AAC5wF,CAAC,CAAC;AACG,MAAC,SAAS,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACjF,EAAE,IAAI,IAAI,CAAC;AACX,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,MAAM,MAAM,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,eAAe,WAAW,CAAC,KAAK,EAAE;AACpC,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE;AAC3C,QAAQ,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC;AACvD,OAAO;AACP,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;AACtB,MAAM,UAAU,EAAE,CAAC;AACnB,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACtC,MAAM,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACrE,MAAM,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK;AACpC,QAAQ,MAAM,CAAC,CAAC,KAAK,CAAC;AACtB,QAAQ,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG,CAAC,0CAA0C,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;AACvF,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,CAAC,mDAAmD,EAAE,aAAa,CAAC,OAAO,CAAC;AAChF,IAAI,CAAC,kDAAkD,EAAE,aAAa,CAAC,OAAO,CAAC;AAC/E,IAAI,CAAC,8CAA8C,EAAE,aAAa,CAAC,OAAO,CAAC;AAC3E,IAAI,CAAC,+CAA+C,EAAE,aAAa,CAAC,OAAO,CAAC;AAC5E,GAAG,CAAC;AACJ,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC;AACrB,EAAE,eAAe,YAAY,GAAG;AAChC,IAAI,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK;AAClD,MAAM,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK;AACtC,QAAQ,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACxD,QAAQ,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC;AAChC,QAAQ,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;AACzB,QAAQ,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AAC1C,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO,CAAC,CAAC;AACT,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,MAAM,GAAG,IAAI,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,cAAc,GAAG,EAAE,CAAC;AAC1B,EAAE,SAAS,mBAAmB,GAAG;AACjC,IAAI,cAAc,GAAG,YAAY,EAAE,CAAC;AACpC,GAAG;AACH,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,MAAM,GAAG,mBAAmB,CAAC;AACxC,IAAI,MAAM,CAAC,GAAG,GAAG,QAAQ,CAAC;AAC1B,IAAI,MAAM,uBAAuB,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7F,IAAI,IAAI,CAAC,uBAAuB,EAAE;AAClC,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACxC,KAAK,MAAM;AACX,MAAM,mBAAmB,EAAE,CAAC;AAC5B,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,MAAM,WAAW,GAAG,aAAa,GAAG,UAAU,EAAE,GAAG,IAAI,CAAC;AAC1D,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,IAAI,WAAW,IAAI,QAAQ,CAAC,QAAQ,EAAE;AAC1C,MAAM,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,MAAM,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;AACrE,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,GAAG,KAAK,EAAE,IAAI,CAAC;AACrB,EAAE,MAAM,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AAC9B,EAAE,OAAO,CAAC,IAAI,EAAE,aAAa,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,2CAA2C,CAAC,CAAC;AACvI,CAAC;;;;"}