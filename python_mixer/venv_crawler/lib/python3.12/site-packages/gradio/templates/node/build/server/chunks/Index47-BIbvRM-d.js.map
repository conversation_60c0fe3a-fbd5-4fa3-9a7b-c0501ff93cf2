{"version": 3, "file": "Index47-BIbvRM-d.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index47.js"], "sourcesContent": ["import { create_ssr_component, subscribe, set_store_value, escape, add_attribute, add_styles, each, validate_component } from \"svelte/internal\";\nimport { createEventDispatcher, onMount, setContext } from \"svelte\";\nimport { writable } from \"svelte/store\";\nconst OverflowIcon = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  return `<svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"><circle cx=\"2.5\" cy=\"8\" r=\"1.5\" fill=\"currentColor\"></circle><circle cx=\"8\" cy=\"8\" r=\"1.5\" fill=\"currentColor\"></circle><circle cx=\"13.5\" cy=\"8\" r=\"1.5\" fill=\"currentColor\"></circle></svg>`;\n});\nconst css = {\n  code: '.tabs.svelte-1tcem6n.svelte-1tcem6n{position:relative;display:flex;flex-direction:column;gap:var(--layout-gap)}.hide.svelte-1tcem6n.svelte-1tcem6n{display:none}.tab-wrapper.svelte-1tcem6n.svelte-1tcem6n{display:flex;align-items:center;justify-content:space-between;position:relative;height:var(--size-8);padding-bottom:var(--size-2)}.tab-container.svelte-1tcem6n.svelte-1tcem6n{display:flex;align-items:center;width:100%;position:relative;overflow:hidden;height:var(--size-8)}.tab-container.svelte-1tcem6n.svelte-1tcem6n::after{content:\"\";position:absolute;bottom:0;left:0;right:0;height:1px;background-color:var(--border-color-primary)}.overflow-menu.svelte-1tcem6n.svelte-1tcem6n{flex-shrink:0;margin-left:var(--size-2)}button.svelte-1tcem6n.svelte-1tcem6n{margin-bottom:0;border:none;border-radius:0;padding:0 var(--size-4);color:var(--body-text-color);font-weight:var(--section-header-text-weight);font-size:var(--section-header-text-size);transition:background-color color 0.2s ease-out;background-color:transparent;height:100%;display:flex;align-items:center;white-space:nowrap;position:relative}button.svelte-1tcem6n.svelte-1tcem6n:disabled{opacity:0.5;cursor:not-allowed}button.svelte-1tcem6n.svelte-1tcem6n:hover:not(:disabled):not(.selected){background-color:var(--background-fill-secondary);color:var(--body-text-color)}.selected.svelte-1tcem6n.svelte-1tcem6n{background-color:transparent;color:var(--color-accent);position:relative}.selected.svelte-1tcem6n.svelte-1tcem6n::after{content:\"\";position:absolute;bottom:0;left:0;width:100%;height:2px;background-color:var(--color-accent);animation:svelte-1tcem6n-fade-grow 0.2s ease-out forwards;transform-origin:center;z-index:1}@keyframes svelte-1tcem6n-fade-grow{from{opacity:0;transform:scaleX(0.8)}to{opacity:1;transform:scaleX(1)}}.overflow-dropdown.svelte-1tcem6n.svelte-1tcem6n{position:absolute;top:calc(100% + var(--size-2));right:0;background-color:var(--background-fill-primary);border:1px solid var(--border-color-primary);border-radius:var(--radius-sm);z-index:var(--layer-5);box-shadow:0 2px 5px rgba(0, 0, 0, 0.1);padding:var(--size-2);min-width:150px;width:max-content}.overflow-dropdown.svelte-1tcem6n button.svelte-1tcem6n{display:block;width:100%;text-align:left;padding:var(--size-2);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.overflow-menu.svelte-1tcem6n>button.svelte-1tcem6n{padding:var(--size-1) var(--size-2);min-width:auto;border:1px solid var(--border-color-primary);border-radius:var(--radius-sm);display:flex;align-items:center;justify-content:center}.overflow-menu.svelte-1tcem6n>button.svelte-1tcem6n:hover{background-color:var(--background-fill-secondary)}.overflow-menu.svelte-1tcem6n svg{width:16px;height:16px}.overflow-item-selected.svelte-1tcem6n svg{color:var(--color-accent)}.visually-hidden.svelte-1tcem6n.svelte-1tcem6n{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0}',\n  map: `{\"version\":3,\"file\":\"Tabs.svelte\",\"sources\":[\"Tabs.svelte\"],\"sourcesContent\":[\"<script context=\\\\\"module\\\\\" lang=\\\\\"ts\\\\\">export const TABS = {};\\\\n<\\/script>\\\\n\\\\n<script lang=\\\\\"ts\\\\\">import { setContext, createEventDispatcher, tick, onMount } from \\\\\"svelte\\\\\";\\\\nimport OverflowIcon from \\\\\"./OverflowIcon.svelte\\\\\";\\\\nimport { writable } from \\\\\"svelte/store\\\\\";\\\\nexport let visible = true;\\\\nexport let elem_id = \\\\\"\\\\\";\\\\nexport let elem_classes = [];\\\\nexport let selected;\\\\nexport let initial_tabs;\\\\nlet tabs = [...initial_tabs];\\\\nlet visible_tabs = [...initial_tabs];\\\\nlet overflow_tabs = [];\\\\nlet overflow_menu_open = false;\\\\nlet overflow_menu;\\\\n$: has_tabs = tabs.length > 0;\\\\nlet tab_nav_el;\\\\nconst selected_tab = writable(selected || tabs[0]?.id || false);\\\\nconst selected_tab_index = writable(tabs.findIndex((t) => t?.id === selected) || 0);\\\\nconst dispatch = createEventDispatcher();\\\\nlet is_overflowing = false;\\\\nlet overflow_has_selected_tab = false;\\\\nlet tab_els = {};\\\\nonMount(() => {\\\\n    const observer = new IntersectionObserver((entries) => {\\\\n        handle_menu_overflow();\\\\n    });\\\\n    observer.observe(tab_nav_el);\\\\n});\\\\nsetContext(TABS, {\\\\n    register_tab: (tab, order) => {\\\\n        tabs[order] = tab;\\\\n        if ($selected_tab === false && tab.visible && tab.interactive) {\\\\n            $selected_tab = tab.id;\\\\n            $selected_tab_index = order;\\\\n        }\\\\n        return order;\\\\n    },\\\\n    unregister_tab: (tab, order) => {\\\\n        if ($selected_tab === tab.id) {\\\\n            $selected_tab = tabs[0]?.id || false;\\\\n        }\\\\n        tabs[order] = null;\\\\n    },\\\\n    selected_tab,\\\\n    selected_tab_index\\\\n});\\\\nfunction change_tab(id) {\\\\n    const tab_to_activate = tabs.find((t) => t?.id === id);\\\\n    if (id !== void 0 && tab_to_activate && tab_to_activate.interactive && tab_to_activate.visible && $selected_tab !== tab_to_activate.id) {\\\\n        selected = id;\\\\n        $selected_tab = id;\\\\n        $selected_tab_index = tabs.findIndex((t) => t?.id === id);\\\\n        dispatch(\\\\\"change\\\\\");\\\\n        overflow_menu_open = false;\\\\n    }\\\\n}\\\\n$: tabs, selected !== null && change_tab(selected);\\\\n$: tabs, tab_nav_el, tab_els, handle_menu_overflow();\\\\nfunction handle_outside_click(event) {\\\\n    if (overflow_menu_open && overflow_menu && !overflow_menu.contains(event.target)) {\\\\n        overflow_menu_open = false;\\\\n    }\\\\n}\\\\nasync function handle_menu_overflow() {\\\\n    if (!tab_nav_el)\\\\n        return;\\\\n    await tick();\\\\n    const tab_nav_size = tab_nav_el.getBoundingClientRect();\\\\n    let max_width = tab_nav_size.width;\\\\n    const tab_sizes = get_tab_sizes(tabs, tab_els);\\\\n    let last_visible_index = 0;\\\\n    const offset = tab_nav_size.left;\\\\n    for (let i = tabs.length - 1; i >= 0; i--) {\\\\n        const tab = tabs[i];\\\\n        if (!tab)\\\\n            continue;\\\\n        const tab_rect = tab_sizes[tab.id];\\\\n        if (!tab_rect)\\\\n            continue;\\\\n        if (tab_rect.right - offset < max_width) {\\\\n            last_visible_index = i;\\\\n            break;\\\\n        }\\\\n    }\\\\n    overflow_tabs = tabs.slice(last_visible_index + 1);\\\\n    visible_tabs = tabs.slice(0, last_visible_index + 1);\\\\n    overflow_has_selected_tab = handle_overflow_has_selected_tab($selected_tab);\\\\n    is_overflowing = overflow_tabs.length > 0;\\\\n}\\\\n$: overflow_has_selected_tab = handle_overflow_has_selected_tab($selected_tab);\\\\nfunction handle_overflow_has_selected_tab(selected_tab2) {\\\\n    if (selected_tab2 === false)\\\\n        return false;\\\\n    return overflow_tabs.some((t) => t?.id === selected_tab2);\\\\n}\\\\nfunction get_tab_sizes(tabs2, tab_els2) {\\\\n    const tab_sizes = {};\\\\n    tabs2.forEach((tab) => {\\\\n        if (!tab)\\\\n            return;\\\\n        tab_sizes[tab.id] = tab_els2[tab.id]?.getBoundingClientRect();\\\\n    });\\\\n    return tab_sizes;\\\\n}\\\\n$: tab_scale = tabs[$selected_tab_index >= 0 ? $selected_tab_index : 0]?.scale;\\\\n<\\/script>\\\\n\\\\n<svelte:window\\\\n\\\\ton:resize={handle_menu_overflow}\\\\n\\\\ton:click={handle_outside_click}\\\\n/>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"tabs {elem_classes.join(' ')}\\\\\"\\\\n\\\\tclass:hide={!visible}\\\\n\\\\tid={elem_id}\\\\n\\\\tstyle:flex-grow={tab_scale}\\\\n>\\\\n\\\\t{#if has_tabs}\\\\n\\\\t\\\\t<div class=\\\\\"tab-wrapper\\\\\">\\\\n\\\\t\\\\t\\\\t<div class=\\\\\"tab-container visually-hidden\\\\\" aria-hidden=\\\\\"true\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t{#each tabs as t, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if t?.visible}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button bind:this={tab_els[t.id]}>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{t?.label}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t<div class=\\\\\"tab-container\\\\\" bind:this={tab_nav_el} role=\\\\\"tablist\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t{#each visible_tabs as t, i}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if t?.visible}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\trole=\\\\\"tab\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:selected={t.id === $selected_tab}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-selected={t.id === $selected_tab}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-controls={t.elem_id}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdisabled={!t.interactive}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\taria-disabled={!t.interactive}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tid={t.elem_id ? t.elem_id + \\\\\"-button\\\\\" : null}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdata-tab-id={t.id}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tif (t.id !== $selected_tab) {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tchange_tab(t.id);\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"select\\\\\", { value: t.label, index: i });\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{t.label}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t<span\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"overflow-menu\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tclass:hide={!is_overflowing || !overflow_tabs.some((t) => t?.visible)}\\\\n\\\\t\\\\t\\\\t\\\\tbind:this={overflow_menu}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click|stopPropagation={() =>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t(overflow_menu_open = !overflow_menu_open)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass:overflow-item-selected={overflow_has_selected_tab}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<OverflowIcon />\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"overflow-dropdown\\\\\" class:hide={!overflow_menu_open}>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each overflow_tabs as t}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{#if t?.visible}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => change_tab(t?.id)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tclass:selected={t?.id === $selected_tab}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{t?.label}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n\\\\t<slot />\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.tabs {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tgap: var(--layout-gap);\\\\n\\\\t}\\\\n\\\\n\\\\t.hide {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.tab-wrapper {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tpadding-bottom: var(--size-2);\\\\n\\\\t}\\\\n\\\\n\\\\t.tab-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t}\\\\n\\\\n\\\\t.tab-container::after {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\tright: 0;\\\\n\\\\t\\\\theight: 1px;\\\\n\\\\t\\\\tbackground-color: var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.overflow-menu {\\\\n\\\\t\\\\tflex-shrink: 0;\\\\n\\\\t\\\\tmargin-left: var(--size-2);\\\\n\\\\t}\\\\n\\\\n\\\\tbutton {\\\\n\\\\t\\\\tmargin-bottom: 0;\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t\\\\tborder-radius: 0;\\\\n\\\\t\\\\tpadding: 0 var(--size-4);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t\\\\tfont-weight: var(--section-header-text-weight);\\\\n\\\\t\\\\tfont-size: var(--section-header-text-size);\\\\n\\\\t\\\\ttransition: background-color color 0.2s ease-out;\\\\n\\\\t\\\\tbackground-color: transparent;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\tbutton:disabled {\\\\n\\\\t\\\\topacity: 0.5;\\\\n\\\\t\\\\tcursor: not-allowed;\\\\n\\\\t}\\\\n\\\\n\\\\tbutton:hover:not(:disabled):not(.selected) {\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.selected {\\\\n\\\\t\\\\tbackground-color: transparent;\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t}\\\\n\\\\n\\\\t.selected::after {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 2px;\\\\n\\\\t\\\\tbackground-color: var(--color-accent);\\\\n\\\\t\\\\tanimation: fade-grow 0.2s ease-out forwards;\\\\n\\\\t\\\\ttransform-origin: center;\\\\n\\\\t\\\\tz-index: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t@keyframes fade-grow {\\\\n\\\\t\\\\tfrom {\\\\n\\\\t\\\\t\\\\topacity: 0;\\\\n\\\\t\\\\t\\\\ttransform: scaleX(0.8);\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\tto {\\\\n\\\\t\\\\t\\\\topacity: 1;\\\\n\\\\t\\\\t\\\\ttransform: scaleX(1);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.overflow-dropdown {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: calc(100% + var(--size-2));\\\\n\\\\t\\\\tright: 0;\\\\n\\\\t\\\\tbackground-color: var(--background-fill-primary);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tz-index: var(--layer-5);\\\\n\\\\t\\\\tbox-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t\\\\tmin-width: 150px;\\\\n\\\\t\\\\twidth: max-content;\\\\n\\\\t}\\\\n\\\\n\\\\t.overflow-dropdown button {\\\\n\\\\t\\\\tdisplay: block;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t}\\\\n\\\\n\\\\t.overflow-menu > button {\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t\\\\tmin-width: auto;\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-sm);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.overflow-menu > button:hover {\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t}\\\\n\\\\n\\\\t.overflow-menu :global(svg) {\\\\n\\\\t\\\\twidth: 16px;\\\\n\\\\t\\\\theight: 16px;\\\\n\\\\t}\\\\n\\\\n\\\\t.overflow-item-selected :global(svg) {\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.visually-hidden {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\twidth: 1px;\\\\n\\\\t\\\\theight: 1px;\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tmargin: -1px;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t\\\\tclip: rect(0, 0, 0, 0);\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t\\\\tborder: 0;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA0LC,mCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,GAAG,CAAE,IAAI,YAAY,CACtB,CAEA,mCAAM,CACL,OAAO,CAAE,IACV,CAEA,0CAAa,CACZ,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,aAAa,CAC9B,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,cAAc,CAAE,IAAI,QAAQ,CAC7B,CAEA,4CAAe,CACd,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,KAAK,CAAE,IAAI,CACX,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,MAAM,CAChB,MAAM,CAAE,IAAI,QAAQ,CACrB,CAEA,4CAAc,OAAQ,CACrB,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,CAAC,CACT,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,CAAC,CACR,MAAM,CAAE,GAAG,CACX,gBAAgB,CAAE,IAAI,sBAAsB,CAC7C,CAEA,4CAAe,CACd,WAAW,CAAE,CAAC,CACd,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,oCAAO,CACN,aAAa,CAAE,CAAC,CAChB,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,CAAC,CAChB,OAAO,CAAE,CAAC,CAAC,IAAI,QAAQ,CAAC,CACxB,KAAK,CAAE,IAAI,iBAAiB,CAAC,CAC7B,WAAW,CAAE,IAAI,4BAA4B,CAAC,CAC9C,SAAS,CAAE,IAAI,0BAA0B,CAAC,CAC1C,UAAU,CAAE,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAChD,gBAAgB,CAAE,WAAW,CAC7B,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,QACX,CAEA,oCAAM,SAAU,CACf,OAAO,CAAE,GAAG,CACZ,MAAM,CAAE,WACT,CAEA,oCAAM,MAAM,KAAK,SAAS,CAAC,KAAK,SAAS,CAAE,CAC1C,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,KAAK,CAAE,IAAI,iBAAiB,CAC7B,CAEA,uCAAU,CACT,gBAAgB,CAAE,WAAW,CAC7B,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,QAAQ,CAAE,QACX,CAEA,uCAAS,OAAQ,CAChB,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,CAAC,CACT,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,GAAG,CACX,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,SAAS,CAAE,wBAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAC3C,gBAAgB,CAAE,MAAM,CACxB,OAAO,CAAE,CACV,CAEA,WAAW,wBAAU,CACpB,IAAK,CACJ,OAAO,CAAE,CAAC,CACV,SAAS,CAAE,OAAO,GAAG,CACtB,CACA,EAAG,CACF,OAAO,CAAE,CAAC,CACV,SAAS,CAAE,OAAO,CAAC,CACpB,CACD,CAEA,gDAAmB,CAClB,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAC/B,KAAK,CAAE,CAAC,CACR,gBAAgB,CAAE,IAAI,yBAAyB,CAAC,CAChD,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,SAAS,CAAC,CACvB,UAAU,CAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CACxC,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,SAAS,CAAE,KAAK,CAChB,KAAK,CAAE,WACR,CAEA,iCAAkB,CAAC,qBAAO,CACzB,OAAO,CAAE,KAAK,CACd,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,IAAI,CAChB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,WAAW,CAAE,MAAM,CACnB,QAAQ,CAAE,MAAM,CAChB,aAAa,CAAE,QAChB,CAEA,6BAAc,CAAG,qBAAO,CACvB,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CACpC,SAAS,CAAE,IAAI,CACf,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAClB,CAEA,6BAAc,CAAG,qBAAM,MAAO,CAC7B,gBAAgB,CAAE,IAAI,2BAA2B,CAClD,CAEA,6BAAc,CAAS,GAAK,CAC3B,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT,CAEA,sCAAuB,CAAS,GAAK,CACpC,KAAK,CAAE,IAAI,cAAc,CAC1B,CAEA,8CAAiB,CAChB,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,GAAG,CACV,MAAM,CAAE,GAAG,CACX,OAAO,CAAE,CAAC,CACV,MAAM,CAAE,IAAI,CACZ,QAAQ,CAAE,MAAM,CAChB,IAAI,CAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACtB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,CACT\"}`\n};\nconst TABS = {};\nconst Tabs = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let has_tabs;\n  let tab_scale;\n  let $selected_tab_index, $$unsubscribe_selected_tab_index;\n  let $selected_tab, $$unsubscribe_selected_tab;\n  let { visible = true } = $$props;\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { selected } = $$props;\n  let { initial_tabs } = $$props;\n  let tabs = [...initial_tabs];\n  let visible_tabs = [...initial_tabs];\n  let overflow_tabs = [];\n  let overflow_menu_open = false;\n  let overflow_menu;\n  let tab_nav_el;\n  const selected_tab = writable(selected || tabs[0]?.id || false);\n  $$unsubscribe_selected_tab = subscribe(selected_tab, (value) => $selected_tab = value);\n  const selected_tab_index = writable(tabs.findIndex((t) => t?.id === selected) || 0);\n  $$unsubscribe_selected_tab_index = subscribe(selected_tab_index, (value) => $selected_tab_index = value);\n  const dispatch = createEventDispatcher();\n  let overflow_has_selected_tab = false;\n  let tab_els = {};\n  onMount(() => {\n    const observer = new IntersectionObserver((entries) => {\n      handle_menu_overflow();\n    });\n    observer.observe(tab_nav_el);\n  });\n  setContext(TABS, {\n    register_tab: (tab, order) => {\n      tabs[order] = tab;\n      if ($selected_tab === false && tab.visible && tab.interactive) {\n        set_store_value(selected_tab, $selected_tab = tab.id, $selected_tab);\n        set_store_value(selected_tab_index, $selected_tab_index = order, $selected_tab_index);\n      }\n      return order;\n    },\n    unregister_tab: (tab, order) => {\n      if ($selected_tab === tab.id) {\n        set_store_value(selected_tab, $selected_tab = tabs[0]?.id || false, $selected_tab);\n      }\n      tabs[order] = null;\n    },\n    selected_tab,\n    selected_tab_index\n  });\n  function change_tab(id) {\n    const tab_to_activate = tabs.find((t) => t?.id === id);\n    if (id !== void 0 && tab_to_activate && tab_to_activate.interactive && tab_to_activate.visible && $selected_tab !== tab_to_activate.id) {\n      selected = id;\n      set_store_value(selected_tab, $selected_tab = id, $selected_tab);\n      set_store_value(selected_tab_index, $selected_tab_index = tabs.findIndex((t) => t?.id === id), $selected_tab_index);\n      dispatch(\"change\");\n      overflow_menu_open = false;\n    }\n  }\n  async function handle_menu_overflow() {\n    return;\n  }\n  function handle_overflow_has_selected_tab(selected_tab2) {\n    if (selected_tab2 === false)\n      return false;\n    return overflow_tabs.some((t) => t?.id === selected_tab2);\n  }\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  if ($$props.initial_tabs === void 0 && $$bindings.initial_tabs && initial_tabs !== void 0)\n    $$bindings.initial_tabs(initial_tabs);\n  $$result.css.add(css);\n  has_tabs = tabs.length > 0;\n  {\n    selected !== null && change_tab(selected);\n  }\n  {\n    handle_menu_overflow();\n  }\n  overflow_has_selected_tab = handle_overflow_has_selected_tab($selected_tab);\n  tab_scale = tabs[$selected_tab_index >= 0 ? $selected_tab_index : 0]?.scale;\n  $$unsubscribe_selected_tab_index();\n  $$unsubscribe_selected_tab();\n  return ` <div class=\"${[\n    \"tabs \" + escape(elem_classes.join(\" \"), true) + \" svelte-1tcem6n\",\n    !visible ? \"hide\" : \"\"\n  ].join(\" \").trim()}\"${add_attribute(\"id\", elem_id, 0)}${add_styles({ \"flex-grow\": tab_scale })}>${has_tabs ? `<div class=\"tab-wrapper svelte-1tcem6n\"><div class=\"tab-container visually-hidden svelte-1tcem6n\" aria-hidden=\"true\">${each(tabs, (t, i) => {\n    return `${t?.visible ? `<button class=\"svelte-1tcem6n\"${add_attribute(\"this\", tab_els[t.id], 0)}>${escape(t?.label)} </button>` : ``}`;\n  })}</div> <div class=\"tab-container svelte-1tcem6n\" role=\"tablist\"${add_attribute(\"this\", tab_nav_el, 0)}>${each(visible_tabs, (t, i) => {\n    return `${t?.visible ? `<button role=\"tab\"${add_attribute(\"aria-selected\", t.id === $selected_tab, 0)}${add_attribute(\"aria-controls\", t.elem_id, 0)} ${!t.interactive ? \"disabled\" : \"\"}${add_attribute(\"aria-disabled\", !t.interactive, 0)}${add_attribute(\"id\", t.elem_id ? t.elem_id + \"-button\" : null, 0)}${add_attribute(\"data-tab-id\", t.id, 0)} class=\"${[\"svelte-1tcem6n\", t.id === $selected_tab ? \"selected\" : \"\"].join(\" \").trim()}\">${escape(t.label)} </button>` : ``}`;\n  })}</div> <span class=\"${[\n    \"overflow-menu svelte-1tcem6n\",\n    \"hide\"\n  ].join(\" \").trim()}\"${add_attribute(\"this\", overflow_menu, 0)}><button class=\"${[\n    \"svelte-1tcem6n\",\n    overflow_has_selected_tab ? \"overflow-item-selected\" : \"\"\n  ].join(\" \").trim()}\">${validate_component(OverflowIcon, \"OverflowIcon\").$$render($$result, {}, {}, {})}</button> <div class=\"${[\"overflow-dropdown svelte-1tcem6n\", !overflow_menu_open ? \"hide\" : \"\"].join(\" \").trim()}\">${each(overflow_tabs, (t) => {\n    return `${t?.visible ? `<button class=\"${[\"svelte-1tcem6n\", t?.id === $selected_tab ? \"selected\" : \"\"].join(\" \").trim()}\">${escape(t?.label)} </button>` : ``}`;\n  })}</div></span></div>` : ``} ${slots.default ? slots.default({}) : ``} </div>`;\n});\nconst Tabs$1 = Tabs;\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  const dispatch = createEventDispatcher();\n  let { visible = true } = $$props;\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { selected } = $$props;\n  let { initial_tabs = [] } = $$props;\n  let { gradio } = $$props;\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  if ($$props.initial_tabs === void 0 && $$bindings.initial_tabs && initial_tabs !== void 0)\n    $$bindings.initial_tabs(initial_tabs);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      dispatch(\"prop_change\", { selected });\n    }\n    $$rendered = `${validate_component(Tabs$1, \"Tabs\").$$render(\n      $$result,\n      {\n        visible,\n        elem_id,\n        elem_classes,\n        initial_tabs,\n        selected\n      },\n      {\n        selected: ($$value) => {\n          selected = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${slots.default ? slots.default({}) : ``}`;\n        }\n      }\n    )}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  Tabs$1 as BaseTabs,\n  TABS,\n  Index as default\n};\n"], "names": [], "mappings": ";;;AAGA,MAAM,YAAY,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACpF,EAAE,OAAO,CAAC,2RAA2R,CAAC,CAAC;AACvS,CAAC,CAAC,CAAC;AACH,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,o5FAAo5F;AAC55F,EAAE,GAAG,EAAE,CAAC,yxaAAyxa,CAAC;AAClya,CAAC,CAAC;AACG,MAAC,IAAI,GAAG,GAAG;AAChB,MAAM,IAAI,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC5E,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,mBAAmB,EAAE,gCAAgC,CAAC;AAC5D,EAAE,IAAI,aAAa,EAAE,0BAA0B,CAAC;AAChD,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,IAAI,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;AAC/B,EAAE,IAAI,YAAY,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC;AACvC,EAAE,IAAI,aAAa,GAAG,EAAE,CAAC;AACzB,EAAE,IAAI,kBAAkB,GAAG,KAAK,CAAC;AACjC,EAAE,IAAI,aAAa,CAAC;AACpB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,KAAK,CAAC,CAAC;AAClE,EAAE,0BAA0B,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC,KAAK,KAAK,aAAa,GAAG,KAAK,CAAC,CAAC;AACzF,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACtF,EAAE,gCAAgC,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC,KAAK,KAAK,mBAAmB,GAAG,KAAK,CAAC,CAAC;AAC3G,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,IAAI,yBAAyB,GAAG,KAAK,CAAC;AACxC,EAAE,IAAI,OAAO,GAAG,EAAE,CAAC;AAOnB,EAAE,UAAU,CAAC,IAAI,EAAE;AACnB,IAAI,YAAY,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK;AAClC,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;AACxB,MAAM,IAAI,aAAa,KAAK,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,WAAW,EAAE;AACrE,QAAQ,eAAe,CAAC,YAAY,EAAE,aAAa,GAAG,GAAG,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;AAC7E,QAAQ,eAAe,CAAC,kBAAkB,EAAE,mBAAmB,GAAG,KAAK,EAAE,mBAAmB,CAAC,CAAC;AAC9F,OAAO;AACP,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,IAAI,cAAc,EAAE,CAAC,GAAG,EAAE,KAAK,KAAK;AACpC,MAAM,IAAI,aAAa,KAAK,GAAG,CAAC,EAAE,EAAE;AACpC,QAAQ,eAAe,CAAC,YAAY,EAAE,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,KAAK,EAAE,aAAa,CAAC,CAAC;AAC3F,OAAO;AACP,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACzB,KAAK;AACL,IAAI,YAAY;AAChB,IAAI,kBAAkB;AACtB,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE;AAC1B,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AAC3D,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,IAAI,eAAe,IAAI,eAAe,CAAC,WAAW,IAAI,eAAe,CAAC,OAAO,IAAI,aAAa,KAAK,eAAe,CAAC,EAAE,EAAE;AAC5I,MAAM,QAAQ,GAAG,EAAE,CAAC;AACpB,MAAM,eAAe,CAAC,YAAY,EAAE,aAAa,GAAG,EAAE,EAAE,aAAa,CAAC,CAAC;AACvE,MAAM,eAAe,CAAC,kBAAkB,EAAE,mBAAmB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,mBAAmB,CAAC,CAAC;AAC1H,MAAM,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACzB,MAAM,kBAAkB,GAAG,KAAK,CAAC;AACjC,KAAK;AACL,GAAG;AACH,EAAE,eAAe,oBAAoB,GAAG;AACxC,IAAI,OAAO;AACX,GAAG;AACH,EAAE,SAAS,gCAAgC,CAAC,aAAa,EAAE;AAC3D,IAAI,IAAI,aAAa,KAAK,KAAK;AAC/B,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,aAAa,CAAC,CAAC;AAC9D,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAC7B,EAAE;AACF,IAAI,QAAQ,KAAK,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE;AACF,IAAI,oBAAoB,EAAE,CAAC;AAC3B,GAAG;AACH,EAAE,yBAAyB,GAAG,gCAAgC,CAAC,aAAa,CAAC,CAAC;AAC9E,EAAE,SAAS,GAAG,IAAI,CAAC,mBAAmB,IAAI,CAAC,GAAG,mBAAmB,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC;AAC9E,EAAE,gCAAgC,EAAE,CAAC;AACrC,EAAE,0BAA0B,EAAE,CAAC;AAC/B,EAAE,OAAO,CAAC,aAAa,EAAE;AACzB,IAAI,OAAO,GAAG,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,iBAAiB;AACtE,IAAI,CAAC,OAAO,GAAG,MAAM,GAAG,EAAE;AAC1B,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,qHAAqH,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AAC5P,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE,OAAO,GAAG,CAAC,8BAA8B,EAAE,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3I,GAAG,CAAC,CAAC,+DAA+D,EAAE,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AAC3I,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE,OAAO,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,KAAK,aAAa,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,GAAG,UAAU,GAAG,EAAE,CAAC,EAAE,aAAa,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,GAAG,SAAS,GAAG,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,KAAK,aAAa,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3d,GAAG,CAAC,CAAC,oBAAoB,EAAE;AAC3B,IAAI,8BAA8B;AAClC,IAAI,MAAM;AACV,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,aAAa,EAAE,CAAC,CAAC,CAAC,gBAAgB,EAAE;AAClF,IAAI,gBAAgB;AACpB,IAAI,yBAAyB,GAAG,wBAAwB,GAAG,EAAE;AAC7D,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,sBAAsB,EAAE,CAAC,kCAAkC,EAAE,CAAC,kBAAkB,GAAG,MAAM,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,KAAK;AACzP,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE,OAAO,GAAG,CAAC,eAAe,EAAE,CAAC,gBAAgB,EAAE,CAAC,EAAE,EAAE,KAAK,aAAa,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpK,GAAG,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAClF,CAAC,CAAC,CAAC;AACE,MAAC,MAAM,GAAG,KAAK;AACf,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC7B,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,QAAQ,CAAC,aAAa,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ;AAC/D,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,YAAY;AACpB,QAAQ,QAAQ;AAChB,OAAO;AACP,MAAM;AACN,QAAQ,QAAQ,EAAE,CAAC,OAAO,KAAK;AAC/B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}