{"version": 3, "file": "color-Byb8VBtc.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/color.js"], "sourcesContent": ["import { o as ordered_colors } from \"./client.js\";\nconst get_next_color = (index) => {\n  return ordered_colors[index % ordered_colors.length];\n};\nexport {\n  get_next_color as g\n};\n"], "names": [], "mappings": ";;AACK,MAAC,cAAc,GAAG,CAAC,KAAK,KAAK;AAClC,EAAE,OAAO,cAAc,CAAC,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;AACvD;;;;"}