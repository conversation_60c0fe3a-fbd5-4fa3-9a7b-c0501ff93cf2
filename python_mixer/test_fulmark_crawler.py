"""Test Enhanced HVAC Crawler na stronie macierzystej firmy Fulmark.pl"""

import asyncio
import json
from datetime import datetime
from pathlib import Path
from loguru import logger

# Configure logging for Fulmark test
logger.add("logs/fulmark_crawler_test.log", rotation="1 day", retention="7 days")

try:
    from krabulon.crawlers.crawl4ai_wrapper import Crawl4AIWrapper
    CRAWL4AI_AVAILABLE = True
except ImportError:
    logger.warning("Crawl4AI not available - using fallback")
    CRAWL4AI_AVAILABLE = False


async def test_fulmark_main_page():
    """Test crawling głównej strony Fulmark.pl"""
    logger.info("🏢 Testowanie głównej strony Fulmark.pl")
    
    fulmark_url = "https://fulmark.pl/"
    
    if not CRAWL4AI_AVAILABLE:
        logger.error("❌ Crawl4AI nie jest dostępne")
        return None
    
    try:
        async with Crawl4AIWrapper(enable_stealth=True, enable_image_extraction=True) as crawler:
            result = await crawler.crawl_hvac_equipment(
                url=fulmark_url,
                extraction_mode="hybrid",  # Użyj hybrid dla najlepszych rezultatów
                save_images=True,
                extract_tables=True
            )
            
            logger.info(f"✅ Crawling Fulmark.pl zakończony pomyślnie")
            logger.info(f"📊 Wyniki dla Fulmark:")
            logger.info(f"   - Sukces: {result['success']}")
            logger.info(f"   - Długość treści: {result['metadata']['content_length']} znaków")
            logger.info(f"   - Znalezione obrazy: {len(result.get('images', []))}")
            logger.info(f"   - Znalezione tabele: {len(result.get('tables', []))}")
            logger.info(f"   - Dane sprzętu: {len(result.get('equipment_data', []))}")
            logger.info(f"   - Czas crawlingu: {result['metadata']['duration_seconds']:.2f} sekund")
            
            # Zapisz wyniki Fulmark
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            result_file = Path(f"fulmark_results/fulmark_main_page_{timestamp}.json")
            result_file.parent.mkdir(exist_ok=True)
            
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 Wyniki Fulmark zapisane do {result_file}")
            
            # Pokaż próbkę treści
            if result['success'] and result.get('content'):
                content_preview = result['content'][:500] + "..." if len(result['content']) > 500 else result['content']
                logger.info(f"📄 Próbka treści Fulmark:\n{content_preview}")
            
            return result
            
    except Exception as e:
        logger.error(f"❌ Błąd podczas crawlingu Fulmark.pl: {e}")
        return None


async def test_fulmark_oferta_page():
    """Test crawling strony oferty Fulmark"""
    logger.info("🛍️ Testowanie strony oferty Fulmark")
    
    # Sprawdź różne możliwe URLe oferty
    possible_urls = [
        "https://fulmark.pl/oferta/",
        "https://fulmark.pl/klimatyzatory-lg/",
        "https://fulmark.pl/uslugi/",
        "https://fulmark.waw.pl/oferta/"
    ]
    
    if not CRAWL4AI_AVAILABLE:
        logger.error("❌ Crawl4AI nie jest dostępne")
        return None
    
    results = []
    
    async with Crawl4AIWrapper(enable_stealth=True, enable_image_extraction=True) as crawler:
        for url in possible_urls:
            try:
                logger.info(f"🔍 Sprawdzanie URL: {url}")
                
                result = await crawler.crawl_hvac_equipment(
                    url=url,
                    extraction_mode="structured",  # Szybsze dla stron oferty
                    save_images=True,
                    extract_tables=True
                )
                
                if result['success']:
                    logger.info(f"✅ Sukces dla {url}")
                    logger.info(f"   - Treść: {result['metadata']['content_length']} znaków")
                    logger.info(f"   - Obrazy: {len(result.get('images', []))}")
                    results.append(result)
                    
                    # Zapisz wynik
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    url_safe = url.replace('https://', '').replace('/', '_')
                    result_file = Path(f"fulmark_results/fulmark_{url_safe}_{timestamp}.json")
                    
                    with open(result_file, 'w', encoding='utf-8') as f:
                        json.dump(result, f, indent=2, ensure_ascii=False)
                    
                    logger.info(f"💾 Zapisano {result_file}")
                else:
                    logger.warning(f"⚠️ Nie udało się pobrać {url}: {result.get('error', 'Nieznany błąd')}")
                
                # Krótka przerwa między requestami
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"❌ Błąd dla {url}: {e}")
                continue
    
    logger.info(f"📊 Znaleziono {len(results)} działających stron oferty Fulmark")
    return results


async def analyze_fulmark_content(results):
    """Analizuj zawartość stron Fulmark"""
    logger.info("🔍 Analiza zawartości stron Fulmark")
    
    if not results:
        logger.warning("⚠️ Brak wyników do analizy")
        return
    
    # Zbierz wszystkie dane
    all_content = ""
    all_images = []
    all_equipment = []
    
    for result in results:
        if result and result.get('success'):
            all_content += result.get('content', '') + "\n"
            all_images.extend(result.get('images', []))
            all_equipment.extend(result.get('equipment_data', []))
    
    # Analiza treści
    logger.info("📊 Analiza treści Fulmark:")
    logger.info(f"   - Łączna długość treści: {len(all_content)} znaków")
    logger.info(f"   - Łączna liczba obrazów: {len(all_images)}")
    logger.info(f"   - Znalezione dane sprzętu: {len(all_equipment)}")
    
    # Szukaj kluczowych słów HVAC
    hvac_keywords = [
        'klimatyzacja', 'klimatyzator', 'lg', 'daikin', 'samsung', 'fujitsu',
        'montaż', 'serwis', 'instalacja', 'btu', 'inverter', 'split',
        'warszawa', 'piaseczno', 'autoryzowany', 'dealer'
    ]
    
    found_keywords = {}
    content_lower = all_content.lower()
    
    for keyword in hvac_keywords:
        count = content_lower.count(keyword)
        if count > 0:
            found_keywords[keyword] = count
    
    logger.info("🔑 Znalezione słowa kluczowe HVAC:")
    for keyword, count in sorted(found_keywords.items(), key=lambda x: x[1], reverse=True):
        logger.info(f"   - '{keyword}': {count} wystąpień")
    
    # Analiza obrazów
    if all_images:
        logger.info("🖼️ Analiza obrazów:")
        image_types = {}
        for img in all_images:
            if isinstance(img, dict):
                src = img.get('src', '')
                alt = img.get('alt', '')
            else:
                src = str(img)
                alt = ''
            
            # Kategoryzuj obrazy
            if any(word in src.lower() for word in ['logo', 'brand']):
                image_types['logo'] = image_types.get('logo', 0) + 1
            elif any(word in src.lower() for word in ['klimat', 'ac', 'hvac']):
                image_types['equipment'] = image_types.get('equipment', 0) + 1
            elif any(word in src.lower() for word in ['gallery', 'realizac']):
                image_types['gallery'] = image_types.get('gallery', 0) + 1
            else:
                image_types['other'] = image_types.get('other', 0) + 1
        
        for img_type, count in image_types.items():
            logger.info(f"   - {img_type}: {count} obrazów")
    
    # Stwórz podsumowanie
    summary = {
        "timestamp": datetime.now().isoformat(),
        "total_pages_crawled": len(results),
        "total_content_length": len(all_content),
        "total_images": len(all_images),
        "total_equipment_data": len(all_equipment),
        "hvac_keywords_found": found_keywords,
        "image_analysis": image_types if all_images else {},
        "crawl_success": True
    }
    
    # Zapisz podsumowanie
    summary_file = Path(f"fulmark_results/fulmark_analysis_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    logger.info(f"📋 Podsumowanie analizy zapisane do {summary_file}")
    return summary


async def run_fulmark_comprehensive_test():
    """Uruchom kompletny test crawlerów na stronie Fulmark"""
    logger.info("🚀 Rozpoczynanie kompletnego testu crawlerów Fulmark")
    logger.info("=" * 60)
    
    # Stwórz katalog na wyniki
    Path("fulmark_results").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    all_results = []
    
    # Test 1: Główna strona
    logger.info("\n📍 TEST 1: Główna strona Fulmark.pl")
    main_result = await test_fulmark_main_page()
    if main_result:
        all_results.append(main_result)
    
    # Test 2: Strony oferty
    logger.info("\n📍 TEST 2: Strony oferty Fulmark")
    offer_results = await test_fulmark_oferta_page()
    if offer_results:
        all_results.extend(offer_results)
    
    # Test 3: Analiza zawartości
    logger.info("\n📍 TEST 3: Analiza zawartości Fulmark")
    analysis = await analyze_fulmark_content(all_results)
    
    # Podsumowanie
    logger.info("\n" + "=" * 60)
    logger.info("📋 PODSUMOWANIE TESTÓW FULMARK")
    logger.info("=" * 60)
    
    successful_crawls = len([r for r in all_results if r and r.get('success')])
    total_attempts = len(all_results) if all_results else 0
    
    logger.info(f"✅ Udane crawle: {successful_crawls}")
    logger.info(f"📊 Łączne próby: {total_attempts}")
    logger.info(f"📈 Wskaźnik sukcesu: {successful_crawls/max(total_attempts, 1):.1%}")
    
    if analysis:
        logger.info(f"📄 Łączna treść: {analysis['total_content_length']} znaków")
        logger.info(f"🖼️ Łączne obrazy: {analysis['total_images']}")
        logger.info(f"🔧 Dane sprzętu: {analysis['total_equipment_data']}")
        logger.info(f"🔑 Słowa kluczowe: {len(analysis['hvac_keywords_found'])}")
    
    logger.info("\n🎉 Test crawlerów Fulmark zakończony!")
    logger.info("📁 Wyniki zapisane w katalogu: fulmark_results/")
    
    return all_results, analysis


if __name__ == "__main__":
    # Uruchom kompletny test Fulmark
    asyncio.run(run_fulmark_comprehensive_test())
