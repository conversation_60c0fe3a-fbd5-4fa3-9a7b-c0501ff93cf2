Collecting gradio
  Using cached gradio-5.31.0-py3-none-any.whl.metadata (16 kB)
Requirement already satisfied: aiofiles<25.0,>=22.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (24.1.0)
Requirement already satisfied: anyio<5.0,>=3.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (4.9.0)
Collecting fastapi<1.0,>=0.115.2 (from gradio)
  Using cached fastapi-0.115.12-py3-none-any.whl.metadata (27 kB)
Collecting ffmpy (from gradio)
  Using cached ffmpy-0.5.0-py3-none-any.whl.metadata (3.0 kB)
Collecting gradio-client==1.10.1 (from gradio)
  Using cached gradio_client-1.10.1-py3-none-any.whl.metadata (7.1 kB)
Collecting groovy~=0.1 (from gradio)
  Using cached groovy-0.1.2-py3-none-any.whl.metadata (6.1 kB)
Requirement already satisfied: httpx>=0.24.1 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (0.28.1)
Requirement already satisfied: huggingface-hub>=0.28.1 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (0.32.3)
Requirement already satisfied: jinja2<4.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (3.1.6)
Requirement already satisfied: markupsafe<4.0,>=2.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (3.0.2)
Requirement already satisfied: numpy<3.0,>=1.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (2.2.6)
Collecting orjson~=3.0 (from gradio)
  Using cached orjson-3.10.18-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (41 kB)
Requirement already satisfied: packaging in ./venv_crawler/lib/python3.12/site-packages (from gradio) (25.0)
Requirement already satisfied: pandas<3.0,>=1.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (2.2.3)
Requirement already satisfied: pillow<12.0,>=8.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (10.4.0)
Requirement already satisfied: pydantic<2.12,>=2.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (2.11.5)
Collecting pydub (from gradio)
  Using cached pydub-0.25.1-py2.py3-none-any.whl.metadata (1.4 kB)
Collecting python-multipart>=0.0.18 (from gradio)
  Using cached python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)
Requirement already satisfied: pyyaml<7.0,>=5.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (6.0.2)
Collecting ruff>=0.9.3 (from gradio)
  Downloading ruff-0.11.12-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (25 kB)
Collecting safehttpx<0.2.0,>=0.1.6 (from gradio)
  Using cached safehttpx-0.1.6-py3-none-any.whl.metadata (4.2 kB)
Collecting semantic-version~=2.0 (from gradio)
  Using cached semantic_version-2.10.0-py2.py3-none-any.whl.metadata (9.7 kB)
Collecting starlette<1.0,>=0.40.0 (from gradio)
  Using cached starlette-0.47.0-py3-none-any.whl.metadata (6.2 kB)
Collecting tomlkit<0.14.0,>=0.12.0 (from gradio)
  Using cached tomlkit-0.13.2-py3-none-any.whl.metadata (2.7 kB)
Collecting typer<1.0,>=0.12 (from gradio)
  Using cached typer-0.16.0-py3-none-any.whl.metadata (15 kB)
Requirement already satisfied: typing-extensions~=4.0 in ./venv_crawler/lib/python3.12/site-packages (from gradio) (4.13.2)
Collecting uvicorn>=0.14.0 (from gradio)
  Using cached uvicorn-0.34.2-py3-none-any.whl.metadata (6.5 kB)
Requirement already satisfied: fsspec in ./venv_crawler/lib/python3.12/site-packages (from gradio-client==1.10.1->gradio) (2025.5.1)
Collecting websockets<16.0,>=10.0 (from gradio-client==1.10.1->gradio)
  Using cached websockets-15.0.1-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (6.8 kB)
Requirement already satisfied: idna>=2.8 in ./venv_crawler/lib/python3.12/site-packages (from anyio<5.0,>=3.0->gradio) (3.10)
Requirement already satisfied: sniffio>=1.1 in ./venv_crawler/lib/python3.12/site-packages (from anyio<5.0,>=3.0->gradio) (1.3.1)
Collecting starlette<1.0,>=0.40.0 (from gradio)
  Using cached starlette-0.46.2-py3-none-any.whl.metadata (6.2 kB)
Requirement already satisfied: certifi in ./venv_crawler/lib/python3.12/site-packages (from httpx>=0.24.1->gradio) (2025.4.26)
Requirement already satisfied: httpcore==1.* in ./venv_crawler/lib/python3.12/site-packages (from httpx>=0.24.1->gradio) (1.0.9)
Requirement already satisfied: h11>=0.16 in ./venv_crawler/lib/python3.12/site-packages (from httpcore==1.*->httpx>=0.24.1->gradio) (0.16.0)
Requirement already satisfied: filelock in ./venv_crawler/lib/python3.12/site-packages (from huggingface-hub>=0.28.1->gradio) (3.18.0)
Requirement already satisfied: requests in ./venv_crawler/lib/python3.12/site-packages (from huggingface-hub>=0.28.1->gradio) (2.32.3)
Requirement already satisfied: tqdm>=4.42.1 in ./venv_crawler/lib/python3.12/site-packages (from huggingface-hub>=0.28.1->gradio) (4.67.1)
Requirement already satisfied: hf-xet<2.0.0,>=1.1.2 in ./venv_crawler/lib/python3.12/site-packages (from huggingface-hub>=0.28.1->gradio) (1.1.2)
Requirement already satisfied: python-dateutil>=2.8.2 in ./venv_crawler/lib/python3.12/site-packages (from pandas<3.0,>=1.0->gradio) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in ./venv_crawler/lib/python3.12/site-packages (from pandas<3.0,>=1.0->gradio) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in ./venv_crawler/lib/python3.12/site-packages (from pandas<3.0,>=1.0->gradio) (2025.2)
Requirement already satisfied: annotated-types>=0.6.0 in ./venv_crawler/lib/python3.12/site-packages (from pydantic<2.12,>=2.0->gradio) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in ./venv_crawler/lib/python3.12/site-packages (from pydantic<2.12,>=2.0->gradio) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in ./venv_crawler/lib/python3.12/site-packages (from pydantic<2.12,>=2.0->gradio) (0.4.1)
Requirement already satisfied: click>=8.0.0 in ./venv_crawler/lib/python3.12/site-packages (from typer<1.0,>=0.12->gradio) (8.2.1)
Collecting shellingham>=1.3.0 (from typer<1.0,>=0.12->gradio)
  Using cached shellingham-1.5.4-py2.py3-none-any.whl.metadata (3.5 kB)
Requirement already satisfied: rich>=10.11.0 in ./venv_crawler/lib/python3.12/site-packages (from typer<1.0,>=0.12->gradio) (14.0.0)
Requirement already satisfied: six>=1.5 in ./venv_crawler/lib/python3.12/site-packages (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio) (1.17.0)
Requirement already satisfied: markdown-it-py>=2.2.0 in ./venv_crawler/lib/python3.12/site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (3.0.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in ./venv_crawler/lib/python3.12/site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (2.19.1)
Requirement already satisfied: charset-normalizer<4,>=2 in ./venv_crawler/lib/python3.12/site-packages (from requests->huggingface-hub>=0.28.1->gradio) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in ./venv_crawler/lib/python3.12/site-packages (from requests->huggingface-hub>=0.28.1->gradio) (2.4.0)
Requirement already satisfied: mdurl~=0.1 in ./venv_crawler/lib/python3.12/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio) (0.1.2)
Using cached gradio-5.31.0-py3-none-any.whl (54.2 MB)
Using cached gradio_client-1.10.1-py3-none-any.whl (323 kB)
Using cached fastapi-0.115.12-py3-none-any.whl (95 kB)
Using cached groovy-0.1.2-py3-none-any.whl (14 kB)
Using cached orjson-3.10.18-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (133 kB)
Using cached python_multipart-0.0.20-py3-none-any.whl (24 kB)
Downloading ruff-0.11.12-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (11.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 11.5/11.5 MB 35.8 MB/s eta 0:00:00
Using cached safehttpx-0.1.6-py3-none-any.whl (8.7 kB)
Using cached semantic_version-2.10.0-py2.py3-none-any.whl (15 kB)
Using cached starlette-0.46.2-py3-none-any.whl (72 kB)
Using cached tomlkit-0.13.2-py3-none-any.whl (37 kB)
Using cached typer-0.16.0-py3-none-any.whl (46 kB)
Using cached uvicorn-0.34.2-py3-none-any.whl (62 kB)
Using cached ffmpy-0.5.0-py3-none-any.whl (6.0 kB)
Using cached pydub-0.25.1-py2.py3-none-any.whl (32 kB)
Using cached shellingham-1.5.4-py2.py3-none-any.whl (9.8 kB)
Using cached websockets-15.0.1-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl (182 kB)
Installing collected packages: pydub, websockets, uvicorn, tomlkit, shellingham, semantic-version, ruff, python-multipart, orjson, groovy, ffmpy, starlette, typer, safehttpx, gradio-client, fastapi, gradio
Successfully installed fastapi-0.115.12 ffmpy-0.5.0 gradio-5.31.0 gradio-client-1.10.1 groovy-0.1.2 orjson-3.10.18 pydub-0.25.1 python-multipart-0.0.20 ruff-0.11.12 safehttpx-0.1.6 semantic-version-2.10.0 shellingham-1.5.4 starlette-0.46.2 tomlkit-0.13.2 typer-0.16.0 uvicorn-0.34.2 websockets-15.0.1
